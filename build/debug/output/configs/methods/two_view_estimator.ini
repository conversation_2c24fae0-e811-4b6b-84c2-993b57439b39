[two_view_estimator]
# 性能分析说明
ProfileCommit=双视图位姿估计器参数配置
# 性能分析选项
enable_profiling=false    # 是否启用性能分析
enable_evaluator=true    # 是否启用评估
log_level=2

# 基本设置
estimator=poselib_model_estimator    # 默认使用的算法: method_LiRP, opengv_model_estimator, poselib_model_estimator, method_robustLiRP, method_povgSixPoint, opencv_two_view_estimator, barath_two_view_estimator
debug_output=false        # 是否输出调试信息, true/false

# 数据质量控制
min_num_required_pairs=6  # 进入双视图估计所需的最小匹配对数量，默认50

# 质量验证参数 (统一管控所有估计器的质量验证)
enable_quality_validation=true    # 是否启用质量验证 (对所有估计器的结果进行统一质量检查)
min_geometric_inliers=6           # 最小几何内点数量 (参考OpenMVG标准)
min_inlier_ratio=0              # 最小内点比例 (25%, 确保内点占总匹配的合理比例)

# 算法选择参数
algorithm=                # 具体算法名称，如opengv的fivept_stewenius或opencv的ESSENTIAL_RANSAC

# 统一精细优化选项
enable_refine=false       # 是否启用统一精细优化：true=根据估计器类型自动选择合适的精细优化方法，false=仅使用初始估计

# 统一精细优化说明：
# 当enable_refine=true时，系统将根据估计器类型自动选择合适的精细优化方法：
#
# 1. LiRP系列方法 (method_LiRP, method_LiRPFast, method_robustLiRP)：
#    使用PoSDK MethodTwoViewOptimizer进行精细优化
#    配置：
#      - 残差函数: ppo_opengv (6D残差向量，数值稳定且精度高)
#      - 损失函数: huber (对异常值鲁棒)
#      - 优化器: eigen_lm (高效的Levenberg-Marquardt实现)
#      - Huber阈值: 0.0016 (较严格，适合精细优化)
#      - Cauchy阈值: 0.008 (用于处理严重异常值)
#
# 2. PoseLib估计器 (poselib_model_estimator)：
#    使用PoseLib内部非线性优化 (refine_model=nonlinear)
#    自动启用Bundle Adjustment或非线性优化方法
#
# 3. 其他估计器：
#    暂不支持精细优化，enable_refine=true将被忽略并记录警告信息
#
# 优化效果：
#   - 提高位姿精度，特别适合对精度要求较高的应用场景
#   - 增强对噪声和小量异常值的鲁棒性
#   - 计算开销适中，适合实时或近实时应用
# 推荐使用场景：
#   - 高精度三维重建
#   - 机器人导航和定位
#   - 增强现实（AR）应用
#   - 双视图几何精度验证


