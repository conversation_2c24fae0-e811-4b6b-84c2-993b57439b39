[method_matches_visualizer]
# 性能分析说明
ProfileCommit=双视图匹配可视化工具       # 配置修改说明，类似git commit message

# 性能分析选项
enable_profiling=true     # 是否启用性能分析
enable_evaluator=false    # 是否启用评估器

# 输出设置
export_folder=storage/matches_visualization    # 输出文件夹路径，匹配图保存位置
enhance_outliers=true     # 是否增强外点显示：true=外点红色突出+内点多样化颜色，false=所有匹配多样化颜色

# 可视化设置
image_quality=95          # PNG图像质量 (0-100)
line_thickness=5          # 匹配线粗细
keypoint_radius=20         # 关键点显示半径
font_scale=0.7           # 文字缩放比例
line_alpha=0.7           # 匹配线透明度 (0.0-1.0)
keypoint_alpha=1       # 关键点透明度 (0.0-1.0)
enable_distributed_selection=true  # 是否启用分布式匹配点选择
enable_color_diversity=true        # 是否启用多样化颜色显示
color_mode=random      # 颜色模式：rainbow|hsv|category|random

# 输出控制
max_matches_per_image=100    # 每个图像对最多显示的匹配数量，0表示无限制
save_empty_matches=false      # 是否保存没有匹配的视图对图像

# 批处理设置
batch_mode=true           # 批处理模式：true=处理所有视图对，false=仅处理指定视图对
specific_view_i=0         # 指定视图对的第一个视图ID (仅在batch_mode=false时有效)
specific_view_j=1         # 指定视图对的第二个视图ID (仅在batch_mode=false时有效)

# 调试设置
show_statistics=true      # 是否在图像上显示统计信息
verbose_output=true       # 是否输出详细处理信息 