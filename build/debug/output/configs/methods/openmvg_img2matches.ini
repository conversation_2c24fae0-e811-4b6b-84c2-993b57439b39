[openmvg_img2matches_plugin]

# 性能分析说明
ProfileCommit=OpenMVG特征匹配插件配置

# 性能分析选项
enable_profiling=true    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估

# ======================================================
# 通用设置
# ======================================================
# OpenMVG二进制文件目录 (用户需根据实际安装路径修改)
# 基于install_openmvg.sh构建后的相对路径
openmvg_bin_folder = {root_dir}/../../dependencies/openMVG/build/Darwin-arm64-Release # for macos (构建生成的二进制文件目录)
; openmvg_bin_folder = {root_dir}/../../dependencies/openMVG/build/Linux-x86_64-Release # for linux (构建生成的二进制文件目录)
; openmvg_bin_folder = /opt/homebrew/bin # for macos (系统安装路径)
; openmvg_bin_folder = /usr/local/bin # for linux (系统安装路径)

# 工作目录 (插件运行时创建的临时目录)
work_dir = ./openmvg_work_tmp

# 是否强制重新计算所有步骤（不使用已存在的缓存文件）
force_compute = false

# 是否输出详细的调试日志 (如果为true，插件会保留work_dir供检查)
debug_output = false

# 用于OpenMVG某些并行步骤的线程数 (0表示自动选择，通常是最大可用线程数)
num_threads = 0

# 用于特征描述子缓存的最大内存（MB），0表示无限制 (OpenMVG ComputeFeatures/ComputeMatches/GeometricFilter)
cache_size = 0

# ======================================================
# SfMInit_ImageListing 设置 (openMVG_main_SfMInit_ImageListing)
# ======================================================
# 相机传感器宽度数据库文件路径。如果留空，OpenMVG将不使用数据库。
# OpenMVG默认安装路径通常是 /usr/local/share/openMVG/sensor_width_camera_database.txt
camera_sensor_db = /usr/local/share/openMVG/sensor_width_camera_database.txt

# 相机模型类型:
# 1: PINHOLE_CAMERA (针孔模型)
# 2: PINHOLE_CAMERA_RADIAL1 (针孔+1阶径向畸变)
# 3: PINHOLE_CAMERA_RADIAL3 (针孔+3阶径向畸变 - OpenMVG默认)
# 4: PINHOLE_CAMERA_BROWN (针孔+Brown T2畸变)
# 5: PINHOLE_CAMERA_FISHEYE (针孔+鱼眼畸变)
# 6: CAMERA_SPHERICAL (球面相机)
camera_model = 3

# 手动指定的相机内参矩阵 (格式: "f;0;ppx;0;f;ppy;0;0;1")
# 如果提供，将覆盖从EXIF或focal_pixels的计算。留空则自动处理。
intrinsics = ""

# 手动指定的焦距 (像素单位)。
# 如果提供了有效的K矩阵(intrinsics)，此参数将被忽略。
# 设置为 -1.0 表示不使用此参数，依赖EXIF或数据库。
focal_pixels = -1.0

# 是否将具有相同图像尺寸和焦距（如果已知）的视图的内参分组。
# 0: 每个视图拥有独立的内参。
# 1: 视图可以共享内参 (OpenMVG默认)。
group_camera_model = 1

# 是否使用图像EXIF中读取的GPS数据作为相机位姿先验。
use_pose_prior = false

# 如果use_pose_prior为true，定义GPS位姿先验的权重 (格式: "x_weight;y_weight;z_weight")。
prior_weights = "1.0;1.0;1.0"

# GPS坐标到3D场景坐标的转换方法:
# 0: ECEF (地心地固坐标系 - OpenMVG默认)。
# 1: UTM (通用横轴墨卡托投影)。
gps_to_xyz_method = 0

# ======================================================
# ComputeFeatures 设置 (openMVG_main_ComputeFeatures)
# ======================================================
# 特征描述子提取方法:
# SIFT (OpenMVG默认)
# SIFT_ANATOMY
# AKAZE_FLOAT (AKAZE配合浮点描述子)
# AKAZE_MLDB (AKAZE配合二进制描述子)
describer_method = SIFT

# 特征描述子提取的预设配置 (控制质量和速度):
# NORMAL (OpenMVG默认)
# HIGH
# ULTRA (可能会非常慢)
describer_preset = NORMAL

# 是否强制提取直立特征 (不考虑图像旋转，通常用于SIFT)。
# true对应OpenMVG的 -u 1 选项。
upright = false

# ======================================================
# ComputeMatches 设置 (openMVG_main_ComputeMatches)
# ======================================================
# 特征匹配时的距离比率阈值 (Lowe's ratio test)。
ratio = 0.8

# 最近邻匹配策略:
# AUTO (OpenMVG默认 - 根据描述子类型自动选择)
# BRUTEFORCEL2 (L2范数暴力匹配)
# BRUTEFORCEHAMMING (Hamming距离暴力匹配 - 用于二进制描述子)
# HNSWL2 (L2范数HNSW近似匹配)
# HNSWL1 (L1范数HNSW近似匹配 - 适用于量化或直方图描述子)
# HNSWHAMMING (Hamming距离HNSW近似匹配)
# CASCADEHASHINGL2 (L2范数级联哈希匹配)
# FASTCASCADEHASHINGL2 (快速L2级联哈希匹配 - 默认用于浮点描述子，但更耗内存)
nearest_matching_method = AUTO

# 预定义的图像对列表文件路径。如果留空，则进行详尽匹配 (所有可能的图像对)。
pair_list = ""

# (插件特定) 是否启用预选特征匹配。如果为true，则使用preemptive_feature_count参数。
use_preemptive = false

# 用于预选特征匹配的特征数量 (仅当use_preemptive为true时生效)。
preemptive_feature_count = 200

# ======================================================
# GeometricFilter 设置 (openMVG_main_GeometricFilter)
# ======================================================
# 用于几何验证的运动模型:
# f: FUNDAMENTAL_MATRIX (基础矩阵 - OpenMVG默认)
# e: ESSENTIAL_MATRIX (本质矩阵)
# h: HOMOGRAPHY_MATRIX (单应矩阵)
# a: ESSENTIAL_MATRIX_ANGULAR (角度参数化的本质矩阵)
# u: ESSENTIAL_MATRIX_UPRIGHT (直立角度参数化的本质矩阵)
# o: ESSENTIAL_MATRIX_ORTHO (正交本质矩阵)
geometric_model = f

# (可选) 输入的图像对列表文件路径，用于限制几何过滤的范围。
# 如果留空，则处理来自ComputeMatches的所有匹配对。
input_pairs = ""

# (可选) 输出经过几何过滤后保留的图像对列表文件路径。
output_pairs = ""

# 是否使用几何模型进行引导匹配，以改善初始匹配。
# true对应OpenMVG的 -r 1 选项。
guided_matching = false

# RANSAC等鲁棒估计算法的最大迭代次数。
max_iteration = 2048
