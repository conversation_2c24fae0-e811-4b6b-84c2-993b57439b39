[method_rotation_averaging]
# 性能分析说明
ProfileCommit=旋转平均配置       # 配置修改说明

# 日志和调试选项
enable_evaluator=false    # 是否启用评估

log_level=1              # 日志级别: 2=详细, 1=正常, 0=无

# GraphOptim工具配置 - 自动检测
# 系统会自动检测以下路径：
# - ../../dependencies/GraphOptim/build_scripted/bin
# - ../dependencies/GraphOptim/build_scripted/bin  
# - 系统PATH中的安装位置


# 旋转估计器选择: GraphOptim | Chatterjee (当前只支持这个)
rotation_estimator=GraphOptim 

# 临时文件配置
temp_dir=./temp                  # 临时文件目录
g2o_filename=relative_poses.g2o  # 输入g2o文件名 (传递给 rotation_estimator)
estimator_output_g2o=optimized_poses.g2o # rotation_estimator 输出的g2o文件名
                                 # MethodRotationAveraging 会从此文件加载全局位姿

# 优化参数 (以下参数不再通过命令行传递给您提供的 rotation_estimator)
# 它们在 rotation_estimator main.cpp 中是内部设置的。
# 如果您有其他版本的 rotation_estimator 支持这些命令行参数，则可以取消注释。
# max_iterations=100      # 最大迭代次数
# convergence_eps=1e-8    # 收敛阈值
# robust_kernel=Huber     # 鲁棒核函数类型
# kernel_width=1.0        # 核函数宽度