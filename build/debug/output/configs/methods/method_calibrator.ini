[method_calibrator]
# 性能分析说明
ProfileCommit=相机标定参数配置-支持多种标定板和相机模型

# 性能分析选项
enable_profiling=true    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估

# 通用设置
run_mode=fast          # 可选: fast [viewer暂不支持
export_model=ON       # 是否导出相机模型
export_path=storage/camera_models  # 导出路径

# 标定板参数 
pattern_type=chessboard  # 标定板类型: chessboard, circles, acircles
board_width=9           # 标定板宽度(内角点数)
board_height=6          # 标定板高度(内角点数)
square_size=25.0       # 方格尺寸(毫米)


# 相机模型设置 - 针对1280x720分辨率相机
camera_model_type=pinhole    # 可选: pinhole, fisheye, omnidirectional
distortion_model=radial_k3   # 可选: none, radial_k1, radial_k3, brown_conrady
fix_principal_point=false    # 是否固定主点
fix_aspect_ratio=false      # 是否固定纵横比
zero_tangent_dist=true     # 是否假设无切向畸变
fix_k1=false               # 是否固定k1
fix_k2=false               # 是否固定k2
fix_k3=false               # 是否固定k3

# 标定参数
min_images=3          # 最少需要的图像数
max_images=100          # 最多使用的图像数
target_rms=0.5        # 目标重投影误差(像素)

# 调试信息
save_debug_images=ON   # 是否保存调试图像
debug_image_path=storage/debug_images  # 调试图像保存路径

# 其他建议添加的参数
max_iter=50            # 最大迭代次数
eps=1e-8              # 收敛阈值

