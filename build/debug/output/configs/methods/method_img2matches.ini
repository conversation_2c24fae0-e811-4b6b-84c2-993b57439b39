[method_img2matches]
# 性能分析说明
ProfileCommit=KAZE-FLANN测试       # 配置修改说明，类似git commit message
# 性能分析选项
enable_profiling=true    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估
# 日志级别: 2=详细, 1=正常, 0=无
log_level=2 

# 运行模式: fast=快速, viewer=可视化    
run_mode=fast

# 特征输出控制
export_features=ON    # 是否输出特征文件
export_fea_path=storage/features      # 特征输出路径（留空则使用默认路径）

# 匹配结果导出配置
export_matches =ON
export_match_path =storage/matches

# ================================================  
# 添加匹配相关的配置
# 注意：匹配器类型会根据描述子类型自动调整以确保兼容性
# - 二进制描述子(ORB/BRIEF/BRISK)将自动使用BRUTEFORCE_HAMMING
# - 浮点描述子(SIFT)将自动使用BRUTEFORCE或FLANN
# ================================================    

# 匹配器类型选择    
matcher_type=FLANN    # 可选: FLANN, BF, BF_NORM_L1, BF_HAMMING
# FLANN      - 基于FLANN的快速匹配(仅适用于SIFT等浮点描述子)
# BF         - 标准暴力匹配(L2距离，适用于浮点描述子如SIFT)
# BF_NORM_L1 - L1范数暴力匹配(适用于浮点描述子)
# BF_HAMMING - 汉明距离暴力匹配(适用于二进制描述子)
#   - ORB: 二进制描述子，适用BF_HAMMING
#   - BRISK: 二进制描述子，适用BF_HAMMING
#   - AKAZE: 二进制描述子，适用BF_HAMMING
#   - SIFT: 浮点描述子，适用FLANN/BF/BF_NORM_L1
#   - KAZE: 浮点描述子，适用FLANN/BF/BF_NORM_L1
#   - FAST: 需要配合描述子使用，如ORB描述子则用BF_HAMMING
#   - AGAST: 需要配合描述子使用，如ORB描述子则用BF_HAMMING



cross_check=true     # 是否进行交叉检查
ratio_thresh=0.8     # 匹配阈值
max_matches=1000     # 最大匹配数

# 视图对选择
show_view_pair_i=0    # 第一幅图像索引
show_view_pair_j=1    # 第二幅图像索引


