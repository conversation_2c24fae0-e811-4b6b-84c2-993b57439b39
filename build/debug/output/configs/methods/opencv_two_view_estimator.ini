[opencv_two_view_estimator]
# 基本配置
ProfileCommit=OpenCV双视图位姿估计参数配置

log_level=0

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 视图对配置
view_i=0  # 源视图ID
view_j=1  # 目标视图ID

# 算法选择 (完全按照OpenCV官方API分类)
# 
# 基础矩阵算法 (Fundamental Matrix):
# - findFundamentalMat_7point: 7点法基础矩阵估计 (直接求解)
# - findFundamentalMat_8point: 8点法基础矩阵估计 (直接求解)
# - findFundamentalMat_ransac: RANSAC鲁棒基础矩阵估计
# - findFundamentalMat_lmeds: LMedS鲁棒基础矩阵估计
# - findFundamentalMat_rho: RHO快速鲁棒基础矩阵估计
#
# 本质矩阵算法 (Essential Matrix):
# - findEssentialMat_ransac: RANSAC鲁棒本质矩阵估计 ⭐ (推荐)
# - findEssentialMat_lmeds: LMedS鲁棒本质矩阵估计
#
# USAC高级算法系列 (Universal Sample Consensus):
# - findEssentialMat_usac_default: USAC默认配置
# - findEssentialMat_usac_parallel: USAC并行版本 ⭐ (多核推荐)
# - findEssentialMat_usac_fm_8pts: USAC基础矩阵8点算法
# - findEssentialMat_usac_fast: USAC快速设置 ⭐ (速度优先)
# - findEssentialMat_usac_accurate: USAC精确设置 ⭐ (精度优先)
# - findEssentialMat_usac_prosac: USAC PROSAC算法 (使用特征质量排序)
# - findEssentialMat_usac_magsac: USAC MAGSAC++算法 ⭐ (鲁棒性最强)
#
# 单应性矩阵算法 (Homography Matrix):
# - findHomography_ransac: RANSAC鲁棒单应性矩阵估计
# - findHomography_lmeds: LMedS鲁棒单应性矩阵估计  
# - findHomography_rho: RHO快速鲁棒单应性矩阵估计
#
# 算法选择建议:
# - 通用推荐: findEssentialMat_ransac (稳定可靠)
# - 精度优先: findEssentialMat_usac_accurate 
# - 速度优先: findEssentialMat_usac_fast
# - 鲁棒性优先: findEssentialMat_usac_magsac
# - 多核并行: findEssentialMat_usac_parallel
algorithm=findEssentialMat_usac_fast

# 鲁棒估计参数 (仅在鲁棒算法时生效)
ransac_threshold=1.5               # RANSAC/USAC重投影误差阈值(像素)，数值越小，要求精度越高
confidence=0.99                    # RANSAC置信度 (0.95-0.999)
max_iterations=50000                # 最大迭代次数

# 注意: 质量控制参数已移至TwoViewEstimator统一管控
# 具体参数请在two_view_estimator.ini中配置:
# - enable_quality_validation: 是否启用质量验证
# - min_geometric_inliers: 最小几何内点数量
# - min_inlier_ratio: 最小内点比例

# 算法特定参数说明:
# 
# ransac_threshold参数建议 (单位: 像素):
# - Essential Matrix: 1.0-2.0 像素
# - Fundamental Matrix: 1.0-3.0 像素
# - Homography: 3.0-5.0 像素
#
# confidence参数说明:
# - 0.95: 快速但可能不够鲁棒
# - 0.99: 平衡速度和鲁棒性 (推荐)
# - 0.999: 最大鲁棒性但较慢
#
# max_iterations参数:
# - Essential/Fundamental: 5000-10000 (复杂度较高)
# - Homography: 2000-5000 (相对简单)
# - USAC算法通常需要更多迭代: 10000-20000
#
# 质量控制说明:
# - min_geometric_inliers: 确保有足够的内点支持模型估计
# - min_inlier_ratio: 确保内点占总匹配的合理比例
# - 质量验证功能已移至TwoViewEstimator统一管控，提供更一致的验证标准
# - 这些参数可根据具体应用场景调整，建议不要低于OpenMVG标准
