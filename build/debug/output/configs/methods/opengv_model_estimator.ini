[opengv_model_estimator]
# 基本配置
ProfileCommit=OpenGV相对位姿估计参数配置

log_level=1

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 视图对配置
view_i=0  # 源视图ID
view_j=1  # 目标视图ID

# 算法选择 (完全按照OpenGV relCentral方法分类)
# 
# 直接方法 (Direct Methods):
# - twopt: 两点法 (需要先验旋转R，仅估计平移)
# - twopt_rotationOnly: 两点法 (仅估计旋转)
# - rotationOnly: 仅估计旋转 (使用Arun方法)
# - fivept_stewenius: Stewenius五点法 (推荐，默认)
# - fivept_nister: Nister五点法
# - fivept_kneip: Kneip五点法 (需要至少5个点)
# - sevenpt: 七点法 (需要至少7个点)
# - eightpt: 八点法 (需要至少8个点)
# - eigensolver: 特征值分解法 (仅估计旋转)
# - rel_nonlin_central: 中心相对位姿非线性优化
#
# RANSAC方法 (Robust Methods):
# - rotationOnly_ransac: 仅旋转RANSAC
# - fivept_stewenius_ransac: Stewenius五点法RANSAC ⭐ (推荐)
# - fivept_nister_ransac: Nister五点法RANSAC
# - sevenpt_ransac: 七点法RANSAC
# - eightpt_ransac: 八点法RANSAC
# - eigensolver_ransac: 特征值分解法RANSAC
#
# 注意：算法名称中包含"_ransac"的会自动使用RANSAC鲁棒估计
algorithm=fivept_stewenius_ransac

# 模型优化配置
# 可选优化方法:
# - none: 不进行优化 (默认)
# - eigensolver: 使用特征值分解法优化旋转
# - nonlinear: 使用非线性优化方法优化完整位姿
# - rotationOnly: 仅优化旋转部分
refine_model=none

# RANSAC参数配置 (仅在算法名称包含"_ransac"时生效)
ransac_threshold=1.8125e-07        # RANSAC阈值 (重投影误差阈值)
ransac_max_iterations=50000        # 最大迭代次数

# 注意: 质量控制参数已移至TwoViewEstimator统一管控
# 具体参数请在two_view_estimator.ini中配置:
# - enable_quality_validation: 是否启用质量验证
# - min_geometric_inliers: 最小几何内点数量 
# - min_inlier_ratio: 最小内点比例
# 这样可以为所有估计器提供统一的质量验证标准

[refine]
# 模型优化参数配置 (仅在refine_model!=none时生效)
use_weights=false              # 是否使用权重
max_iterations=100             # 非线性优化最大迭代次数
function_tolerance=1e-6        # 函数收敛容差
parameter_tolerance=1e-8       # 参数收敛容差

