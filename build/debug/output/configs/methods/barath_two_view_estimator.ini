[barath_two_view_estimator]
# ========================================================================
# Barath双视图位姿估计参数配置
# 技术说明: 本插件通过Python接口调用Barath博士开发的MAGSAC和SupeRANSAC算法
# - MAGSAC论文: "MAGSAC: marginalizing sample consensus" (CVPR 2019)
# - MAGSAC++论文: "MAGSAC++, a fast, reliable and accurate robust estimator" (CVPR 2020)  
# - SupeRANSAC论文: "SupeRANSAC: One RANSAC to Rule them All" (arXiv 2024)
# ========================================================================

# ========================================================================
# 【必须设置参数】- 这些参数必须根据具体场景配置
# ========================================================================

ProfileCommit=Barath双视图位姿估计参数配置


# ----- 视图对配置 (必须设置) -----
view_i=0  # 源视图ID - 必须根据实际视图对设置
view_j=1  # 目标视图ID - 必须根据实际视图对设置

# ----- 算法选择 (必须设置) -----
# 
# MAGSAC系列算法 (推荐用于生产环境):
# - MAGSAC: 原始MAGSAC算法 ⭐ (推荐，平衡性能和鲁棒性)
# - MAGSAC++: MAGSAC++改进版本 ⭐ (最强鲁棒性，处理高外点率)
#
# SupeRANSAC系列算法 (实验性，性能优异):
# - SUPERANSAC_DEFAULT: SupeRANSAC默认配置 ⭐ (通用推荐)
# - SUPERANSAC_FAST: SupeRANSAC快速配置 (速度优先)
# - SUPERANSAC_ACCURATE: SupeRANSAC精确配置 (精度优先)
# - SUPERANSAC_MAGSAC: SupeRANSAC+MAGSAC融合算法 ⭐ (最佳性能)
# - SUPERANSAC_PARALLEL: SupeRANSAC并行版本 (多核推荐)
#
# 【选择建议】:
# - 通用场景: MAGSAC (稳定可靠，广泛验证)
# - 高外点率: MAGSAC++ (最新技术，最强鲁棒性)
# - 需要速度: SUPERANSAC_FAST 
# - 需要精度: SUPERANSAC_ACCURATE
# - 多核环境: SUPERANSAC_PARALLEL
algorithm=MAGSAC++

# ----- Python环境配置 (可能需要设置) -----
conda_env=                             # conda环境名称 (留空使用系统Python)
                                      # 如果系统Python没有安装magsac/pysuperansac包，需要指定环境
                                      # 例如: pymagsac_py312, superansac_env等

# ========================================================================
# 【主要参数】- 影响算法性能的核心参数，建议根据数据特点调整
# ========================================================================

# ----- 鲁棒估计核心参数 -----
ransac_threshold=5                   # RANSAC重投影误差阈值(像素) ⭐ 重要参数
                                      # 数值越小，要求精度越高，但可能过于严格
                                      # 建议范围: 1.0-3.0像素，根据图像质量调整
                                      
confidence=0.99                        # RANSAC置信度 ⭐ 重要参数
                                      # 0.95: 快速但可能不够鲁棒
                                      # 0.99: 平衡速度和鲁棒性 (推荐)
                                      # 0.999: 最大鲁棒性但较慢

max_iterations=2000                    # 最大迭代次数 ⭐ 重要参数
                                      # MAGSAC系列: 1000-10000 (自适应迭代)
                                      # SupeRANSAC系列: 500-5000 (通常收敛更快)
                                      # 复杂场景可适当增加

min_iterations=50                      # 最小迭代次数
                                      # 确保算法有足够的采样机会

# ========================================================================
# 【基础性能参数】- 一般情况下使用默认值，特殊需求时调整
# ========================================================================

log_level=2                          # 日志级别 (0=关闭调试, 1=正常, 2=详细)
enable_profiling=false                # 是否启用性能分析
enable_evaluator=false                # 是否启用评估

# ========================================================================
# 【高级参数】- 专业用户根据具体算法和性能需求调整
# ========================================================================

# ----- MAGSAC性能参数 -----
core_number=1                          # 并行核心数
                                      # 1: 单核处理 (Barath默认，稳定性最好)
                                      # 2-4: 多核并行 (提升sigma-consensus速度)
                                      # 建议根据CPU核心数设置，但不要超过4

partition_num=5                        # 分区数量 - 影响sigma-consensus的速度和精度
                                      # 默认5，可选择3-10，数值越大精度越高但速度越慢

# ----- MAGSAC采样器配置 -----
# 【重要】: 不同采样器有不同的前置要求，请仔细选择

sampler_id=0                           # 采样器类型 ⭐ 影响采样策略
# 
# 【采样器选择指南】:
# 0 (Uniform): 均匀采样 - 最通用，适合大多数场景，计算开销最小
#              【前置要求】: 无特殊要求
#              【适用场景】: 通用场景，匹配点质量相对均匀
#
# 1 (PROSAC): 基于质量的采样 - 需要预先按质量排序的匹配点
#             【前置要求】: 匹配点必须按质量评分排序
#             【适用场景】: 有质量评分的特征匹配
#
# 2 (P-NAPSAC): 空间邻域感知采样 - 利用空间邻域信息
#               【前置要求】: 必须设置image_width和image_height
#               【适用场景】: 结构化场景，空间分布有意义
#               【相关参数】: pnapsac_layers, pnapsac_blend_ratio
#
# 3 (NG-RANSAC): 基于深度学习的采样 - 需要内点概率
#                【前置要求】: 需要深度学习模型提供的内点概率
#                【适用场景】: 有深度学习预测的场景
#
# 4 (AR-Sampler): 自适应重排序采样 - 推荐默认选择
#                 【前置要求】: 无特殊要求
#                 【适用场景】: 复杂外点分布的场景
#                 【相关参数】: ar_variance

# ----- 采样器相关参数 (仅在特定采样器下生效) -----

# 【仅当sampler_id=2 (P-NAPSAC)时需要设置】
image_width=3040                      # 图像宽度 - P-NAPSAC采样器必需
image_height=2014                     # 图像高度 - P-NAPSAC采样器必需
pnapsac_layers=4                       # P-NAPSAC网格层数 - 默认4层 {16,8,4,2}
pnapsac_blend_ratio=0.5                # 混合到全局采样的比例 - 默认0.5

# 【仅当sampler_id=4 (AR-Sampler)时可调整】
ar_variance=0.1                        # AR-Sampler方差参数 - 默认0.1

# ----- Bundle Adjustment 优化控制 -----
# 【用途】: Bundle Adjustment是一种非线性优化技术，用于精确化双视图位姿估计
# 【原理】: 通过最小化重投影误差来同时优化本质矩阵，提高估计精度
# 【计算开销】: BA优化需要额外的计算时间，特别是内点数量较多时
enable_bundle_adjustment=false          # 是否启用Bundle Adjustment优化 ⭐ 重要参数
                                      # true: 启用BA优化，提高精度但增加计算时间
                                      # false: 跳过BA优化，仅使用RANSAC/MAGSAC估计结果
                                      # 【建议】: 需要先安装MAGSAC库，对精度要求高的应用建议启用
                                      # 【注意】: 如果MAGSAC库未安装，即使设置为true也会跳过BA优化

ba_min_inliers=5                       # 执行BA优化所需的最小内点数
                                      # 【说明】: 内点数少于此值时跳过BA优化
                                      # 【建议】: 本质矩阵需要至少5个点，设置为6提供安全余量
                                      # 【范围】: 5-20，根据数据质量调整

ba_max_iterations=100                  # Bundle Adjustment最大迭代次数
                                      # 【说明】: 控制BA优化的收敛性和计算时间
                                      # 【建议】: 50-200，复杂场景可适当增加
                                      # 【平衡】: 迭代次数越多精度越高但耗时越长



# ----- 自适应内点选择 (高级后处理选项) -----
# 【注意】: 这是MAGSAC之后的可选后处理步骤，不是MAGSAC算法本身的一部分
enable_adaptive_inlier_selection=false # 是否启用自适应内点选择
                                      # 【用途】: 选择一个内点集合，使得重新拟合的模型最接近MAGSAC估计的模型
                                      # 【建议】: 一般情况下保持false，除非在SfM管道中有特殊需求
                                      # 【警告】: 内点数量多不一定意味着解的质量更好
                                      
adaptive_max_threshold=10.0            # 自适应选择的最大阈值 - 默认10.0像素
adaptive_min_inliers=20                # 自适应选择的最小内点数 - 默认20 

# ========================================================================
# 【参数调优建议】
# ========================================================================
#
# 【ransac_threshold调优指南】(单位: 像素):
# - 高精度相机 (如实验室标定): 0.5-1.0像素
# - 一般工业相机: 1.0-2.0像素  
# - 手机相机/网络图片: 1.5-3.0像素
# - 低质量/变形图片: 2.0-5.0像素
#
# 【confidence调优指南】:
# - 快速原型验证: 0.95 (速度优先)
# - 生产环境: 0.99 (推荐平衡点)
# - 关键任务: 0.999 (鲁棒性优先)
#
# 【max_iterations调优指南】:
# - 快速测试: 500-1000
# - 正常使用: 1000-5000 (推荐)
# - 复杂场景: 5000-20000
# - 极端情况: 20000+ (谨慎使用)
#
# 【算法选择建议】:
# - 新手用户: MAGSAC (稳定可靠)
# - 高外点率(>70%): MAGSAC++
# - 追求速度: SUPERANSAC_FAST
# - 追求精度: SUPERANSAC_ACCURATE  
# - 多核系统: SUPERANSAC_PARALLEL
#
# 【采样器选择建议】:
# - 不确定场景: sampler_id=4 (AR-Sampler)
# - 图像有明显结构: sampler_id=2 (P-NAPSAC) + 设置图像尺寸
# - 有特征质量评分: sampler_id=1 (PROSAC)
# - 追求最快速度: sampler_id=0 (Uniform)
#
# 【Bundle Adjustment建议】:
# - 高精度应用: enable_bundle_adjustment=true + ba_max_iterations=100-200
# - 实时应用: enable_bundle_adjustment=false (优先速度)
# - 平衡模式: enable_bundle_adjustment=true + ba_max_iterations=50-100
# - 低质量数据: ba_min_inliers=8-12 (提高最小内点要求)
# - 高质量数据: ba_min_inliers=5-6 + ba_convergence_threshold=1e-8
#
# ========================================================================
# 【Python环境设置说明】
# ========================================================================
#
# 方式1: 使用系统Python (conda_env留空)
# - 确保已安装: pip install magsac pysuperansac
#
# 方式2: 使用conda环境
# - conda create -n barath_env python=3.9
# - conda activate barath_env  
# - pip install magsac pysuperansac
# - 设置: conda_env=barath_env
#
# ======================================================================== 