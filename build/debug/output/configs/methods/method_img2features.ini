[method_img2features]
# 性能分析说明
ProfileCommit=特征检测器参数优化       # 配置修改说明，类似git commit message
# 性能分析选项
enable_profiling=true    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估
# 通用设置
export_features=ON     # 是否导出特征点信息, ON/OFF
export_fea_path=storage/features1      # 特征输出路径（留空则使用默认路径）

run_mode=fast          # 可选: fast, viewer
detector_type=SIFT     # 可选: SIFT, ORB, AKAZE, BRISK, KAZE, FAST, AGAST


[SIFT]
nfeatures=0            # 检测特征点数量，0表示不限制
nOctaveLayers=8        # 每组金字塔层数, 增大nOctaveLayers可以提高特征点检测的精度
contrastThreshold=0.04 # 对比度阈值, 增大contrastThreshold可以提高特征点检测的精度
edgeThreshold=10       # 边缘阈值, 增大edgeThreshold可以提高特征点检测的精度
sigma=1.6             # 高斯模糊系数, 增大sigma可以提高特征点检测的精度

[ORB]
nfeatures=1000        # 特征点数量
scaleFactor=1.2       # 金字塔比例因子
nlevels=8            # 金字塔层数
edgeThreshold=31      # 边缘阈值
firstLevel=0          # 第一层级
WTA_K=2              # 用于计算BRIEF描述子的点对数
scoreType=HARRIS_SCORE # 评分类型，固定使用HARRIS_SCORE
patchSize=31         # 特征点周围区域大小
fastThreshold=20     # FAST检测器阈值

[AKAZE]
descriptor_type=DESCRIPTOR_MLDB  # 描述子类型，固定使用DESCRIPTOR_MLDB
descriptor_size=0    # 描述子大小，固定为0
descriptor_channels=3 # 描述子通道数，固定为3
threshold=0.001      # 检测阈值
nOctaves=4          # 金字塔组数
nOctaveLayers=4     # 每组层数
diffusivity=DIFF_PM_G2 # 扩散类型，固定使用DIFF_PM_G2

[BRISK]
thresh=30           # 检测阈值
octaves=3          # 金字塔组数
patternScale=1.0   # 采样模式缩放因子

[KAZE]
extended=false      # 是否使用扩展描述子
upright=false      # 是否使用垂直模式
threshold=0.001    # 检测阈值
nOctaves=4         # 金字塔组数
nOctaveLayers=4    # 每组层数
diffusivity=DIFF_PM_G2 # 扩散类型，固定使用DIFF_PM_G2

[FAST]
threshold=10        # 检测阈值 (推荐范围: 10-40)
nonmaxSuppression=true # 是否使用非极大值抑制

[AGAST]
threshold=10        # 检测阈值 (推荐范围: 10-40)
nonmaxSuppression=true # 是否使用非极大值抑制
type=OAST_9_16     # 检测器类型，可选: AGAST_5_8, AGAST_7_12d, AGAST_7_12s, OAST_9_16