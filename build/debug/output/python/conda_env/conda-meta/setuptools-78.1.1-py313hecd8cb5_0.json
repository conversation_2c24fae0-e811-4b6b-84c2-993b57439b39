{"build": "py313hecd8cb5_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.13,<3.14.0a0", "python_abi 3.13.* *_cp313"], "extracted_package_dir": "/Users/<USER>/miniconda3/pkgs/setuptools-78.1.1-py313hecd8cb5_0", "files": ["lib/python3.13/site-packages/_distutils_hack/__init__.py", "lib/python3.13/site-packages/_distutils_hack/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/_distutils_hack/__pycache__/override.cpython-313.pyc", "lib/python3.13/site-packages/_distutils_hack/override.py", "lib/python3.13/site-packages/distutils-precedence.pth", "lib/python3.13/site-packages/pkg_resources/__init__.py", "lib/python3.13/site-packages/pkg_resources/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/api_tests.txt", "lib/python3.13/site-packages/pkg_resources/py.typed", "lib/python3.13/site-packages/pkg_resources/tests/__init__.py", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-313.pyc", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "lib/python3.13/site-packages/pkg_resources/tests/test_find_distributions.py", "lib/python3.13/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "lib/python3.13/site-packages/pkg_resources/tests/test_markers.py", "lib/python3.13/site-packages/pkg_resources/tests/test_pkg_resources.py", "lib/python3.13/site-packages/pkg_resources/tests/test_resources.py", "lib/python3.13/site-packages/pkg_resources/tests/test_working_set.py", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/PKG-INFO", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/SOURCES.txt", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/dependency_links.txt", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/entry_points.txt", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/requires.txt", "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/top_level.txt", "lib/python3.13/site-packages/setuptools/__init__.py", "lib/python3.13/site-packages/setuptools/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_core_metadata.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_entry_points.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_imp.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_importlib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_itertools.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_normalization.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_path.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_reqs.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_shutil.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/_static.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/archive_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/build_meta.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/depends.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/discovery.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/dist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/errors.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/extension.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/glob.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/installer.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/launch.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/logging.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/modified.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/monkey.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/msvc.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/namespaces.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/package_index.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/sandbox.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/unicode_utils.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/warnings.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/__pycache__/windows_support.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_core_metadata.py", "lib/python3.13/site-packages/setuptools/_distutils/__init__.py", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_log.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/core.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/debug.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/errors.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/extension.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/log.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/_log.py", "lib/python3.13/site-packages/setuptools/_distutils/_macos_compat.py", "lib/python3.13/site-packages/setuptools/_distutils/_modified.py", "lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py", "lib/python3.13/site-packages/setuptools/_distutils/archive_util.py", "lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py", "lib/python3.13/site-packages/setuptools/_distutils/cmd.py", "lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/command/_framework_compat.py", "lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py", "lib/python3.13/site-packages/setuptools/_distutils/command/bdist_dumb.py", "lib/python3.13/site-packages/setuptools/_distutils/command/bdist_rpm.py", "lib/python3.13/site-packages/setuptools/_distutils/command/build.py", "lib/python3.13/site-packages/setuptools/_distutils/command/build_clib.py", "lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py", "lib/python3.13/site-packages/setuptools/_distutils/command/build_py.py", "lib/python3.13/site-packages/setuptools/_distutils/command/build_scripts.py", "lib/python3.13/site-packages/setuptools/_distutils/command/check.py", "lib/python3.13/site-packages/setuptools/_distutils/command/clean.py", "lib/python3.13/site-packages/setuptools/_distutils/command/config.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install_data.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install_egg_info.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install_headers.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install_lib.py", "lib/python3.13/site-packages/setuptools/_distutils/command/install_scripts.py", "lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py", "lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py", "lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/unix.py", "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/zos.py", "lib/python3.13/site-packages/setuptools/_distutils/core.py", "lib/python3.13/site-packages/setuptools/_distutils/cygwinccompiler.py", "lib/python3.13/site-packages/setuptools/_distutils/debug.py", "lib/python3.13/site-packages/setuptools/_distutils/dep_util.py", "lib/python3.13/site-packages/setuptools/_distutils/dir_util.py", "lib/python3.13/site-packages/setuptools/_distutils/dist.py", "lib/python3.13/site-packages/setuptools/_distutils/errors.py", "lib/python3.13/site-packages/setuptools/_distutils/extension.py", "lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py", "lib/python3.13/site-packages/setuptools/_distutils/file_util.py", "lib/python3.13/site-packages/setuptools/_distutils/filelist.py", "lib/python3.13/site-packages/setuptools/_distutils/log.py", "lib/python3.13/site-packages/setuptools/_distutils/spawn.py", "lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/__init__.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/py39.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/support.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_archive_util.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_clib.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_ext.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_py.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_check.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_clean.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_cmd.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_core.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_dir_util.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_dist.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_extension.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_file_util.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_filelist.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_data.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_headers.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_lib.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_log.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_modified.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_sdist.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_spawn.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_text_file.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_util.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_version.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "lib/python3.13/site-packages/setuptools/_distutils/tests/unix_compat.py", "lib/python3.13/site-packages/setuptools/_distutils/text_file.py", "lib/python3.13/site-packages/setuptools/_distutils/unixccompiler.py", "lib/python3.13/site-packages/setuptools/_distutils/util.py", "lib/python3.13/site-packages/setuptools/_distutils/version.py", "lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py", "lib/python3.13/site-packages/setuptools/_distutils/zosccompiler.py", "lib/python3.13/site-packages/setuptools/_entry_points.py", "lib/python3.13/site-packages/setuptools/_imp.py", "lib/python3.13/site-packages/setuptools/_importlib.py", "lib/python3.13/site-packages/setuptools/_itertools.py", "lib/python3.13/site-packages/setuptools/_normalization.py", "lib/python3.13/site-packages/setuptools/_path.py", "lib/python3.13/site-packages/setuptools/_reqs.py", "lib/python3.13/site-packages/setuptools/_shutil.py", "lib/python3.13/site-packages/setuptools/_static.py", "lib/python3.13/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoasync.py", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autocommand.py", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/automain.py", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoparse.py", "lib/python3.13/site-packages/setuptools/_vendor/autocommand/errors.py", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/inflect/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/py38.py", "lib/python3.13/site-packages/setuptools/_vendor/inflect/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.pyi", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py", "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/metadata.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py", "lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__main__.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/android.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/api.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/macos.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/unix.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/version.py", "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/windows.py", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py", "lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py", "lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py", "lib/python3.13/site-packages/setuptools/_vendor/tomli/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_checkers.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_config.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_decorators.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_functions.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_importhook.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_memo.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_suppression.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_transformer.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_utils.py", "lib/python3.13/site-packages/setuptools/_vendor/typeguard/py.typed", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__main__.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py", "lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py", "lib/python3.13/site-packages/setuptools/archive_util.py", "lib/python3.13/site-packages/setuptools/build_meta.py", "lib/python3.13/site-packages/setuptools/cli-32.exe", "lib/python3.13/site-packages/setuptools/cli-64.exe", "lib/python3.13/site-packages/setuptools/cli-arm64.exe", "lib/python3.13/site-packages/setuptools/cli.exe", "lib/python3.13/site-packages/setuptools/command/__init__.py", "lib/python3.13/site-packages/setuptools/command/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/alias.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/build.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/build_clib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/build_ext.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/build_py.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/develop.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/dist_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/easy_install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/egg_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/install_lib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/install_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/rotate.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/saveopts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/sdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/setopt.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/__pycache__/test.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/command/_requirestxt.py", "lib/python3.13/site-packages/setuptools/command/alias.py", "lib/python3.13/site-packages/setuptools/command/bdist_egg.py", "lib/python3.13/site-packages/setuptools/command/bdist_rpm.py", "lib/python3.13/site-packages/setuptools/command/bdist_wheel.py", "lib/python3.13/site-packages/setuptools/command/build.py", "lib/python3.13/site-packages/setuptools/command/build_clib.py", "lib/python3.13/site-packages/setuptools/command/build_ext.py", "lib/python3.13/site-packages/setuptools/command/build_py.py", "lib/python3.13/site-packages/setuptools/command/develop.py", "lib/python3.13/site-packages/setuptools/command/dist_info.py", "lib/python3.13/site-packages/setuptools/command/easy_install.py", "lib/python3.13/site-packages/setuptools/command/editable_wheel.py", "lib/python3.13/site-packages/setuptools/command/egg_info.py", "lib/python3.13/site-packages/setuptools/command/install.py", "lib/python3.13/site-packages/setuptools/command/install_egg_info.py", "lib/python3.13/site-packages/setuptools/command/install_lib.py", "lib/python3.13/site-packages/setuptools/command/install_scripts.py", "lib/python3.13/site-packages/setuptools/command/launcher manifest.xml", "lib/python3.13/site-packages/setuptools/command/rotate.py", "lib/python3.13/site-packages/setuptools/command/saveopts.py", "lib/python3.13/site-packages/setuptools/command/sdist.py", "lib/python3.13/site-packages/setuptools/command/setopt.py", "lib/python3.13/site-packages/setuptools/command/test.py", "lib/python3.13/site-packages/setuptools/compat/__init__.py", "lib/python3.13/site-packages/setuptools/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/compat/__pycache__/py310.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/compat/__pycache__/py311.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/compat/__pycache__/py312.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/compat/__pycache__/py39.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/compat/py310.py", "lib/python3.13/site-packages/setuptools/compat/py311.py", "lib/python3.13/site-packages/setuptools/compat/py312.py", "lib/python3.13/site-packages/setuptools/compat/py39.py", "lib/python3.13/site-packages/setuptools/config/NOTICE", "lib/python3.13/site-packages/setuptools/config/__init__.py", "lib/python3.13/site-packages/setuptools/config/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/__pycache__/expand.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/__pycache__/setupcfg.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/NOTICE", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py", "lib/python3.13/site-packages/setuptools/config/distutils.schema.json", "lib/python3.13/site-packages/setuptools/config/expand.py", "lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py", "lib/python3.13/site-packages/setuptools/config/setupcfg.py", "lib/python3.13/site-packages/setuptools/config/setuptools.schema.json", "lib/python3.13/site-packages/setuptools/depends.py", "lib/python3.13/site-packages/setuptools/discovery.py", "lib/python3.13/site-packages/setuptools/dist.py", "lib/python3.13/site-packages/setuptools/errors.py", "lib/python3.13/site-packages/setuptools/extension.py", "lib/python3.13/site-packages/setuptools/glob.py", "lib/python3.13/site-packages/setuptools/gui-32.exe", "lib/python3.13/site-packages/setuptools/gui-64.exe", "lib/python3.13/site-packages/setuptools/gui-arm64.exe", "lib/python3.13/site-packages/setuptools/gui.exe", "lib/python3.13/site-packages/setuptools/installer.py", "lib/python3.13/site-packages/setuptools/launch.py", "lib/python3.13/site-packages/setuptools/logging.py", "lib/python3.13/site-packages/setuptools/modified.py", "lib/python3.13/site-packages/setuptools/monkey.py", "lib/python3.13/site-packages/setuptools/msvc.py", "lib/python3.13/site-packages/setuptools/namespaces.py", "lib/python3.13/site-packages/setuptools/package_index.py", "lib/python3.13/site-packages/setuptools/sandbox.py", "lib/python3.13/site-packages/setuptools/script (dev).tmpl", "lib/python3.13/site-packages/setuptools/script.tmpl", "lib/python3.13/site-packages/setuptools/tests/__init__.py", "lib/python3.13/site-packages/setuptools/tests/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/contexts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/environment.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/fixtures.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/namespaces.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/server.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_depends.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_develop.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_dist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_extern.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_glob.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_logging.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/text.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/__pycache__/textwrap.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/compat/__init__.py", "lib/python3.13/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/compat/py39.py", "lib/python3.13/site-packages/setuptools/tests/config/__init__.py", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/downloads/__init__.py", "lib/python3.13/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/config/downloads/preload.py", "lib/python3.13/site-packages/setuptools/tests/config/setupcfg_examples.txt", "lib/python3.13/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "lib/python3.13/site-packages/setuptools/tests/config/test_expand.py", "lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "lib/python3.13/site-packages/setuptools/tests/config/test_setupcfg.py", "lib/python3.13/site-packages/setuptools/tests/contexts.py", "lib/python3.13/site-packages/setuptools/tests/environment.py", "lib/python3.13/site-packages/setuptools/tests/fixtures.py", "lib/python3.13/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "lib/python3.13/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "lib/python3.13/site-packages/setuptools/tests/integration/__init__.py", "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-313.pyc", "lib/python3.13/site-packages/setuptools/tests/integration/helpers.py", "lib/python3.13/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "lib/python3.13/site-packages/setuptools/tests/mod_with_constant.py", "lib/python3.13/site-packages/setuptools/tests/namespaces.py", "lib/python3.13/site-packages/setuptools/tests/script-with-bom.py", "lib/python3.13/site-packages/setuptools/tests/server.py", "lib/python3.13/site-packages/setuptools/tests/test_archive_util.py", "lib/python3.13/site-packages/setuptools/tests/test_bdist_deprecations.py", "lib/python3.13/site-packages/setuptools/tests/test_bdist_egg.py", "lib/python3.13/site-packages/setuptools/tests/test_bdist_wheel.py", "lib/python3.13/site-packages/setuptools/tests/test_build.py", "lib/python3.13/site-packages/setuptools/tests/test_build_clib.py", "lib/python3.13/site-packages/setuptools/tests/test_build_ext.py", "lib/python3.13/site-packages/setuptools/tests/test_build_meta.py", "lib/python3.13/site-packages/setuptools/tests/test_build_py.py", "lib/python3.13/site-packages/setuptools/tests/test_config_discovery.py", "lib/python3.13/site-packages/setuptools/tests/test_core_metadata.py", "lib/python3.13/site-packages/setuptools/tests/test_depends.py", "lib/python3.13/site-packages/setuptools/tests/test_develop.py", "lib/python3.13/site-packages/setuptools/tests/test_dist.py", "lib/python3.13/site-packages/setuptools/tests/test_dist_info.py", "lib/python3.13/site-packages/setuptools/tests/test_distutils_adoption.py", "lib/python3.13/site-packages/setuptools/tests/test_easy_install.py", "lib/python3.13/site-packages/setuptools/tests/test_editable_install.py", "lib/python3.13/site-packages/setuptools/tests/test_egg_info.py", "lib/python3.13/site-packages/setuptools/tests/test_extern.py", "lib/python3.13/site-packages/setuptools/tests/test_find_packages.py", "lib/python3.13/site-packages/setuptools/tests/test_find_py_modules.py", "lib/python3.13/site-packages/setuptools/tests/test_glob.py", "lib/python3.13/site-packages/setuptools/tests/test_install_scripts.py", "lib/python3.13/site-packages/setuptools/tests/test_logging.py", "lib/python3.13/site-packages/setuptools/tests/test_manifest.py", "lib/python3.13/site-packages/setuptools/tests/test_namespaces.py", "lib/python3.13/site-packages/setuptools/tests/test_packageindex.py", "lib/python3.13/site-packages/setuptools/tests/test_sandbox.py", "lib/python3.13/site-packages/setuptools/tests/test_sdist.py", "lib/python3.13/site-packages/setuptools/tests/test_setopt.py", "lib/python3.13/site-packages/setuptools/tests/test_setuptools.py", "lib/python3.13/site-packages/setuptools/tests/test_shutil_wrapper.py", "lib/python3.13/site-packages/setuptools/tests/test_unicode_utils.py", "lib/python3.13/site-packages/setuptools/tests/test_virtualenv.py", "lib/python3.13/site-packages/setuptools/tests/test_warnings.py", "lib/python3.13/site-packages/setuptools/tests/test_wheel.py", "lib/python3.13/site-packages/setuptools/tests/test_windows_wrappers.py", "lib/python3.13/site-packages/setuptools/tests/text.py", "lib/python3.13/site-packages/setuptools/tests/textwrap.py", "lib/python3.13/site-packages/setuptools/unicode_utils.py", "lib/python3.13/site-packages/setuptools/version.py", "lib/python3.13/site-packages/setuptools/warnings.py", "lib/python3.13/site-packages/setuptools/wheel.py", "lib/python3.13/site-packages/setuptools/windows_support.py"], "fn": "setuptools-78.1.1-py313hecd8cb5_0.conda", "license": "MIT", "link": {"source": "/Users/<USER>/miniconda3/pkgs/setuptools-78.1.1-py313hecd8cb5_0", "type": 1}, "md5": "019b1ebad1dccbbfdee9d1567c79be31", "name": "setuptools", "package_tarball_full_path": "/Users/<USER>/miniconda3/pkgs/setuptools-78.1.1-py313hecd8cb5_0.conda", "paths_data": {"paths": [{"_path": "lib/python3.13/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "lib/python3.13/site-packages/_distutils_hack/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "a18a71dd9a0c7e8a781d486588c6f26bd8b9ecdf684d8d423d8dbf8d41068538", "sha256_in_prefix": "a18a71dd9a0c7e8a781d486588c6f26bd8b9ecdf684d8d423d8dbf8d41068538", "size_in_bytes": 10772}, {"_path": "lib/python3.13/site-packages/_distutils_hack/__pycache__/override.cpython-313.pyc", "path_type": "hardlink", "sha256": "4670717af54f3b829cc5e5675adb3234690f00beafb3141d675899736f954e60", "sha256_in_prefix": "4670717af54f3b829cc5e5675adb3234690f00beafb3141d675899736f954e60", "size_in_bytes": 254}, {"_path": "lib/python3.13/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "lib/python3.13/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "sha256_in_prefix": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "size_in_bytes": 151}, {"_path": "lib/python3.13/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "sha256_in_prefix": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "size_in_bytes": 126203}, {"_path": "lib/python3.13/site-packages/pkg_resources/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "86ceab83f569ecf111d7b4cf6e2e8deb82a27fc1a96e37c114cf13536462001e", "sha256_in_prefix": "86ceab83f569ecf111d7b4cf6e2e8deb82a27fc1a96e37c114cf13536462001e", "size_in_bytes": 164756}, {"_path": "lib/python3.13/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "lib/python3.13/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "7bf33e5fa78fefa1b8e3fc141095330f4605f3cfca917e8bdf57ad6228b96d9d", "sha256_in_prefix": "7bf33e5fa78fefa1b8e3fc141095330f4605f3cfca917e8bdf57ad6228b96d9d", "size_in_bytes": 157}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-313.pyc", "path_type": "hardlink", "sha256": "084ff51155544385264fe8a3493093ca59ed2f09d2f29f1603360aa628db5db6", "sha256_in_prefix": "084ff51155544385264fe8a3493093ca59ed2f09d2f29f1603360aa628db5db6", "size_in_bytes": 3372}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-313.pyc", "path_type": "hardlink", "sha256": "f2d243d58238028a52e36d909e27b6be9852e90d55e00b2cc8b1fd6c63ddc8cb", "sha256_in_prefix": "f2d243d58238028a52e36d909e27b6be9852e90d55e00b2cc8b1fd6c63ddc8cb", "size_in_bytes": 1927}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-313.pyc", "path_type": "hardlink", "sha256": "54dc292c674856c935a6e49cd1cc2ccd16caea35722397c9483635e88f4e28a6", "sha256_in_prefix": "54dc292c674856c935a6e49cd1cc2ccd16caea35722397c9483635e88f4e28a6", "size_in_bytes": 588}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-313.pyc", "path_type": "hardlink", "sha256": "dbc6e7057fdec306d8c9a106fbae3176ce0d10e00111804e3ca470a2f71c9bdd", "sha256_in_prefix": "dbc6e7057fdec306d8c9a106fbae3176ce0d10e00111804e3ca470a2f71c9bdd", "size_in_bytes": 25318}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-313.pyc", "path_type": "hardlink", "sha256": "c2926c0ffa5b9538940e8f3a29826dfe89c6429a56c5021ff24e401c44a746fa", "sha256_in_prefix": "c2926c0ffa5b9538940e8f3a29826dfe89c6429a56c5021ff24e401c44a746fa", "size_in_bytes": 47869}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-313.pyc", "path_type": "hardlink", "sha256": "91c6635f49a65e45ec3bd951ccdb5b1548764201accf4e235cc1cee31bffc137", "sha256_in_prefix": "91c6635f49a65e45ec3bd951ccdb5b1548764201accf4e235cc1cee31bffc137", "size_in_bytes": 10609}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-313.pyc", "path_type": "hardlink", "sha256": "7cbbac049da0abea46936d3abfa201ea3bc5bb7117f1f2d494fc57774c1cdf0f", "sha256_in_prefix": "7cbbac049da0abea46936d3abfa201ea3bc5bb7117f1f2d494fc57774c1cdf0f", "size_in_bytes": 323}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "lib/python3.13/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "93fd6bfe3c7c9b68c73c6a839293199cb43f475ac7c76f5ffd32e02f2fe45ce7", "sha256_in_prefix": "93fd6bfe3c7c9b68c73c6a839293199cb43f475ac7c76f5ffd32e02f2fe45ce7", "size_in_bytes": 6548}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "sha256_in_prefix": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "size_in_bytes": 24294}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "lib/python3.13/site-packages/setuptools-78.1.1-py3.13.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "lib/python3.13/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "2e0d4036ae3a6e0c75b8d6b39ec5595cd6959b084a63ac92fb2ddcfe8d983c45", "sha256_in_prefix": "2e0d4036ae3a6e0c75b8d6b39ec5595cd6959b084a63ac92fb2ddcfe8d983c45", "size_in_bytes": 13669}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_core_metadata.cpython-313.pyc", "path_type": "hardlink", "sha256": "49a920017f8614e6f51e671b3fca85511163c077f785144e92415762f6f9d543", "sha256_in_prefix": "49a920017f8614e6f51e671b3fca85511163c077f785144e92415762f6f9d543", "size_in_bytes": 15102}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_entry_points.cpython-313.pyc", "path_type": "hardlink", "sha256": "49d3be523e4535b981984a38893596f033e223dbab593b33a8ede2d80cbcd206", "sha256_in_prefix": "49d3be523e4535b981984a38893596f033e223dbab593b33a8ede2d80cbcd206", "size_in_bytes": 4578}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_imp.cpython-313.pyc", "path_type": "hardlink", "sha256": "fd0f8b75040e77ada6c870d13d32fc92af4202180ea8ef0f2443acb6333e2fe2", "sha256_in_prefix": "fd0f8b75040e77ada6c870d13d32fc92af4202180ea8ef0f2443acb6333e2fe2", "size_in_bytes": 3689}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_importlib.cpython-313.pyc", "path_type": "hardlink", "sha256": "2f58bfaedbcf4d0dd9f263a43e790ac1ad118e03e3baae5b464e7b8cdd7ae767", "sha256_in_prefix": "2f58bfaedbcf4d0dd9f263a43e790ac1ad118e03e3baae5b464e7b8cdd7ae767", "size_in_bytes": 367}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_itertools.cpython-313.pyc", "path_type": "hardlink", "sha256": "a692e87104df48d020ec8cbb76dab9d5fdb379dbb3d0843618afc84fe45ca49d", "sha256_in_prefix": "a692e87104df48d020ec8cbb76dab9d5fdb379dbb3d0843618afc84fe45ca49d", "size_in_bytes": 991}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_normalization.cpython-313.pyc", "path_type": "hardlink", "sha256": "d492374b2be95229747aad99acf3290b194b2fe594c9831235347b0e1538070f", "sha256_in_prefix": "d492374b2be95229747aad99acf3290b194b2fe594c9831235347b0e1538070f", "size_in_bytes": 7178}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_path.cpython-313.pyc", "path_type": "hardlink", "sha256": "41fed1ec00ab08ece5fd80e335b5a74222c0240327299d3dcccac0145ea2bd78", "sha256_in_prefix": "41fed1ec00ab08ece5fd80e335b5a74222c0240327299d3dcccac0145ea2bd78", "size_in_bytes": 4052}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_reqs.cpython-313.pyc", "path_type": "hardlink", "sha256": "0351b0921e981705da748bd63f9743413f9936ef9f68b77a6fff1bfc33518d35", "sha256_in_prefix": "0351b0921e981705da748bd63f9743413f9936ef9f68b77a6fff1bfc33518d35", "size_in_bytes": 1933}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_shutil.cpython-313.pyc", "path_type": "hardlink", "sha256": "2525c30a1e036f9d89436a002481b25b99e1bbe9c2ed9e7789915ef7ae926cbc", "sha256_in_prefix": "2525c30a1e036f9d89436a002481b25b99e1bbe9c2ed9e7789915ef7ae926cbc", "size_in_bytes": 2327}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/_static.cpython-313.pyc", "path_type": "hardlink", "sha256": "bd33d6053bbe1c13dd6a64cf10391f09e8902fa2bd6d731cc5f5fcde332819ef", "sha256_in_prefix": "bd33d6053bbe1c13dd6a64cf10391f09e8902fa2bd6d731cc5f5fcde332819ef", "size_in_bytes": 5932}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/archive_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "fec276c6b1f72d4bb186de8b5beaa98b92a369e4cf0b08113aa7b7e6288a02fc", "sha256_in_prefix": "fec276c6b1f72d4bb186de8b5beaa98b92a369e4cf0b08113aa7b7e6288a02fc", "size_in_bytes": 9386}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/build_meta.cpython-313.pyc", "path_type": "hardlink", "sha256": "af94039ac595907f0881d36df8a40070f5af822e347f7947aa3a8e46a154ca55", "sha256_in_prefix": "af94039ac595907f0881d36df8a40070f5af822e347f7947aa3a8e46a154ca55", "size_in_bytes": 24963}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/depends.cpython-313.pyc", "path_type": "hardlink", "sha256": "e1de5b379e34c7eb939caae10c8b228cffa0d877c7161dc51cf98c71ee8ca788", "sha256_in_prefix": "e1de5b379e34c7eb939caae10c8b228cffa0d877c7161dc51cf98c71ee8ca788", "size_in_bytes": 7570}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/discovery.cpython-313.pyc", "path_type": "hardlink", "sha256": "75d9e0697498548712d3149eb37ca2ccaf24169ea22a0709585b387f3c6fcba2", "sha256_in_prefix": "75d9e0697498548712d3149eb37ca2ccaf24169ea22a0709585b387f3c6fcba2", "size_in_bytes": 28992}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/dist.cpython-313.pyc", "path_type": "hardlink", "sha256": "437930c70efae1eaf83bcab39f20053af470c51662e9ef4491aba4c5cdbfd9ba", "sha256_in_prefix": "437930c70efae1eaf83bcab39f20053af470c51662e9ef4491aba4c5cdbfd9ba", "size_in_bytes": 52447}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/errors.cpython-313.pyc", "path_type": "hardlink", "sha256": "318b124234fc691c817703df1a1038f160923471799b9d17b7372e29db745ee5", "sha256_in_prefix": "318b124234fc691c817703df1a1038f160923471799b9d17b7372e29db745ee5", "size_in_bytes": 3505}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/extension.cpython-313.pyc", "path_type": "hardlink", "sha256": "5d08913300f687beb4799282d01af62f8c46b718c57cc88abf2baa64ac0caa7b", "sha256_in_prefix": "5d08913300f687beb4799282d01af62f8c46b718c57cc88abf2baa64ac0caa7b", "size_in_bytes": 6676}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/glob.cpython-313.pyc", "path_type": "hardlink", "sha256": "71bbac1ef7bfa39c3805a4702fc157565eb46e566b65cc82dbf64cf6fcbc6c27", "sha256_in_prefix": "71bbac1ef7bfa39c3805a4702fc157565eb46e566b65cc82dbf64cf6fcbc6c27", "size_in_bytes": 7622}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/installer.cpython-313.pyc", "path_type": "hardlink", "sha256": "7c2d6d2314795195808bb98e03698596f9ef505a6c0495e29355814f7722b020", "sha256_in_prefix": "7c2d6d2314795195808bb98e03698596f9ef505a6c0495e29355814f7722b020", "size_in_bytes": 6711}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/launch.cpython-313.pyc", "path_type": "hardlink", "sha256": "d86317b9a440077039c88c193c75bfa61241bce796c8ea6c15307bf9fc28cd64", "sha256_in_prefix": "d86317b9a440077039c88c193c75bfa61241bce796c8ea6c15307bf9fc28cd64", "size_in_bytes": 1270}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/logging.cpython-313.pyc", "path_type": "hardlink", "sha256": "9329b6e6f1a0d26463d4daeb513313044f59cb239ef746139725de9d31936495", "sha256_in_prefix": "9329b6e6f1a0d26463d4daeb513313044f59cb239ef746139725de9d31936495", "size_in_bytes": 2062}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/modified.cpython-313.pyc", "path_type": "hardlink", "sha256": "b6c77e75ee2924e23de1dd502f7a251682eca6aa6dd98708063147d8b45a5a56", "sha256_in_prefix": "b6c77e75ee2924e23de1dd502f7a251682eca6aa6dd98708063147d8b45a5a56", "size_in_bytes": 466}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/monkey.cpython-313.pyc", "path_type": "hardlink", "sha256": "68f2a84220ace00b3ee86895feb4d2b38c356365a936c1fb341fb48c094bc6d1", "sha256_in_prefix": "68f2a84220ace00b3ee86895feb4d2b38c356365a936c1fb341fb48c094bc6d1", "size_in_bytes": 5176}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/msvc.cpython-313.pyc", "path_type": "hardlink", "sha256": "23dd051885cb4bba3c1352f5144c0af66222fe0f3240b8a7dab453cd180ce6ce", "sha256_in_prefix": "23dd051885cb4bba3c1352f5144c0af66222fe0f3240b8a7dab453cd180ce6ce", "size_in_bytes": 55834}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/namespaces.cpython-313.pyc", "path_type": "hardlink", "sha256": "65e87c660342b9eba11942ac6fa88994e63846ba701df8526899c79d13dfe67b", "sha256_in_prefix": "65e87c660342b9eba11942ac6fa88994e63846ba701df8526899c79d13dfe67b", "size_in_bytes": 5382}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/package_index.cpython-313.pyc", "path_type": "hardlink", "sha256": "e955f22483dba3cf91afe65eb6b8a7d6d81b2166cdef6bbfc7fcb74d77e75cb5", "sha256_in_prefix": "e955f22483dba3cf91afe65eb6b8a7d6d81b2166cdef6bbfc7fcb74d77e75cb5", "size_in_bytes": 56140}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/sandbox.cpython-313.pyc", "path_type": "hardlink", "sha256": "09f56f0dbe475383a6ddc7612ec05c64f71f01acd8830936a32a3b5c8590dfd4", "sha256_in_prefix": "09f56f0dbe475383a6ddc7612ec05c64f71f01acd8830936a32a3b5c8590dfd4", "size_in_bytes": 24439}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/unicode_utils.cpython-313.pyc", "path_type": "hardlink", "sha256": "f534f5d8465ab7ddfdd89334c550b196336ec931777d8dcbcdc4ca3b6745c71f", "sha256_in_prefix": "f534f5d8465ab7ddfdd89334c550b196336ec931777d8dcbcdc4ca3b6745c71f", "size_in_bytes": 4321}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/version.cpython-313.pyc", "path_type": "hardlink", "sha256": "e78a8c3f5e53055a85cfc389fe9454c7536d76007117ebbf627cad2214534f85", "sha256_in_prefix": "e78a8c3f5e53055a85cfc389fe9454c7536d76007117ebbf627cad2214534f85", "size_in_bytes": 397}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/warnings.cpython-313.pyc", "path_type": "hardlink", "sha256": "536287f03032f73f39ac625d0e0cdb738c41d474f5960c1513429c0c7b0244ad", "sha256_in_prefix": "536287f03032f73f39ac625d0e0cdb738c41d474f5960c1513429c0c7b0244ad", "size_in_bytes": 5375}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "341d82217330ad9feb50db34722e50f4521d262e4920eb18f38b3759f13a7b75", "sha256_in_prefix": "341d82217330ad9feb50db34722e50f4521d262e4920eb18f38b3759f13a7b75", "size_in_bytes": 13532}, {"_path": "lib/python3.13/site-packages/setuptools/__pycache__/windows_support.cpython-313.pyc", "path_type": "hardlink", "sha256": "27ee29fcfbc6f770a32b7c06a33c5553b842082171bc75c7bfb4881d36f6535c", "sha256_in_prefix": "27ee29fcfbc6f770a32b7c06a33c5553b842082171bc75c7bfb4881d36f6535c", "size_in_bytes": 1419}, {"_path": "lib/python3.13/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "cd1821b3c06b1e22d748844ca355c51a39248bfd668c8fe6cc5490ab364c7650", "sha256_in_prefix": "cd1821b3c06b1e22d748844ca355c51a39248bfd668c8fe6cc5490ab364c7650", "size_in_bytes": 495}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_log.cpython-313.pyc", "path_type": "hardlink", "sha256": "a766446de067e7b0ea20fabf7b05d9f9405a3c4c73838c780334ebbbe6359629", "sha256_in_prefix": "a766446de067e7b0ea20fabf7b05d9f9405a3c4c73838c780334ebbbe6359629", "size_in_bytes": 241}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-313.pyc", "path_type": "hardlink", "sha256": "82c9fc68da3c9996cc7c033b8939bbbf5b7bd7fcc54c6c3465305094c856cc97", "sha256_in_prefix": "82c9fc68da3c9996cc7c033b8939bbbf5b7bd7fcc54c6c3465305094c856cc97", "size_in_bytes": 532}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-313.pyc", "path_type": "hardlink", "sha256": "5046bb7140f376ed9ceac7d35e7f1c2864d05fbeb60b5a82b1e8bdcf1ec09a36", "sha256_in_prefix": "5046bb7140f376ed9ceac7d35e7f1c2864d05fbeb60b5a82b1e8bdcf1ec09a36", "size_in_bytes": 4743}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-313.pyc", "path_type": "hardlink", "sha256": "19e4d103176b5089df2b4814f44818f5bb28e3114246f5b9688ed7d98eea4eeb", "sha256_in_prefix": "19e4d103176b5089df2b4814f44818f5bb28e3114246f5b9688ed7d98eea4eeb", "size_in_bytes": 694}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "770673e45645e276f781f1d6e0f9f7f3a9524b8a1469a55329c3be8508310cfc", "sha256_in_prefix": "770673e45645e276f781f1d6e0f9f7f3a9524b8a1469a55329c3be8508310cfc", "size_in_bytes": 10766}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-313.pyc", "path_type": "hardlink", "sha256": "4f3a2ab8e796b229f64f1587e0badbce7adaff3f998d23774ac0520975ef302a", "sha256_in_prefix": "4f3a2ab8e796b229f64f1587e0badbce7adaff3f998d23774ac0520975ef302a", "size_in_bytes": 659}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-313.pyc", "path_type": "hardlink", "sha256": "8446d25171275b29ea293a74cd593551e47d301bd6f2e42b13cca5a1e7ba7a3f", "sha256_in_prefix": "8446d25171275b29ea293a74cd593551e47d301bd6f2e42b13cca5a1e7ba7a3f", "size_in_bytes": 22093}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/core.cpython-313.pyc", "path_type": "hardlink", "sha256": "464e1ce2dee115e4e08d6a1a01165ab2836ee2fe307f6188c27f9ab5c431685f", "sha256_in_prefix": "464e1ce2dee115e4e08d6a1a01165ab2836ee2fe307f6188c27f9ab5c431685f", "size_in_bytes": 8965}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-313.pyc", "path_type": "hardlink", "sha256": "d3843ede120ea8440c4c8cf5e69f5b0ca750e76a15c70f0d85dbaa1c920d0ea0", "sha256_in_prefix": "d3843ede120ea8440c4c8cf5e69f5b0ca750e76a15c70f0d85dbaa1c920d0ea0", "size_in_bytes": 623}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/debug.cpython-313.pyc", "path_type": "hardlink", "sha256": "4085841d3bc92af429733cfcddb0cd00c33e9cd0a1bf6e35002009b84937cea8", "sha256_in_prefix": "4085841d3bc92af429733cfcddb0cd00c33e9cd0a1bf6e35002009b84937cea8", "size_in_bytes": 286}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "8be7365d120fe4deb2018440cc4955446711a5c91336efc4375e6febdb76b837", "sha256_in_prefix": "8be7365d120fe4deb2018440cc4955446711a5c91336efc4375e6febdb76b837", "size_in_bytes": 680}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "35711de1a3f8e92ad44ba791e223d926cdd5ff57e2244a56454aa5d552f1b2b2", "sha256_in_prefix": "35711de1a3f8e92ad44ba791e223d926cdd5ff57e2244a56454aa5d552f1b2b2", "size_in_bytes": 10372}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/dist.cpython-313.pyc", "path_type": "hardlink", "sha256": "ca0ed4acb4171566b96eb6c44752f1049e490ebd92fb080ffe1624cc223f0b40", "sha256_in_prefix": "ca0ed4acb4171566b96eb6c44752f1049e490ebd92fb080ffe1624cc223f0b40", "size_in_bytes": 56604}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/errors.cpython-313.pyc", "path_type": "hardlink", "sha256": "9732114e90553292c085ca01ab769aba274e3c8bc82cddcc89efa18bb6987073", "sha256_in_prefix": "9732114e90553292c085ca01ab769aba274e3c8bc82cddcc89efa18bb6987073", "size_in_bytes": 4701}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/extension.cpython-313.pyc", "path_type": "hardlink", "sha256": "d0468828f5a5223f2f8d0f635120d5f7c8fcdd33263fd1a1c3d9d66d43019bd4", "sha256_in_prefix": "d0468828f5a5223f2f8d0f635120d5f7c8fcdd33263fd1a1c3d9d66d43019bd4", "size_in_bytes": 10338}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-313.pyc", "path_type": "hardlink", "sha256": "e228d01ca7268cd30261023ee4171731c35513aaa99b21a68074259bc1bc26d7", "sha256_in_prefix": "e228d01ca7268cd30261023ee4171731c35513aaa99b21a68074259bc1bc26d7", "size_in_bytes": 15894}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "4844e659e9d68227e229fd3906ea19f7157c0baad8b10298a271a3491fb80d0d", "sha256_in_prefix": "4844e659e9d68227e229fd3906ea19f7157c0baad8b10298a271a3491fb80d0d", "size_in_bytes": 9602}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-313.pyc", "path_type": "hardlink", "sha256": "3c4199dfbd6a83800f460be7f5ffe6d63aee0eb8c1a15c52cb5f34d9636290fc", "sha256_in_prefix": "3c4199dfbd6a83800f460be7f5ffe6d63aee0eb8c1a15c52cb5f34d9636290fc", "size_in_bytes": 17799}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/log.cpython-313.pyc", "path_type": "hardlink", "sha256": "667b6362f5730de0c7382169920cb82c61f934449448d05c13c649eecb44a69b", "sha256_in_prefix": "667b6362f5730de0c7382169920cb82c61f934449448d05c13c649eecb44a69b", "size_in_bytes": 2637}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-313.pyc", "path_type": "hardlink", "sha256": "4bc6491432c5a05dd746241857742f887489a6a6d010f2b48090e3205fde3920", "sha256_in_prefix": "4bc6491432c5a05dd746241857742f887489a6a6d010f2b48090e3205fde3920", "size_in_bytes": 5655}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-313.pyc", "path_type": "hardlink", "sha256": "a6b401afaad314404da9a2b43147016f1d3d24efd31b01f8e4dd3f39b4135a4a", "sha256_in_prefix": "a6b401afaad314404da9a2b43147016f1d3d24efd31b01f8e4dd3f39b4135a4a", "size_in_bytes": 23262}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-313.pyc", "path_type": "hardlink", "sha256": "97284a68c577d31b42e5f56786bf2617062d3862bcbc13b54eb0204a71d3f96d", "sha256_in_prefix": "97284a68c577d31b42e5f56786bf2617062d3862bcbc13b54eb0204a71d3f96d", "size_in_bytes": 10666}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-313.pyc", "path_type": "hardlink", "sha256": "0a205ffd5a3567f73c64f8ca294d4d49f66ad3d27f78d69e6b88fe5ca4c00c88", "sha256_in_prefix": "0a205ffd5a3567f73c64f8ca294d4d49f66ad3d27f78d69e6b88fe5ca4c00c88", "size_in_bytes": 381}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/util.cpython-313.pyc", "path_type": "hardlink", "sha256": "30245b9784251f7dc595ea7da414f5524af7110abb66c93e10d93788c04e0711", "sha256_in_prefix": "30245b9784251f7dc595ea7da414f5524af7110abb66c93e10d93788c04e0711", "size_in_bytes": 19383}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/version.cpython-313.pyc", "path_type": "hardlink", "sha256": "783083a63e302b44bee422ac873ab55f15f60d3b4af6d054cc6023ad44dda686", "sha256_in_prefix": "783083a63e302b44bee422ac873ab55f15f60d3b4af6d054cc6023ad44dda686", "size_in_bytes": 10603}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-313.pyc", "path_type": "hardlink", "sha256": "3b81b1431832bd34987bce6184de64f58c9f6e1329f240d04e3dcc22592ba6dd", "sha256_in_prefix": "3b81b1431832bd34987bce6184de64f58c9f6e1329f240d04e3dcc22592ba6dd", "size_in_bytes": 6821}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-313.pyc", "path_type": "hardlink", "sha256": "d2544a311363ff704410459f4d847038cd0b05ea718ae48bc5cd0a5208ae6ae8", "sha256_in_prefix": "d2544a311363ff704410459f4d847038cd0b05ea718ae48bc5cd0a5208ae6ae8", "size_in_bytes": 262}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "dbd9e001fc9f1226e97c6f4bd732b0da01dfc1661bbd41771fbb130d4a2cccb1", "sha256_in_prefix": "dbd9e001fc9f1226e97c6f4bd732b0da01dfc1661bbd41771fbb130d4a2cccb1", "size_in_bytes": 478}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-313.pyc", "path_type": "hardlink", "sha256": "0ca45ec583aafbb9bfda866977ed6c8c661afc66179eaa3ccc5f50caf1e34db1", "sha256_in_prefix": "0ca45ec583aafbb9bfda866977ed6c8c661afc66179eaa3ccc5f50caf1e34db1", "size_in_bytes": 2666}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "dabee76e89187e71d98d1ad853f69dc76600bccb6470da6128946fd33f3d6a11", "sha256_in_prefix": "dabee76e89187e71d98d1ad853f69dc76600bccb6470da6128946fd33f3d6a11", "size_in_bytes": 6584}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-313.pyc", "path_type": "hardlink", "sha256": "a98534987d93af76184838838767cc4404ccd0321a90e11d195341e866692ba5", "sha256_in_prefix": "a98534987d93af76184838838767cc4404ccd0321a90e11d195341e866692ba5", "size_in_bytes": 5699}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-313.pyc", "path_type": "hardlink", "sha256": "83cffaeac361f5d960d93233c7035d047d4c57de5ad2d9f62e1e09aef1b497f0", "sha256_in_prefix": "83cffaeac361f5d960d93233c7035d047d4c57de5ad2d9f62e1e09aef1b497f0", "size_in_bytes": 22379}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-313.pyc", "path_type": "hardlink", "sha256": "1ebe49b722ffb2f3ca1d042d97b1ba1bf1ecd8588d6de3cac35e4c031c632627", "sha256_in_prefix": "1ebe49b722ffb2f3ca1d042d97b1ba1bf1ecd8588d6de3cac35e4c031c632627", "size_in_bytes": 6444}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-313.pyc", "path_type": "hardlink", "sha256": "1fd8d57cf061f0ccbe0b4621ea03546df957c96eb685b7eba73ca7e926d748f8", "sha256_in_prefix": "1fd8d57cf061f0ccbe0b4621ea03546df957c96eb685b7eba73ca7e926d748f8", "size_in_bytes": 7749}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-313.pyc", "path_type": "hardlink", "sha256": "956d86bc0a764c2ec06915bb11cdfd647d41e56591ea2332a64fb8eb9f21842f", "sha256_in_prefix": "956d86bc0a764c2ec06915bb11cdfd647d41e56591ea2332a64fb8eb9f21842f", "size_in_bytes": 30746}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-313.pyc", "path_type": "hardlink", "sha256": "1e398f52f2f4952ed582b1ea7120d685af026dbb34265627565477faa31ccd42", "sha256_in_prefix": "1e398f52f2f4952ed582b1ea7120d685af026dbb34265627565477faa31ccd42", "size_in_bytes": 16473}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "bd766d8b2d4c8f24cc17ec1ab7611d4b0973357389abc056ca421dc6210cd62d", "sha256_in_prefix": "bd766d8b2d4c8f24cc17ec1ab7611d4b0973357389abc056ca421dc6210cd62d", "size_in_bytes": 6860}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-313.pyc", "path_type": "hardlink", "sha256": "00e0fbb174c26faacb1c8939ffed8545ae48b92aad94a746ccd3f5a44c02578a", "sha256_in_prefix": "00e0fbb174c26faacb1c8939ffed8545ae48b92aad94a746ccd3f5a44c02578a", "size_in_bytes": 7260}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-313.pyc", "path_type": "hardlink", "sha256": "23bd770df3f858cee4e6d31f48cf30bcff2ea7a326cc91dcd33b08d247c81184", "sha256_in_prefix": "23bd770df3f858cee4e6d31f48cf30bcff2ea7a326cc91dcd33b08d247c81184", "size_in_bytes": 3231}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-313.pyc", "path_type": "hardlink", "sha256": "0a13fd64bbaf482ca3a4e8fb366a544ea46f86b2d737e9ef06b04aad009144c9", "sha256_in_prefix": "0a13fd64bbaf482ca3a4e8fb366a544ea46f86b2d737e9ef06b04aad009144c9", "size_in_bytes": 15035}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-313.pyc", "path_type": "hardlink", "sha256": "923412c18d3beaa34d9d5f561ed83f4e811f42dd3da07a17b3fb61a5dee0edd8", "sha256_in_prefix": "923412c18d3beaa34d9d5f561ed83f4e811f42dd3da07a17b3fb61a5dee0edd8", "size_in_bytes": 27638}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-313.pyc", "path_type": "hardlink", "sha256": "0116dc3bd9bc3c51a89e02963308871224603ba187e008bd64598f5290ac7543", "sha256_in_prefix": "0116dc3bd9bc3c51a89e02963308871224603ba187e008bd64598f5290ac7543", "size_in_bytes": 4441}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "bf85c7f14207bb0e0d2e502e69a57d16ab251ded4e9dc0b071052f4d996a845a", "sha256_in_prefix": "bf85c7f14207bb0e0d2e502e69a57d16ab251ded4e9dc0b071052f4d996a845a", "size_in_bytes": 5207}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-313.pyc", "path_type": "hardlink", "sha256": "b4dfab56805219c83db0a1302074a1af7deae133d21daabb5f404cb25d6a7fb5", "sha256_in_prefix": "b4dfab56805219c83db0a1302074a1af7deae133d21daabb5f404cb25d6a7fb5", "size_in_bytes": 2471}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-313.pyc", "path_type": "hardlink", "sha256": "b1b6517ac3c9bb69f78fbaeabffc0d97f66eb26b578de1a55c77a4034a9f3388", "sha256_in_prefix": "b1b6517ac3c9bb69f78fbaeabffc0d97f66eb26b578de1a55c77a4034a9f3388", "size_in_bytes": 8465}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "cd407562ad31a5a34cc7e4bc44d8f51729374c259bd2ef96d63898689b08654c", "sha256_in_prefix": "cd407562ad31a5a34cc7e4bc44d8f51729374c259bd2ef96d63898689b08654c", "size_in_bytes": 3176}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "81dfdcb963c83dec8bffc2b7ed1db381830c6b58cace17897627065525f4afa7", "sha256_in_prefix": "81dfdcb963c83dec8bffc2b7ed1db381830c6b58cace17897627065525f4afa7", "size_in_bytes": 22495}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "sha256_in_prefix": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "size_in_bytes": 5118}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "sha256_in_prefix": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "size_in_bytes": 12818}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "3de8b40e4634203fd8fb5a7461c1a8a6213d3b9bc63a310b0193d192a6911488", "sha256_in_prefix": "3de8b40e4634203fd8fb5a7461c1a8a6213d3b9bc63a310b0193d192a6911488", "size_in_bytes": 1243}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-313.pyc", "path_type": "hardlink", "sha256": "f51fa2f56da5342f4358a1dc6f1469619279000c3b30796497732ed6a9b37991", "sha256_in_prefix": "f51fa2f56da5342f4358a1dc6f1469619279000c3b30796497732ed6a9b37991", "size_in_bytes": 253}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-313.pyc", "path_type": "hardlink", "sha256": "c8e8f6db5ee4c7bededbc7d0e850fc0d5a3757dd2ca7eeba6885ed960547d3f9", "sha256_in_prefix": "c8e8f6db5ee4c7bededbc7d0e850fc0d5a3757dd2ca7eeba6885ed960547d3f9", "size_in_bytes": 2706}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-313.pyc", "path_type": "hardlink", "sha256": "fafa8d3e16eb3cff90fb29cc532f80d511af41d0cced7f9ff5e15c670ae0f743", "sha256_in_prefix": "fafa8d3e16eb3cff90fb29cc532f80d511af41d0cced7f9ff5e15c670ae0f743", "size_in_bytes": 50682}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-313.pyc", "path_type": "hardlink", "sha256": "d79db79128407a7d1a4cb706d8000a414133c6bf598f292c5e4e9837379a8d32", "sha256_in_prefix": "d79db79128407a7d1a4cb706d8000a414133c6bf598f292c5e4e9837379a8d32", "size_in_bytes": 11916}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-313.pyc", "path_type": "hardlink", "sha256": "db7dbd444436532af091e0e4c5fe8378b52ad8b0c9e1574c41e17a5f815557cf", "sha256_in_prefix": "db7dbd444436532af091e0e4c5fe8378b52ad8b0c9e1574c41e17a5f815557cf", "size_in_bytes": 1618}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-313.pyc", "path_type": "hardlink", "sha256": "f5bdaf5bfc53894b76e7605c71b6bc7dd3a9f25666a24ac3e7caba0d44a4591e", "sha256_in_prefix": "f5bdaf5bfc53894b76e7605c71b6bc7dd3a9f25666a24ac3e7caba0d44a4591e", "size_in_bytes": 25538}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-313.pyc", "path_type": "hardlink", "sha256": "2d907db991f2dfec3f32c55b41b8b5cce2acf3fd2e8a47de95237f8271bd9a61", "sha256_in_prefix": "2d907db991f2dfec3f32c55b41b8b5cce2acf3fd2e8a47de95237f8271bd9a61", "size_in_bytes": 15856}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-313.pyc", "path_type": "hardlink", "sha256": "482bef4a38a0f4d48a7908e74f31de60ffdcaeb775ab3336a662060c3514a0d8", "sha256_in_prefix": "482bef4a38a0f4d48a7908e74f31de60ffdcaeb775ab3336a662060c3514a0d8", "size_in_bytes": 6365}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-313.pyc", "path_type": "hardlink", "sha256": "fae1c59e90638f5ece6d1f2438be4ad4bb9f38c3ba1f18091c7e84a153be62d3", "sha256_in_prefix": "fae1c59e90638f5ece6d1f2438be4ad4bb9f38c3ba1f18091c7e84a153be62d3", "size_in_bytes": 4046}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a4903fc025d22ab6c51e103afced533ded74fa72a3fed7de2e6d4763705ceb4", "sha256_in_prefix": "4a4903fc025d22ab6c51e103afced533ded74fa72a3fed7de2e6d4763705ceb4", "size_in_bytes": 4618}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-313.pyc", "path_type": "hardlink", "sha256": "a9b2d2da177059301016b6fe5c2b0777a154f2c8551684a03b8d86f2efb589ce", "sha256_in_prefix": "a9b2d2da177059301016b6fe5c2b0777a154f2c8551684a03b8d86f2efb589ce", "size_in_bytes": 3952}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-313.pyc", "path_type": "hardlink", "sha256": "50d882e8280cc3234dfb99b0b0cbd4850ecd174a1f150ffd8daa5f87a69f5611", "sha256_in_prefix": "50d882e8280cc3234dfb99b0b0cbd4850ecd174a1f150ffd8daa5f87a69f5611", "size_in_bytes": 7597}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-313.pyc", "path_type": "hardlink", "sha256": "4d749099152763b4fa9b551d95f3c5f3fa4f3cb2a99f2717e7364e1db07632a8", "sha256_in_prefix": "4d749099152763b4fa9b551d95f3c5f3fa4f3cb2a99f2717e7364e1db07632a8", "size_in_bytes": 15505}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "sha256_in_prefix": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "size_in_bytes": 11834}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "sha256_in_prefix": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "size_in_bytes": 16502}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "78b9c33c2c9fb555b99144b5838890cf4be0fca7fe30e8b37cf3cf6dfb0d832f", "sha256_in_prefix": "78b9c33c2c9fb555b99144b5838890cf4be0fca7fe30e8b37cf3cf6dfb0d832f", "size_in_bytes": 1904}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-313.pyc", "path_type": "hardlink", "sha256": "7dc5be085cd3cd047e94dbb6c90d74831a7407c1aeeca9970d79fcda75f4129a", "sha256_in_prefix": "7dc5be085cd3cd047e94dbb6c90d74831a7407c1aeeca9970d79fcda75f4129a", "size_in_bytes": 6310}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "28162f8533051844dd27d201ca2bc637c2ca1d26c1127a2eddda0850a87beaa7", "sha256_in_prefix": "28162f8533051844dd27d201ca2bc637c2ca1d26c1127a2eddda0850a87beaa7", "size_in_bytes": 21011}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "e271bc48502277261a9018e7419cb734f94d7f8d2f47c9f17b298d53b4fcfdc7", "sha256_in_prefix": "e271bc48502277261a9018e7419cb734f94d7f8d2f47c9f17b298d53b4fcfdc7", "size_in_bytes": 1943}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-313.pyc", "path_type": "hardlink", "sha256": "151160e629fd7efecc1ece2af350120aecd65cf494143117b9d8296e7ff37a39", "sha256_in_prefix": "151160e629fd7efecc1ece2af350120aecd65cf494143117b9d8296e7ff37a39", "size_in_bytes": 3884}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-313.pyc", "path_type": "hardlink", "sha256": "6c73408617125d75c4799b5db4750c27063759fe05a0ff43f850a04fdaeba933", "sha256_in_prefix": "6c73408617125d75c4799b5db4750c27063759fe05a0ff43f850a04fdaeba933", "size_in_bytes": 5679}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-313.pyc", "path_type": "hardlink", "sha256": "7803fc7655715cbd5f3566bb11226aefd403b4294b0c0aa754eb2b71c995b79a", "sha256_in_prefix": "7803fc7655715cbd5f3566bb11226aefd403b4294b0c0aa754eb2b71c995b79a", "size_in_bytes": 2856}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-313.pyc", "path_type": "hardlink", "sha256": "61ec71fbc053abb49053f48d1da4fc24d10c0e1a0c1c069c7a065ea0f33ab2ca", "sha256_in_prefix": "61ec71fbc053abb49053f48d1da4fc24d10c0e1a0c1c069c7a065ea0f33ab2ca", "size_in_bytes": 6720}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-313.pyc", "path_type": "hardlink", "sha256": "0b06b1a9bfc95b71d3e8742b6033827c3f5322d06cd1de6d9f8dad3574acde43", "sha256_in_prefix": "0b06b1a9bfc95b71d3e8742b6033827c3f5322d06cd1de6d9f8dad3574acde43", "size_in_bytes": 29736}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-313.pyc", "path_type": "hardlink", "sha256": "9bac5bc663a3d91ba89aff25b7b568c07bc0ac458610799c0ecbb4bbfab2e685", "sha256_in_prefix": "9bac5bc663a3d91ba89aff25b7b568c07bc0ac458610799c0ecbb4bbfab2e685", "size_in_bytes": 9905}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "1f095e8a129a94a477cc0c1e1822405fed0d462b600ec9f27a240bea80d72d4e", "sha256_in_prefix": "1f095e8a129a94a477cc0c1e1822405fed0d462b600ec9f27a240bea80d72d4e", "size_in_bytes": 4754}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-313.pyc", "path_type": "hardlink", "sha256": "7009c259c9635ab3894fdd22cab309fcfa9fb2dad92e4675265697b5886f7543", "sha256_in_prefix": "7009c259c9635ab3894fdd22cab309fcfa9fb2dad92e4675265697b5886f7543", "size_in_bytes": 7156}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-313.pyc", "path_type": "hardlink", "sha256": "511eff604db344bb1ca61c2a53a83c0a89c50603934a47e2b5dbd7992f29129f", "sha256_in_prefix": "511eff604db344bb1ca61c2a53a83c0a89c50603934a47e2b5dbd7992f29129f", "size_in_bytes": 1948}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a3ebd1759da6c326bd4944e487cb4a9e81f5f0d9e642cf961e01e6734a111ca", "sha256_in_prefix": "4a3ebd1759da6c326bd4944e487cb4a9e81f5f0d9e642cf961e01e6734a111ca", "size_in_bytes": 6437}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-313.pyc", "path_type": "hardlink", "sha256": "38615cc24d8f2b19a73130c67344c87b5660dd5f1d92147e91a6e2a514377499", "sha256_in_prefix": "38615cc24d8f2b19a73130c67344c87b5660dd5f1d92147e91a6e2a514377499", "size_in_bytes": 5158}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-313.pyc", "path_type": "hardlink", "sha256": "9671f3a8b8cccefe874d41e847395ebe38613ba87ecfda7c39dca918b958e256", "sha256_in_prefix": "9671f3a8b8cccefe874d41e847395ebe38613ba87ecfda7c39dca918b958e256", "size_in_bytes": 6174}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "41ccfe4acc6c0d581d6bc69aa0af10a94284c01950a069539082e5d7d15841c8", "sha256_in_prefix": "41ccfe4acc6c0d581d6bc69aa0af10a94284c01950a069539082e5d7d15841c8", "size_in_bytes": 8565}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-313.pyc", "path_type": "hardlink", "sha256": "9b1a2265ea2bde21128aac3714e323b939d9bbeef8449f670a45848138abf025", "sha256_in_prefix": "9b1a2265ea2bde21128aac3714e323b939d9bbeef8449f670a45848138abf025", "size_in_bytes": 27110}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-313.pyc", "path_type": "hardlink", "sha256": "43dd9b9b6cc616dd113d10eeb0c12716838607d282889d75a2e0176791a27a6a", "sha256_in_prefix": "43dd9b9b6cc616dd113d10eeb0c12716838607d282889d75a2e0176791a27a6a", "size_in_bytes": 4252}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "343edf2951b592ae98d89efa1cbf25b38050096a5526266f86cad62fe9ad15fb", "sha256_in_prefix": "343edf2951b592ae98d89efa1cbf25b38050096a5526266f86cad62fe9ad15fb", "size_in_bytes": 6940}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-313.pyc", "path_type": "hardlink", "sha256": "76f71008808a6740791b2f67d2815fa16ff7634928a531f53aa48275cc3f17b5", "sha256_in_prefix": "76f71008808a6740791b2f67d2815fa16ff7634928a531f53aa48275cc3f17b5", "size_in_bytes": 14496}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-313.pyc", "path_type": "hardlink", "sha256": "ba3a12ddc200880c35d6dacd570e0133c9799a9c2a9186feb6123ff52cf7906c", "sha256_in_prefix": "ba3a12ddc200880c35d6dacd570e0133c9799a9c2a9186feb6123ff52cf7906c", "size_in_bytes": 14225}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-313.pyc", "path_type": "hardlink", "sha256": "6b5003bfbc3241330afd54a8226f6120bbbf91c0d42e220d06b2e096bf6fed30", "sha256_in_prefix": "6b5003bfbc3241330afd54a8226f6120bbbf91c0d42e220d06b2e096bf6fed30", "size_in_bytes": 4895}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-313.pyc", "path_type": "hardlink", "sha256": "7afaa35a82a76759cd1c4979afbbffaabd267a30f9453127f3d52e6dbf98d667", "sha256_in_prefix": "7afaa35a82a76759cd1c4979afbbffaabd267a30f9453127f3d52e6dbf98d667", "size_in_bytes": 1899}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-313.pyc", "path_type": "hardlink", "sha256": "7b2b295380799cddb2d35bf182fb0e7da6f0d6ded8bc2e4b0795ba697a8960a3", "sha256_in_prefix": "7b2b295380799cddb2d35bf182fb0e7da6f0d6ded8bc2e4b0795ba697a8960a3", "size_in_bytes": 6180}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "233c53f1071a215ce3616cbeb436072cda35cedd76f82bd929dde5b02b2722c3", "sha256_in_prefix": "233c53f1071a215ce3616cbeb436072cda35cedd76f82bd929dde5b02b2722c3", "size_in_bytes": 2762}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-313.pyc", "path_type": "hardlink", "sha256": "63538cf1688754c06e29091169e4dabcc1ad30679bc0502e53dc78c2437e62a7", "sha256_in_prefix": "63538cf1688754c06e29091169e4dabcc1ad30679bc0502e53dc78c2437e62a7", "size_in_bytes": 987}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-313.pyc", "path_type": "hardlink", "sha256": "d3f07b2d342460c9c6b58453c6f3f3b96bd8688dd7fc5852a07407976193b000", "sha256_in_prefix": "d3f07b2d342460c9c6b58453c6f3f3b96bd8688dd7fc5852a07407976193b000", "size_in_bytes": 7231}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "aa397992bfca055d68a178cb5f39bc2dd1bab6c5a140e9833853503a8935fe72", "sha256_in_prefix": "aa397992bfca055d68a178cb5f39bc2dd1bab6c5a140e9833853503a8935fe72", "size_in_bytes": 20716}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-313.pyc", "path_type": "hardlink", "sha256": "559584a6e422438240f1689cdf8870b705e741ad6a546179d0659f454d1ac74c", "sha256_in_prefix": "559584a6e422438240f1689cdf8870b705e741ad6a546179d0659f454d1ac74c", "size_in_bytes": 6835}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-313.pyc", "path_type": "hardlink", "sha256": "98863e46de37211814573df220c9b36253d2d5aa015653f894f4e590e4e765de", "sha256_in_prefix": "98863e46de37211814573df220c9b36253d2d5aa015653f894f4e590e4e765de", "size_in_bytes": 18437}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-313.pyc", "path_type": "hardlink", "sha256": "2d28fd44dd85129fdd9c220ebdb0905acf4acb957fe625f65b962b532aa84d40", "sha256_in_prefix": "2d28fd44dd85129fdd9c220ebdb0905acf4acb957fe625f65b962b532aa84d40", "size_in_bytes": 3306}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "c0dec5651f7a762398994fe6622578bf51c9011e617c61901a0bddb5a9ae5202", "sha256_in_prefix": "c0dec5651f7a762398994fe6622578bf51c9011e617c61901a0bddb5a9ae5202", "size_in_bytes": 13434}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-313.pyc", "path_type": "hardlink", "sha256": "e1a89d1f16ed3029f670528c76d96ac515872a20b52e7460b2c73529d62f184b", "sha256_in_prefix": "e1a89d1f16ed3029f670528c76d96ac515872a20b52e7460b2c73529d62f184b", "size_in_bytes": 3869}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-313.pyc", "path_type": "hardlink", "sha256": "dd539e81a6fd6b8b4d445973ef353567776ffcf8f9807f8fabcdbda361690042", "sha256_in_prefix": "dd539e81a6fd6b8b4d445973ef353567776ffcf8f9807f8fabcdbda361690042", "size_in_bytes": 178}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-313.pyc", "path_type": "hardlink", "sha256": "57be9678406feacde7209faf5c75767752f08f0a925dec76ab4447b2a04e95b5", "sha256_in_prefix": "57be9678406feacde7209faf5c75767752f08f0a925dec76ab4447b2a04e95b5", "size_in_bytes": 782}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "5148d3ba4d1fba5a6d6a549d867eebe3874254c3f05f32e44f479ae99f461a78", "sha256_in_prefix": "5148d3ba4d1fba5a6d6a549d867eebe3874254c3f05f32e44f479ae99f461a78", "size_in_bytes": 172}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-313.pyc", "path_type": "hardlink", "sha256": "7177f075ed3afea239b03007d1634817bc6e2a120ffdab25b7715241055e7bb2", "sha256_in_prefix": "7177f075ed3afea239b03007d1634817bc6e2a120ffdab25b7715241055e7bb2", "size_in_bytes": 658}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "lib/python3.13/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "lib/python3.13/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "lib/python3.13/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "lib/python3.13/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "lib/python3.13/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "lib/python3.13/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "sha256_in_prefix": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "size_in_bytes": 5824}, {"_path": "lib/python3.13/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "lib/python3.13/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "lib/python3.13/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "lib/python3.13/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-313.pyc", "path_type": "hardlink", "sha256": "649dd055d8393509ee793835d23296bd512b51fb5a3d997c2502df309ae60072", "sha256_in_prefix": "649dd055d8393509ee793835d23296bd512b51fb5a3d997c2502df309ae60072", "size_in_bytes": 142668}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "d5a870d1b6046b5c0925d6e4ca3d7613a36a848b1d3831e9be51ac31b40f3055", "sha256_in_prefix": "d5a870d1b6046b5c0925d6e4ca3d7613a36a848b1d3831e9be51ac31b40f3055", "size_in_bytes": 400}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-313.pyc", "path_type": "hardlink", "sha256": "5457365b08c916ff81174d8b1ea878b943008741cf10fc48f74b8b81893acd1e", "sha256_in_prefix": "5457365b08c916ff81174d8b1ea878b943008741cf10fc48f74b8b81893acd1e", "size_in_bytes": 4842}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-313.pyc", "path_type": "hardlink", "sha256": "bd3ecb7300c9367dd0ddace68ab8afe324acb9ae3628cf7fd0e4bca6ffcb6adf", "sha256_in_prefix": "bd3ecb7300c9367dd0ddace68ab8afe324acb9ae3628cf7fd0e4bca6ffcb6adf", "size_in_bytes": 1289}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-313.pyc", "path_type": "hardlink", "sha256": "d44ed9ccf355f9c95822833f5bfae67977e839611978b34c6c607c2770fd56c9", "sha256_in_prefix": "d44ed9ccf355f9c95822833f5bfae67977e839611978b34c6c607c2770fd56c9", "size_in_bytes": 1861}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-313.pyc", "path_type": "hardlink", "sha256": "f4638b1c3f4d34a975e76132b58403cbbbf51546fc891e16ffbd6d604cdb0ce8", "sha256_in_prefix": "f4638b1c3f4d34a975e76132b58403cbbbf51546fc891e16ffbd6d604cdb0ce8", "size_in_bytes": 11062}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-313.pyc", "path_type": "hardlink", "sha256": "974b32c75463e57b8418c3667c515df09276f0a886b51b097b790cfdd7c16acc", "sha256_in_prefix": "974b32c75463e57b8418c3667c515df09276f0a886b51b097b790cfdd7c16acc", "size_in_bytes": 472}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "e493a6d93cd5eab35a63accb52baedb38a11eed29ceb562ea39e77583f0a5bf2", "sha256_in_prefix": "e493a6d93cd5eab35a63accb52baedb38a11eed29ceb562ea39e77583f0a5bf2", "size_in_bytes": 288}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "6fbc87dc51ea821f92366abb31a431fad13ab0a020c1c753f15bec7c95c60ab8", "sha256_in_prefix": "6fbc87dc51ea821f92366abb31a431fad13ab0a020c1c753f15bec7c95c60ab8", "size_in_bytes": 121832}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-313.pyc", "path_type": "hardlink", "sha256": "bcbbc50b34ec97b21fe98cc81eca14ef5e64c3d29b9badb21d10dc1c9069dfaa", "sha256_in_prefix": "bcbbc50b34ec97b21fe98cc81eca14ef5e64c3d29b9badb21d10dc1c9069dfaa", "size_in_bytes": 274}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "cb4b73afa9f7938d3e9f9acb507576de1de5a41c16ca7bb65f533ea5cf0a9c06", "sha256_in_prefix": "cb4b73afa9f7938d3e9f9acb507576de1de5a41c16ca7bb65f533ea5cf0a9c06", "size_in_bytes": 181}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-313.pyc", "path_type": "hardlink", "sha256": "272a1b4293bbfc32ad57c787f0f07ad3ce8284d58b5d0339e36e22a91d126897", "sha256_in_prefix": "272a1b4293bbfc32ad57c787f0f07ad3ce8284d58b5d0339e36e22a91d126897", "size_in_bytes": 1065}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "662cef7997d2f206cac7b13ac0dae51eb1de36b8303a648aa8c2ffc3738bc881", "sha256_in_prefix": "662cef7997d2f206cac7b13ac0dae51eb1de36b8303a648aa8c2ffc3738bc881", "size_in_bytes": 52252}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-313.pyc", "path_type": "hardlink", "sha256": "9f187d7edb690b828a0e12238b652bd7e396557a9913f197c8637a8c4b26ca41", "sha256_in_prefix": "9f187d7edb690b828a0e12238b652bd7e396557a9913f197c8637a8c4b26ca41", "size_in_bytes": 3784}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-313.pyc", "path_type": "hardlink", "sha256": "1e0b63928f694ce02e93b000947b1e946b5d35ad1c49695b573d394d3a1fe8a0", "sha256_in_prefix": "1e0b63928f694ce02e93b000947b1e946b5d35ad1c49695b573d394d3a1fe8a0", "size_in_bytes": 1952}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-313.pyc", "path_type": "hardlink", "sha256": "e36a4bc5244629f68c9af46ed3ba12d82b0e374566a40931520832521f13aac0", "sha256_in_prefix": "e36a4bc5244629f68c9af46ed3ba12d82b0e374566a40931520832521f13aac0", "size_in_bytes": 2251}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-313.pyc", "path_type": "hardlink", "sha256": "aeb54264612e07f0a7d2af7fdb3389d83a93fc87b3ec68607e452267c9cf26bb", "sha256_in_prefix": "aeb54264612e07f0a7d2af7fdb3389d83a93fc87b3ec68607e452267c9cf26bb", "size_in_bytes": 3267}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-313.pyc", "path_type": "hardlink", "sha256": "32b14438aca5d3fef18bcb8e2ddf41848c9d2e2dfef99bc4039f064e6c2fdecc", "sha256_in_prefix": "32b14438aca5d3fef18bcb8e2ddf41848c9d2e2dfef99bc4039f064e6c2fdecc", "size_in_bytes": 2302}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-313.pyc", "path_type": "hardlink", "sha256": "eab372027acd3483ffb0aee9991b710745e3bcaf1b7f3171b0bd172a9a4b566d", "sha256_in_prefix": "eab372027acd3483ffb0aee9991b710745e3bcaf1b7f3171b0bd172a9a4b566d", "size_in_bytes": 3683}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-313.pyc", "path_type": "hardlink", "sha256": "6eac44315f4b063a75734348c05048c37f7fb604419bb1a73a1004e392fdb19d", "sha256_in_prefix": "6eac44315f4b063a75734348c05048c37f7fb604419bb1a73a1004e392fdb19d", "size_in_bytes": 3785}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-313.pyc", "path_type": "hardlink", "sha256": "53073d4ce2bccefb1571ba4e2a9efd1a7796e807be288ea8fe71fcdad3ffbecd", "sha256_in_prefix": "53073d4ce2bccefb1571ba4e2a9efd1a7796e807be288ea8fe71fcdad3ffbecd", "size_in_bytes": 1181}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "e536909c96d6431ea911a460d7642b378e2f946912aea83f0d330e49e8496cf3", "sha256_in_prefix": "e536909c96d6431ea911a460d7642b378e2f946912aea83f0d330e49e8496cf3", "size_in_bytes": 182}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-313.pyc", "path_type": "hardlink", "sha256": "74de8667c9312adf1a5c6b1d0df4812dbc3b0e00848a81c45fb6f4c80b6f2303", "sha256_in_prefix": "74de8667c9312adf1a5c6b1d0df4812dbc3b0e00848a81c45fb6f4c80b6f2303", "size_in_bytes": 1231}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-313.pyc", "path_type": "hardlink", "sha256": "11c4096cbb40629d5cbba1590a09b8ad85a7f77e4f8281b1880d9b7b1e9796dd", "sha256_in_prefix": "11c4096cbb40629d5cbba1590a09b8ad85a7f77e4f8281b1880d9b7b1e9796dd", "size_in_bytes": 1634}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "f892ff05916976d83638f1363976c6e48c6ef1a9749e7c06f289c3db1d2dd301", "sha256_in_prefix": "f892ff05916976d83638f1363976c6e48c6ef1a9749e7c06f289c3db1d2dd301", "size_in_bytes": 113218}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "0d73d75eee1b976c68fde1b2c6a8f59b7d1247386c615f3176325c76f7d180fd", "sha256_in_prefix": "0d73d75eee1b976c68fde1b2c6a8f59b7d1247386c615f3176325c76f7d180fd", "size_in_bytes": 171}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-313.pyc", "path_type": "hardlink", "sha256": "584bcbfbf59412d8fa813932a08458c267d54bedcfcc02633950279b627f0233", "sha256_in_prefix": "584bcbfbf59412d8fa813932a08458c267d54bedcfcc02633950279b627f0233", "size_in_bytes": 336}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-313.pyc", "path_type": "hardlink", "sha256": "83de48023d6f0e50a0ea4bf606081bbf914de3f00b28bdc476c1f8c0434c170a", "sha256_in_prefix": "83de48023d6f0e50a0ea4bf606081bbf914de3f00b28bdc476c1f8c0434c170a", "size_in_bytes": 13845}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "288b06956805ce963f6675a4b86825a814df1030610ed6ecef56e2abcc738dd2", "sha256_in_prefix": "288b06956805ce963f6675a4b86825a814df1030610ed6ecef56e2abcc738dd2", "size_in_bytes": 38575}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "5eef8f185144cd531730c1d3484389d7e2d2c659efce0c3a4733995259d76216", "sha256_in_prefix": "5eef8f185144cd531730c1d3484389d7e2d2c659efce0c3a4733995259d76216", "size_in_bytes": 21880}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "f5630be58cb63a942cb26c40a8b509d0901cf80f80f39559dda10168d7a6a703", "sha256_in_prefix": "f5630be58cb63a942cb26c40a8b509d0901cf80f80f39559dda10168d7a6a703", "size_in_bytes": 24618}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-313.pyc", "path_type": "hardlink", "sha256": "6847ad85b0ded96cefa425f18c84f7cac480f3415519389eaf165535a489f235", "sha256_in_prefix": "6847ad85b0ded96cefa425f18c84f7cac480f3415519389eaf165535a489f235", "size_in_bytes": 1050}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-313.pyc", "path_type": "hardlink", "sha256": "a0969a535c80a3c8258bd1e65dd1129355889466fc4e3422b182c887a95fbedc", "sha256_in_prefix": "a0969a535c80a3c8258bd1e65dd1129355889466fc4e3422b182c887a95fbedc", "size_in_bytes": 1411}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-313.pyc", "path_type": "hardlink", "sha256": "9fb395ba6242fee06e73ef1710ab43262d69a80f04858f83588e020e5e6e3ced", "sha256_in_prefix": "9fb395ba6242fee06e73ef1710ab43262d69a80f04858f83588e020e5e6e3ced", "size_in_bytes": 851}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-313.pyc", "path_type": "hardlink", "sha256": "28e71eec96fc9465daa8e8d886d4ee6cdf421bdba5ff2a97d012b30cd8d785fa", "sha256_in_prefix": "28e71eec96fc9465daa8e8d886d4ee6cdf421bdba5ff2a97d012b30cd8d785fa", "size_in_bytes": 429}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-313.pyc", "path_type": "hardlink", "sha256": "3c213d6c3a9bc3bf2e4adf3d00419d4eee4088a1fd8b1722c86a762065ecc21a", "sha256_in_prefix": "3c213d6c3a9bc3bf2e4adf3d00419d4eee4088a1fd8b1722c86a762065ecc21a", "size_in_bytes": 429}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "eea72d8abd972b70254f82819f2d101b37914173a5ce60603cd23e68af6c7c94", "sha256_in_prefix": "eea72d8abd972b70254f82819f2d101b37914173a5ce60603cd23e68af6c7c94", "size_in_bytes": 325}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-313.pyc", "path_type": "hardlink", "sha256": "0a4411569906ddb88eda5482fbea2064ce58734923d7a0e6018aea03cc4cd164", "sha256_in_prefix": "0a4411569906ddb88eda5482fbea2064ce58734923d7a0e6018aea03cc4cd164", "size_in_bytes": 167984}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-313.pyc", "path_type": "hardlink", "sha256": "7c66dc4581349912d152c77927eee1a074fb242cc60bb4a74fa889dd0f21ad64", "sha256_in_prefix": "7c66dc4581349912d152c77927eee1a074fb242cc60bb4a74fa889dd0f21ad64", "size_in_bytes": 34883}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "ba0127f745032c4dcb58c56dddec04cd0a0be00e1ad5e689df68a2f93748a031", "sha256_in_prefix": "ba0127f745032c4dcb58c56dddec04cd0a0be00e1ad5e689df68a2f93748a031", "size_in_bytes": 531}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-313.pyc", "path_type": "hardlink", "sha256": "a7eff50ea4b43f1d073e882435fcc491123752abadc579d998e7297d455558b6", "sha256_in_prefix": "a7eff50ea4b43f1d073e882435fcc491123752abadc579d998e7297d455558b6", "size_in_bytes": 5197}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-313.pyc", "path_type": "hardlink", "sha256": "2faef6d74af1e7e1a9851a3cefad4e88d08e53814a8115b9841c0bfc8c1f9855", "sha256_in_prefix": "2faef6d74af1e7e1a9851a3cefad4e88d08e53814a8115b9841c0bfc8c1f9855", "size_in_bytes": 9982}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-313.pyc", "path_type": "hardlink", "sha256": "4cf28ba23a7e3cd136c1bb2d2d9025646fe83861701a526b5448be6ea44d0549", "sha256_in_prefix": "4cf28ba23a7e3cd136c1bb2d2d9025646fe83861701a526b5448be6ea44d0549", "size_in_bytes": 4596}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-313.pyc", "path_type": "hardlink", "sha256": "205deb963d7036a28d630aec435638b4303cacc9871e9f032749eab65f1547c0", "sha256_in_prefix": "205deb963d7036a28d630aec435638b4303cacc9871e9f032749eab65f1547c0", "size_in_bytes": 14162}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-313.pyc", "path_type": "hardlink", "sha256": "3ccc623798bcd7d0fc5c1854406350002270f99b5e04e93548868c0d2db1942d", "sha256_in_prefix": "3ccc623798bcd7d0fc5c1854406350002270f99b5e04e93548868c0d2db1942d", "size_in_bytes": 3326}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-313.pyc", "path_type": "hardlink", "sha256": "3d0ef5bd221331c862d62350b8b505e888106c90f71c08897a8e2f681d25768e", "sha256_in_prefix": "3d0ef5bd221331c862d62350b8b505e888106c90f71c08897a8e2f681d25768e", "size_in_bytes": 8046}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-313.pyc", "path_type": "hardlink", "sha256": "28cd5adfaac904079f5784fbcad027a92f59ec2d64eec23ef287b55362e6e708", "sha256_in_prefix": "28cd5adfaac904079f5784fbcad027a92f59ec2d64eec23ef287b55362e6e708", "size_in_bytes": 11652}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-313.pyc", "path_type": "hardlink", "sha256": "705df63daa6521d1bac1ec8b706002f890f793e2854560976902c85c475d887d", "sha256_in_prefix": "705df63daa6521d1bac1ec8b706002f890f793e2854560976902c85c475d887d", "size_in_bytes": 27346}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-313.pyc", "path_type": "hardlink", "sha256": "da2d86f8e65cbc9f3dd7bfbf4afe54a083fdbeb44b24a36ce0056c66c0619264", "sha256_in_prefix": "da2d86f8e65cbc9f3dd7bfbf4afe54a083fdbeb44b24a36ce0056c66c0619264", "size_in_bytes": 4607}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-313.pyc", "path_type": "hardlink", "sha256": "c9e91a9201da4f015d3bbbe5ec00ce8b78cee57fcc00461ba5c47676cd9d2f80", "sha256_in_prefix": "c9e91a9201da4f015d3bbbe5ec00ce8b78cee57fcc00461ba5c47676cd9d2f80", "size_in_bytes": 37618}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-313.pyc", "path_type": "hardlink", "sha256": "d1dc18c5c4067cc126ba513700343338daded1ca24cb490a97a8919590ca4a28", "sha256_in_prefix": "d1dc18c5c4067cc126ba513700343338daded1ca24cb490a97a8919590ca4a28", "size_in_bytes": 23296}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-313.pyc", "path_type": "hardlink", "sha256": "5ecf9d11b37239ae7d4dccc5c75cb019d11f90b7fbbab6e35e762fa0cc5e122b", "sha256_in_prefix": "5ecf9d11b37239ae7d4dccc5c75cb019d11f90b7fbbab6e35e762fa0cc5e122b", "size_in_bytes": 6731}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-313.pyc", "path_type": "hardlink", "sha256": "df46d8772511505d5360c1f0fbf48d8e1f4c23edc84b460c2c15b8ae2328c052", "sha256_in_prefix": "df46d8772511505d5360c1f0fbf48d8e1f4c23edc84b460c2c15b8ae2328c052", "size_in_bytes": 19944}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "b528d4916e05f528ec1be872894815d044ae83f2d2a74b11a82db696963bec29", "sha256_in_prefix": "b528d4916e05f528ec1be872894815d044ae83f2d2a74b11a82db696963bec29", "size_in_bytes": 4272}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-313.pyc", "path_type": "hardlink", "sha256": "a1856eb1c470b365cfdcccb78fa2ec94d5e063015cc466daaa60ec958ad9c649", "sha256_in_prefix": "a1856eb1c470b365cfdcccb78fa2ec94d5e063015cc466daaa60ec958ad9c649", "size_in_bytes": 47413}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "699f8fa921b5338d6b8e66ef221d1dc10b6ce639b17c6f6d49f4e98cd504be33", "sha256_in_prefix": "699f8fa921b5338d6b8e66ef221d1dc10b6ce639b17c6f6d49f4e98cd504be33", "size_in_bytes": 19262}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-313.pyc", "path_type": "hardlink", "sha256": "1c262fbbee9543d8696aa27298483a73e65367c0f1341107f8a69e5adc971948", "sha256_in_prefix": "1c262fbbee9543d8696aa27298483a73e65367c0f1341107f8a69e5adc971948", "size_in_bytes": 1893}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-313.pyc", "path_type": "hardlink", "sha256": "5b9ade726fcf506b94aa114d2f0392a32007421556a713c854355abc5044eaf4", "sha256_in_prefix": "5b9ade726fcf506b94aa114d2f0392a32007421556a713c854355abc5044eaf4", "size_in_bytes": 10748}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-313.pyc", "path_type": "hardlink", "sha256": "5247c4b9e06bef5d9e0cb08ad24fa10f0e69757884c97b7d493397dfad0e2a42", "sha256_in_prefix": "5247c4b9e06bef5d9e0cb08ad24fa10f0e69757884c97b7d493397dfad0e2a42", "size_in_bytes": 13006}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-313.pyc", "path_type": "hardlink", "sha256": "816e0ddd17f1f86276d995d5abc38077dd030d23ddb702003233436476d4d664", "sha256_in_prefix": "816e0ddd17f1f86276d995d5abc38077dd030d23ddb702003233436476d4d664", "size_in_bytes": 7970}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-313.pyc", "path_type": "hardlink", "sha256": "f538c75772e45056fedb16cad57fec3265e2a5a0419ddd7c9ce1de81b997b439", "sha256_in_prefix": "f538c75772e45056fedb16cad57fec3265e2a5a0419ddd7c9ce1de81b997b439", "size_in_bytes": 15029}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-313.pyc", "path_type": "hardlink", "sha256": "c64d981894952320f962fc7305d493172baf1648ad316fe407615ad92bb825e8", "sha256_in_prefix": "c64d981894952320f962fc7305d493172baf1648ad316fe407615ad92bb825e8", "size_in_bytes": 580}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-313.pyc", "path_type": "hardlink", "sha256": "0d39046d37db60bfca3d7834a515ec38bfd3fca57ec7de9812c558fe722bf1ab", "sha256_in_prefix": "0d39046d37db60bfca3d7834a515ec38bfd3fca57ec7de9812c558fe722bf1ab", "size_in_bytes": 13757}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "08f4435fd661cdd145dd5ee487d86f17db34d54047fc25344ec401db3317a7eb", "sha256_in_prefix": "08f4435fd661cdd145dd5ee487d86f17db34d54047fc25344ec401db3317a7eb", "size_in_bytes": 358}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-313.pyc", "path_type": "hardlink", "sha256": "34654321ce0f27827d6b803c79ff24b0490bd0f5868e756647bb81c40ecca596", "sha256_in_prefix": "34654321ce0f27827d6b803c79ff24b0490bd0f5868e756647bb81c40ecca596", "size_in_bytes": 27070}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-313.pyc", "path_type": "hardlink", "sha256": "bbbfe03d4765855f287c2e59ab14a462348346bc3f1f90048d86d93460aa9e28", "sha256_in_prefix": "bbbfe03d4765855f287c2e59ab14a462348346bc3f1f90048d86d93460aa9e28", "size_in_bytes": 3870}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-313.pyc", "path_type": "hardlink", "sha256": "f78bbe179c2d93f7a79844e850e861e503c26bfd5126ac2ae5c3084bf01a9552", "sha256_in_prefix": "f78bbe179c2d93f7a79844e850e861e503c26bfd5126ac2ae5c3084bf01a9552", "size_in_bytes": 340}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "0487e19a1056b1aeeb6decfdc4e793690036f0782315c5d4d346f55f9d45f04e", "sha256_in_prefix": "0487e19a1056b1aeeb6decfdc4e793690036f0782315c5d4d346f55f9d45f04e", "size_in_bytes": 2079}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-313.pyc", "path_type": "hardlink", "sha256": "52a74fa02719bf6ab183236d3d5367c69b4c25941be6a6b472bb540feb85e368", "sha256_in_prefix": "52a74fa02719bf6ab183236d3d5367c69b4c25941be6a6b472bb540feb85e368", "size_in_bytes": 35900}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-313.pyc", "path_type": "hardlink", "sha256": "57c9c92cb808e889cb0f5d91816345b21da8da5f65ee6256349d819e10cb32a2", "sha256_in_prefix": "57c9c92cb808e889cb0f5d91816345b21da8da5f65ee6256349d819e10cb32a2", "size_in_bytes": 3801}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-313.pyc", "path_type": "hardlink", "sha256": "a5da2baef7afdf172d7c48c2727c4720ff1d324af855a25ba8c1717ada8b5918", "sha256_in_prefix": "a5da2baef7afdf172d7c48c2727c4720ff1d324af855a25ba8c1717ada8b5918", "size_in_bytes": 10716}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-313.pyc", "path_type": "hardlink", "sha256": "78684bd474873e4a6bfe083bc6260631083517385e0a9d0206d02cde12dc7449", "sha256_in_prefix": "78684bd474873e4a6bfe083bc6260631083517385e0a9d0206d02cde12dc7449", "size_in_bytes": 2879}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-313.pyc", "path_type": "hardlink", "sha256": "511f737e8536ef27a0d0ee507240602b263609f50b4fe0e2f752ead4ae72ef33", "sha256_in_prefix": "511f737e8536ef27a0d0ee507240602b263609f50b4fe0e2f752ead4ae72ef33", "size_in_bytes": 12281}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-313.pyc", "path_type": "hardlink", "sha256": "c486cf89eb23d9afd793a9aa416b22a0e0cd62332477a70a3e86eea959ac851b", "sha256_in_prefix": "c486cf89eb23d9afd793a9aa416b22a0e0cd62332477a70a3e86eea959ac851b", "size_in_bytes": 9271}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-313.pyc", "path_type": "hardlink", "sha256": "f8b6d53670c4abef1195e7a90dc3f4b33123c3bf9aeb44c087b333577b1d8d2d", "sha256_in_prefix": "f8b6d53670c4abef1195e7a90dc3f4b33123c3bf9aeb44c087b333577b1d8d2d", "size_in_bytes": 1725}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-313.pyc", "path_type": "hardlink", "sha256": "ed8f840b52f5b539f3a1338219ef7c93c27e5429d7d2bed728905904ff3d01a4", "sha256_in_prefix": "ed8f840b52f5b539f3a1338219ef7c93c27e5429d7d2bed728905904ff3d01a4", "size_in_bytes": 5622}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-313.pyc", "path_type": "hardlink", "sha256": "6512308027a542486a8eed6e18a2fbcf9f56af4335c05bee9e1fafce396eb685", "sha256_in_prefix": "6512308027a542486a8eed6e18a2fbcf9f56af4335c05bee9e1fafce396eb685", "size_in_bytes": 3397}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-313.pyc", "path_type": "hardlink", "sha256": "1757838195a625907a02800e74ae77316e662209b68675ed564c084f4b3dc955", "sha256_in_prefix": "1757838195a625907a02800e74ae77316e662209b68675ed564c084f4b3dc955", "size_in_bytes": 53639}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-313.pyc", "path_type": "hardlink", "sha256": "841f6b1b9b1e62664460290a4485bf8455a2f76df0633c1ef9079c2a68421819", "sha256_in_prefix": "841f6b1b9b1e62664460290a4485bf8455a2f76df0633c1ef9079c2a68421819", "size_in_bytes": 2440}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-313.pyc", "path_type": "hardlink", "sha256": "d86eab7fa0e2025f922566a0814cdd0e0f3e0e65ebb06f281160f7d9d59acc50", "sha256_in_prefix": "d86eab7fa0e2025f922566a0814cdd0e0f3e0e65ebb06f281160f7d9d59acc50", "size_in_bytes": 7882}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "fa5fa19152df9cc3ad6d3db8711c5ea41e4a7ed63afbaea7c6799f38e391ff22", "sha256_in_prefix": "fa5fa19152df9cc3ad6d3db8711c5ea41e4a7ed63afbaea7c6799f38e391ff22", "size_in_bytes": 241}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-313.pyc", "path_type": "hardlink", "sha256": "772563f23e764e1a9de3c3b90f0dcb25efc871484c71832e716518429327f9f9", "sha256_in_prefix": "772563f23e764e1a9de3c3b90f0dcb25efc871484c71832e716518429327f9f9", "size_in_bytes": 980}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "8b9de3500e98cd7c88e4dd2fde21552ef1efcc9efb0acbf2eae744b1dc5f6be8", "sha256_in_prefix": "8b9de3500e98cd7c88e4dd2fde21552ef1efcc9efb0acbf2eae744b1dc5f6be8", "size_in_bytes": 26744}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-313.pyc", "path_type": "hardlink", "sha256": "dd323d01434d3f407a00a0da6e1888cece1cb69d54f7f4351bf4f48178e23538", "sha256_in_prefix": "dd323d01434d3f407a00a0da6e1888cece1cb69d54f7f4351bf4f48178e23538", "size_in_bytes": 1367}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "0f95c339aabea042040ed08df1f408624af973940fcec6b751298c19eb5626ad", "sha256_in_prefix": "0f95c339aabea042040ed08df1f408624af973940fcec6b751298c19eb5626ad", "size_in_bytes": 759}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-313.pyc", "path_type": "hardlink", "sha256": "e1eb4856998a1c26b4a1c1b197cf44051db7759da9999265cdd5a7971bd544f8", "sha256_in_prefix": "e1eb4856998a1c26b4a1c1b197cf44051db7759da9999265cdd5a7971bd544f8", "size_in_bytes": 16358}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-313.pyc", "path_type": "hardlink", "sha256": "b97f303c7389cb303a0ce6aee62f27a52e38f90e3e29515d5243e4ba1f21f0b4", "sha256_in_prefix": "b97f303c7389cb303a0ce6aee62f27a52e38f90e3e29515d5243e4ba1f21f0b4", "size_in_bytes": 8689}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-313.pyc", "path_type": "hardlink", "sha256": "2c3c86105b7e86a599cba4229d98835fd7a816035d13b6d224a3f70f449d05f1", "sha256_in_prefix": "2c3c86105b7e86a599cba4229d98835fd7a816035d13b6d224a3f70f449d05f1", "size_in_bytes": 931}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-313.pyc", "path_type": "hardlink", "sha256": "f119a4fff3920b24fa6670c3cf52cb8a5fbf6a2665ce8352770761a108c493b2", "sha256_in_prefix": "f119a4fff3920b24fa6670c3cf52cb8a5fbf6a2665ce8352770761a108c493b2", "size_in_bytes": 11754}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "d05ceb470dcce47a5818d1c597923d396b76d6ccb7b21148f02ed7b2c33ee818", "sha256_in_prefix": "d05ceb470dcce47a5818d1c597923d396b76d6ccb7b21148f02ed7b2c33ee818", "size_in_bytes": 6865}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-313.pyc", "path_type": "hardlink", "sha256": "8fc67d91e2030140cb416a36b80a15787c9b1b7f197d5c622a209ce7dfc466ad", "sha256_in_prefix": "8fc67d91e2030140cb416a36b80a15787c9b1b7f197d5c622a209ce7dfc466ad", "size_in_bytes": 16437}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-313.pyc", "path_type": "hardlink", "sha256": "dd00aa8678d3cb9b7c8a811d21d50d1c14c0061d671851b92999ba2688704f22", "sha256_in_prefix": "dd00aa8678d3cb9b7c8a811d21d50d1c14c0061d671851b92999ba2688704f22", "size_in_bytes": 4491}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-313.pyc", "path_type": "hardlink", "sha256": "0ad2121fe301fb5d863845874102b49c1deb5f1e5ede59a81793a5e08eb309fb", "sha256_in_prefix": "0ad2121fe301fb5d863845874102b49c1deb5f1e5ede59a81793a5e08eb309fb", "size_in_bytes": 6805}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-313.pyc", "path_type": "hardlink", "sha256": "bfab0afb7278bb6e564f520492a149ca532931a7a9d2dd1f13f7647bc92ac40b", "sha256_in_prefix": "bfab0afb7278bb6e564f520492a149ca532931a7a9d2dd1f13f7647bc92ac40b", "size_in_bytes": 1478}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "5339a2afefdaf663e10197089434ec95dd9224f5497d1093a765395c6c0e7c9d", "sha256_in_prefix": "5339a2afefdaf663e10197089434ec95dd9224f5497d1093a765395c6c0e7c9d", "size_in_bytes": 171}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "fcdbab7409297764d81155c2f60a32ddf8e211eb09131faeb86cadde758f1def", "sha256_in_prefix": "fcdbab7409297764d81155c2f60a32ddf8e211eb09131faeb86cadde758f1def", "size_in_bytes": 181}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-313.pyc", "path_type": "hardlink", "sha256": "19656a7533a3cffb3dfe3d9e60b758e4e26532b8185e1647b470b5780e9bb6c1", "sha256_in_prefix": "19656a7533a3cffb3dfe3d9e60b758e4e26532b8185e1647b470b5780e9bb6c1", "size_in_bytes": 5208}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-313.pyc", "path_type": "hardlink", "sha256": "ba793fe8e07cd74e310c82b8441fa3c93a1dd65ecda807a9b1e940f5958f7f50", "sha256_in_prefix": "ba793fe8e07cd74e310c82b8441fa3c93a1dd65ecda807a9b1e940f5958f7f50", "size_in_bytes": 10138}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-313.pyc", "path_type": "hardlink", "sha256": "ec98792f3ddcd799c050dd290a49fc6026220c743e606e22c514629fde0ba07c", "sha256_in_prefix": "ec98792f3ddcd799c050dd290a49fc6026220c743e606e22c514629fde0ba07c", "size_in_bytes": 4616}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-313.pyc", "path_type": "hardlink", "sha256": "1340f8a67960761aa63b680f610e57b393e5cdbeca3e9220b0a0566e45ca0950", "sha256_in_prefix": "1340f8a67960761aa63b680f610e57b393e5cdbeca3e9220b0a0566e45ca0950", "size_in_bytes": 14241}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-313.pyc", "path_type": "hardlink", "sha256": "b1bc763418caecdc67acdbc49683b89e3c51a115b7fa1cf8c206b050cea0529f", "sha256_in_prefix": "b1bc763418caecdc67acdbc49683b89e3c51a115b7fa1cf8c206b050cea0529f", "size_in_bytes": 3341}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-313.pyc", "path_type": "hardlink", "sha256": "d707483ce87746de67989b6cb3db320abd464532d4554186d83619385ad25274", "sha256_in_prefix": "d707483ce87746de67989b6cb3db320abd464532d4554186d83619385ad25274", "size_in_bytes": 8087}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-313.pyc", "path_type": "hardlink", "sha256": "f633aae23b708a28391509bb11bc7d4fea0e451cac062d592938ef5fe35151d7", "sha256_in_prefix": "f633aae23b708a28391509bb11bc7d4fea0e451cac062d592938ef5fe35151d7", "size_in_bytes": 10775}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-313.pyc", "path_type": "hardlink", "sha256": "8be68c27e4ca601f8b4720f3bba78e812ebe96966da44fd0727949cc0af6985b", "sha256_in_prefix": "8be68c27e4ca601f8b4720f3bba78e812ebe96966da44fd0727949cc0af6985b", "size_in_bytes": 4666}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-313.pyc", "path_type": "hardlink", "sha256": "c19850b60f3e769250bdc6d71853a1d873631c66f1b244834ad8cbe5e3310811", "sha256_in_prefix": "c19850b60f3e769250bdc6d71853a1d873631c66f1b244834ad8cbe5e3310811", "size_in_bytes": 38163}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-313.pyc", "path_type": "hardlink", "sha256": "5f39fc58249b1ff5aaaf443b19ea8fa9b79d53ecac22ff674ea679cffa99728f", "sha256_in_prefix": "5f39fc58249b1ff5aaaf443b19ea8fa9b79d53ecac22ff674ea679cffa99728f", "size_in_bytes": 21992}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-313.pyc", "path_type": "hardlink", "sha256": "7e4a5b5814d78454fefb9efe99133c42c4d4d273acbccbe33845c1f32aba7c56", "sha256_in_prefix": "7e4a5b5814d78454fefb9efe99133c42c4d4d273acbccbe33845c1f32aba7c56", "size_in_bytes": 7431}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-313.pyc", "path_type": "hardlink", "sha256": "c7adab49b545b7090ea00e8ac3e5fef3d4cd1178cee20e3df54593500f82d019", "sha256_in_prefix": "c7adab49b545b7090ea00e8ac3e5fef3d4cd1178cee20e3df54593500f82d019", "size_in_bytes": 19492}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "dee22c64da33a896c6ddfffc63de29db50364249d46695e330ad7ffdf6eef4b3", "sha256_in_prefix": "dee22c64da33a896c6ddfffc63de29db50364249d46695e330ad7ffdf6eef4b3", "size_in_bytes": 22227}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-313.pyc", "path_type": "hardlink", "sha256": "9a874117639dd4f38b3b8b9d85847540169e4fe71a1f8755eca6d8226c9e0385", "sha256_in_prefix": "9a874117639dd4f38b3b8b9d85847540169e4fe71a1f8755eca6d8226c9e0385", "size_in_bytes": 5081}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "e8c0d6657a0ffdb70d7c0e991669fab39f289ea8a5e8a0711029deb3af76f9fc", "sha256_in_prefix": "e8c0d6657a0ffdb70d7c0e991669fab39f289ea8a5e8a0711029deb3af76f9fc", "size_in_bytes": 168}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-313.pyc", "path_type": "hardlink", "sha256": "c2f458b15914230367e9391f180600357b2bb807259d2d065e324b7e0bf99a7a", "sha256_in_prefix": "c2f458b15914230367e9391f180600357b2bb807259d2d065e324b7e0bf99a7a", "size_in_bytes": 483}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "lib/python3.13/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "lib/python3.13/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "lib/python3.13/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "lib/python3.13/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "lib/python3.13/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "lib/python3.13/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "lib/python3.13/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "34524cbdac82219bac11f067c71b7e9b07e6a74de3b1a434d9e602dc4a6b930d", "sha256_in_prefix": "34524cbdac82219bac11f067c71b7e9b07e6a74de3b1a434d9e602dc4a6b930d", "size_in_bytes": 626}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-313.pyc", "path_type": "hardlink", "sha256": "f0ba98fac669746a06e523224b0b9bca4a9dd5d82834665a05849cf6b849652f", "sha256_in_prefix": "f0ba98fac669746a06e523224b0b9bca4a9dd5d82834665a05849cf6b849652f", "size_in_bytes": 6529}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/alias.cpython-313.pyc", "path_type": "hardlink", "sha256": "fd74e916e00c088181680f735f2add750c0552fec615ecfb0d5e321c3aa297b6", "sha256_in_prefix": "fd74e916e00c088181680f735f2add750c0552fec615ecfb0d5e321c3aa297b6", "size_in_bytes": 3605}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-313.pyc", "path_type": "hardlink", "sha256": "59c88fe9a6dd4a9147fcda83796c75f60fcc343f28c824035abbcdef4998ef60", "sha256_in_prefix": "59c88fe9a6dd4a9147fcda83796c75f60fcc343f28c824035abbcdef4998ef60", "size_in_bytes": 24907}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-313.pyc", "path_type": "hardlink", "sha256": "740dfff983327af3390d2e27cbb73f1de3114b8d8dce660643a5a6f6f9ea1b55", "sha256_in_prefix": "740dfff983327af3390d2e27cbb73f1de3114b8d8dce660643a5a6f6f9ea1b55", "size_in_bytes": 2105}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "5560f7b3b93f47fd77238205a8c1a9c18916f3e6bda195c37cdf3e170c361c0a", "sha256_in_prefix": "5560f7b3b93f47fd77238205a8c1a9c18916f3e6bda195c37cdf3e170c361c0a", "size_in_bytes": 27012}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/build.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a9c3c14fab53723d6b9fdd9c20f196985ba323d7e0debf09ddaede88e7ad2fb", "sha256_in_prefix": "4a9c3c14fab53723d6b9fdd9c20f196985ba323d7e0debf09ddaede88e7ad2fb", "size_in_bytes": 5196}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/build_clib.cpython-313.pyc", "path_type": "hardlink", "sha256": "f983e9e071a575f5fe3eacfc31df4759736d00d81927275008a0e5a4f96c7216", "sha256_in_prefix": "f983e9e071a575f5fe3eacfc31df4759736d00d81927275008a0e5a4f96c7216", "size_in_bytes": 3836}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/build_ext.cpython-313.pyc", "path_type": "hardlink", "sha256": "84c5c3ea8dd15b100c5449e9bbafc1e4106557715cf9455dfa101e75b05e14ac", "sha256_in_prefix": "84c5c3ea8dd15b100c5449e9bbafc1e4106557715cf9455dfa101e75b05e14ac", "size_in_bytes": 23460}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/build_py.cpython-313.pyc", "path_type": "hardlink", "sha256": "c891fc7fa9998cee31c8e3b8fd8b325c94ffa21855a91d0660aff891c304b5b4", "sha256_in_prefix": "c891fc7fa9998cee31c8e3b8fd8b325c94ffa21855a91d0660aff891c304b5b4", "size_in_bytes": 22153}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/develop.cpython-313.pyc", "path_type": "hardlink", "sha256": "9926db91cbaec01befcbe5e8162d1796bcc21c9c9ce9c15f4ee5134b0ec394b0", "sha256_in_prefix": "9926db91cbaec01befcbe5e8162d1796bcc21c9c9ce9c15f4ee5134b0ec394b0", "size_in_bytes": 10181}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/dist_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a236bea696139a8bd5968e544bbf98a244da1848385145e03d659b90785259e", "sha256_in_prefix": "4a236bea696139a8bd5968e544bbf98a244da1848385145e03d659b90785259e", "size_in_bytes": 5235}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/easy_install.cpython-313.pyc", "path_type": "hardlink", "sha256": "bab562aaae6d62a2541cf82b4424caefed01807b567fc78f928f8333019bb368", "sha256_in_prefix": "bab562aaae6d62a2541cf82b4424caefed01807b567fc78f928f8333019bb368", "size_in_bytes": 112742}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "01b9451279b8591fe74e1d2de61bc79e49435fcfb52e5583045179246b14f407", "sha256_in_prefix": "01b9451279b8591fe74e1d2de61bc79e49435fcfb52e5583045179246b14f407", "size_in_bytes": 49551}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/egg_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "c329e56e62e616fc63e3f69fd7291fe7a492b6fd90425482658e1b9b4e1ebb66", "sha256_in_prefix": "c329e56e62e616fc63e3f69fd7291fe7a492b6fd90425482658e1b9b4e1ebb66", "size_in_bytes": 34678}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/install.cpython-313.pyc", "path_type": "hardlink", "sha256": "388b754082be66cc09d7f8f1e5d18d35e13fe06a1ad8beb6aff11bd506a4ae37", "sha256_in_prefix": "388b754082be66cc09d7f8f1e5d18d35e13fe06a1ad8beb6aff11bd506a4ae37", "size_in_bytes": 7815}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "1e7f3f4494ce8caf90a7489e8f6e249e9d0528de8519a19b0057888a18bcc7e4", "sha256_in_prefix": "1e7f3f4494ce8caf90a7489e8f6e249e9d0528de8519a19b0057888a18bcc7e4", "size_in_bytes": 3873}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/install_lib.cpython-313.pyc", "path_type": "hardlink", "sha256": "186ce9b285e97f1a0a44ae5c126d73c341c3194e662762ceadf48a345436df71", "sha256_in_prefix": "186ce9b285e97f1a0a44ae5c126d73c341c3194e662762ceadf48a345436df71", "size_in_bytes": 6151}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/install_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "b6d68857767fd16e22709b65cd031227f939e3a1890151ca2d225ad850ce1030", "sha256_in_prefix": "b6d68857767fd16e22709b65cd031227f939e3a1890151ca2d225ad850ce1030", "size_in_bytes": 4026}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/rotate.cpython-313.pyc", "path_type": "hardlink", "sha256": "1d5ee465cec49927bfb606cd222fe136d0a7f0422a87f4bbadb00fc70f8a6bdf", "sha256_in_prefix": "1d5ee465cec49927bfb606cd222fe136d0a7f0422a87f4bbadb00fc70f8a6bdf", "size_in_bytes": 3832}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/saveopts.cpython-313.pyc", "path_type": "hardlink", "sha256": "cdbb5e11b4965b663b0ec3c37e89db2965186df15a6684996688e359365cdaa9", "sha256_in_prefix": "cdbb5e11b4965b663b0ec3c37e89db2965186df15a6684996688e359365cdaa9", "size_in_bytes": 1295}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/sdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "0fa6ba623e3a7156e6ba4d927598f7ab0716da1668096420423586c78c2ead41", "sha256_in_prefix": "0fa6ba623e3a7156e6ba4d927598f7ab0716da1668096420423586c78c2ead41", "size_in_bytes": 12121}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/setopt.cpython-313.pyc", "path_type": "hardlink", "sha256": "dc168a9a35ebb9772dc1c71cf0b9fbf54bda354516d5705dd152f67990ed2938", "sha256_in_prefix": "dc168a9a35ebb9772dc1c71cf0b9fbf54bda354516d5705dd152f67990ed2938", "size_in_bytes": 7212}, {"_path": "lib/python3.13/site-packages/setuptools/command/__pycache__/test.cpython-313.pyc", "path_type": "hardlink", "sha256": "fae35124c921e03145a59ddb1c173bfed81489012bc6d33589dac21135c9c289", "sha256_in_prefix": "fae35124c921e03145a59ddb1c173bfed81489012bc6d33589dac21135c9c289", "size_in_bytes": 1907}, {"_path": "lib/python3.13/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "lib/python3.13/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "lib/python3.13/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "lib/python3.13/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "lib/python3.13/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "sha256_in_prefix": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "size_in_bytes": 22246}, {"_path": "lib/python3.13/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "lib/python3.13/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "lib/python3.13/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "lib/python3.13/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "lib/python3.13/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "lib/python3.13/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "lib/python3.13/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "lib/python3.13/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "sha256_in_prefix": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "size_in_bytes": 35624}, {"_path": "lib/python3.13/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "lib/python3.13/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "sha256_in_prefix": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "size_in_bytes": 7046}, {"_path": "lib/python3.13/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "lib/python3.13/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "lib/python3.13/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "lib/python3.13/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "lib/python3.13/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "lib/python3.13/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "lib/python3.13/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "lib/python3.13/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "lib/python3.13/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "4202117001ce084bca63265c7394d3c4c071c027b1815d85e4f83ad0f5ebcc98", "sha256_in_prefix": "4202117001ce084bca63265c7394d3c4c071c027b1815d85e4f83ad0f5ebcc98", "size_in_bytes": 155}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__pycache__/py310.cpython-313.pyc", "path_type": "hardlink", "sha256": "9f2b97ca3653ccaae0de27d9d75f91897a7b9238a75b929106fd936196927126", "sha256_in_prefix": "9f2b97ca3653ccaae0de27d9d75f91897a7b9238a75b929106fd936196927126", "size_in_bytes": 311}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__pycache__/py311.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a4656cf4d791857e97272bf412b7ae703eb2698c76db519e32291a07fcb2e43", "sha256_in_prefix": "4a4656cf4d791857e97272bf412b7ae703eb2698c76db519e32291a07fcb2e43", "size_in_bytes": 1408}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__pycache__/py312.cpython-313.pyc", "path_type": "hardlink", "sha256": "48d1691a3783e0c49471f6ccb2fec7deab1190b92b97224be9b1bf3fc8bff1b1", "sha256_in_prefix": "48d1691a3783e0c49471f6ccb2fec7deab1190b92b97224be9b1bf3fc8bff1b1", "size_in_bytes": 440}, {"_path": "lib/python3.13/site-packages/setuptools/compat/__pycache__/py39.cpython-313.pyc", "path_type": "hardlink", "sha256": "9a7eb5e1a2419967d4d0cb2605d8e7ec9b5879102a66d4dd35f47cd466f89f9b", "sha256_in_prefix": "9a7eb5e1a2419967d4d0cb2605d8e7ec9b5879102a66d4dd35f47cd466f89f9b", "size_in_bytes": 286}, {"_path": "lib/python3.13/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "lib/python3.13/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "lib/python3.13/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "lib/python3.13/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "lib/python3.13/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "lib/python3.13/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "lib/python3.13/site-packages/setuptools/config/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "3ec4706aa7f41a3d1af8052d3b4e5e344b475a72b99245313f5c86eafc946dbf", "sha256_in_prefix": "3ec4706aa7f41a3d1af8052d3b4e5e344b475a72b99245313f5c86eafc946dbf", "size_in_bytes": 1980}, {"_path": "lib/python3.13/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-313.pyc", "path_type": "hardlink", "sha256": "c8a3e625dc5bb81300111525e3168c1427556484a5da797a900cb8c38dfe38c9", "sha256_in_prefix": "c8a3e625dc5bb81300111525e3168c1427556484a5da797a900cb8c38dfe38c9", "size_in_bytes": 25536}, {"_path": "lib/python3.13/site-packages/setuptools/config/__pycache__/expand.cpython-313.pyc", "path_type": "hardlink", "sha256": "bb525dd5ce1aa5c0cb6f9a0f0bbefe2d229c43a3b841947af81b3b1c5785ec0e", "sha256_in_prefix": "bb525dd5ce1aa5c0cb6f9a0f0bbefe2d229c43a3b841947af81b3b1c5785ec0e", "size_in_bytes": 24771}, {"_path": "lib/python3.13/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-313.pyc", "path_type": "hardlink", "sha256": "86f5bff664339add507abf03999de52ea0c7c962b8a4fe69d42085a2a356ab7e", "sha256_in_prefix": "86f5bff664339add507abf03999de52ea0c7c962b8a4fe69d42085a2a356ab7e", "size_in_bytes": 23722}, {"_path": "lib/python3.13/site-packages/setuptools/config/__pycache__/setupcfg.cpython-313.pyc", "path_type": "hardlink", "sha256": "3d6c34b3557c4b0ee345d6decf0827b4cc4410ea41fb28484cdb2b37dab72331", "sha256_in_prefix": "3d6c34b3557c4b0ee345d6decf0827b4cc4410ea41fb28484cdb2b37dab72331", "size_in_bytes": 32670}, {"_path": "lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "0ae0045285adfcdfaa20560f7ca319a208414aa3d8bd79eaaabb9c6fb3b88431", "sha256_in_prefix": "0ae0045285adfcdfaa20560f7ca319a208414aa3d8bd79eaaabb9c6fb3b88431", "size_in_bytes": 1886}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-313.pyc", "path_type": "hardlink", "sha256": "1f16b8e9c1ffc67e4a240743cf969017e02381103fed626ae4c3fc6f56b91a8f", "sha256_in_prefix": "1f16b8e9c1ffc67e4a240743cf969017e02381103fed626ae4c3fc6f56b91a8f", "size_in_bytes": 18775}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-313.pyc", "path_type": "hardlink", "sha256": "94f59e0c9abd7b4157ccb2d96cc875d463c49b84f3aabb76092ca93d1b2b517d", "sha256_in_prefix": "94f59e0c9abd7b4157ccb2d96cc875d463c49b84f3aabb76092ca93d1b2b517d", "size_in_bytes": 3188}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-313.pyc", "path_type": "hardlink", "sha256": "93bb7f20aafcdf6c7e77a9312b6e7f5daa9c4b777a2774d830995e9eda1e651b", "sha256_in_prefix": "93bb7f20aafcdf6c7e77a9312b6e7f5daa9c4b777a2774d830995e9eda1e651b", "size_in_bytes": 2889}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-313.pyc", "path_type": "hardlink", "sha256": "59e13eccd4f3b8e6e432ad9e57bb10bf6ab557cc64922e268d8bcf0587fe2a9e", "sha256_in_prefix": "59e13eccd4f3b8e6e432ad9e57bb10bf6ab557cc64922e268d8bcf0587fe2a9e", "size_in_bytes": 241015}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-313.pyc", "path_type": "hardlink", "sha256": "c8dd2416393579545fd701d7397f601aab161dd0c534bf072b2c56ed0de36b23", "sha256_in_prefix": "c8dd2416393579545fd701d7397f601aab161dd0c534bf072b2c56ed0de36b23", "size_in_bytes": 18584}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "lib/python3.13/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "lib/python3.13/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "lib/python3.13/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "lib/python3.13/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "lib/python3.13/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "lib/python3.13/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "lib/python3.13/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "sha256_in_prefix": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "size_in_bytes": 44897}, {"_path": "lib/python3.13/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "lib/python3.13/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "lib/python3.13/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "lib/python3.13/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "lib/python3.13/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "lib/python3.13/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "lib/python3.13/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "lib/python3.13/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "lib/python3.13/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "lib/python3.13/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "lib/python3.13/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "lib/python3.13/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "lib/python3.13/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "lib/python3.13/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "lib/python3.13/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "sha256_in_prefix": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "size_in_bytes": 40519}, {"_path": "lib/python3.13/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "lib/python3.13/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "lib/python3.13/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "1e541b31d3a03565cb95d7331da45ce22741c7611086293f0826bd1df7a981e9", "sha256_in_prefix": "1e541b31d3a03565cb95d7331da45ce22741c7611086293f0826bd1df7a981e9", "size_in_bytes": 655}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/contexts.cpython-313.pyc", "path_type": "hardlink", "sha256": "41e391e697c43da77a4ae9c455c37a188764b162536ce42854caf479ccef39cd", "sha256_in_prefix": "41e391e697c43da77a4ae9c455c37a188764b162536ce42854caf479ccef39cd", "size_in_bytes": 7166}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/environment.cpython-313.pyc", "path_type": "hardlink", "sha256": "d3e14b74a4505fde3aedab42ce84b46bba28f79ac6677e0d0e476d83ffd5a74f", "sha256_in_prefix": "d3e14b74a4505fde3aedab42ce84b46bba28f79ac6677e0d0e476d83ffd5a74f", "size_in_bytes": 3616}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/fixtures.cpython-313.pyc", "path_type": "hardlink", "sha256": "3be6949c2be9d34f8e57d1f33b942b620a13dfb035e68608b9de94e44d81ebaf", "sha256_in_prefix": "3be6949c2be9d34f8e57d1f33b942b620a13dfb035e68608b9de94e44d81ebaf", "size_in_bytes": 7232}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-313.pyc", "path_type": "hardlink", "sha256": "7c508b7bdc04648cf22219d565645dbf476094ea71a238f2787d83793ecf0d49", "sha256_in_prefix": "7c508b7bdc04648cf22219d565645dbf476094ea71a238f2787d83793ecf0d49", "size_in_bytes": 189}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/namespaces.cpython-313.pyc", "path_type": "hardlink", "sha256": "56edd29c90888cc2503fba7db97cf73ac7bf7bc3d8819b51d899afebfcf938a9", "sha256_in_prefix": "56edd29c90888cc2503fba7db97cf73ac7bf7bc3d8819b51d899afebfcf938a9", "size_in_bytes": 4086}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-313.pyc", "path_type": "hardlink", "sha256": "b91886391a0743f55149676bc5d2100d2cd8dfd72d23663d59ec969b712034b1", "sha256_in_prefix": "b91886391a0743f55149676bc5d2100d2cd8dfd72d23663d59ec969b712034b1", "size_in_bytes": 183}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/server.cpython-313.pyc", "path_type": "hardlink", "sha256": "b9ede151e2e284d54f5ede03f9696431238a7741a62c21bbfbea3e79a9404a72", "sha256_in_prefix": "b9ede151e2e284d54f5ede03f9696431238a7741a62c21bbfbea3e79a9404a72", "size_in_bytes": 5022}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-313.pyc", "path_type": "hardlink", "sha256": "d5da12fa0c493f0c88944acf4b0ea3b4407f7a48dc687b14017c08b470dcdc2a", "sha256_in_prefix": "d5da12fa0c493f0c88944acf4b0ea3b4407f7a48dc687b14017c08b470dcdc2a", "size_in_bytes": 1812}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-313.pyc", "path_type": "hardlink", "sha256": "3eb3cfc588a3e234819ea985fa288c4b177c7db5d6fb7ae1f4d84b23b0fb0195", "sha256_in_prefix": "3eb3cfc588a3e234819ea985fa288c4b177c7db5d6fb7ae1f4d84b23b0fb0195", "size_in_bytes": 1530}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-313.pyc", "path_type": "hardlink", "sha256": "ca1af4f70e0449d97208c924b6112b7337af3d32392cc5dc74fadcfccf37a88a", "sha256_in_prefix": "ca1af4f70e0449d97208c924b6112b7337af3d32392cc5dc74fadcfccf37a88a", "size_in_bytes": 4215}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "ed8631bf1e9b3311b647591695933664ff1770b14567ae37062dd213cc41e8c5", "sha256_in_prefix": "ed8631bf1e9b3311b647591695933664ff1770b14567ae37062dd213cc41e8c5", "size_in_bytes": 33336}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build.cpython-313.pyc", "path_type": "hardlink", "sha256": "7029cd1025183d792fc1127de8bc27c05b52ce54f7d181f5a656d9eb797e5294", "sha256_in_prefix": "7029cd1025183d792fc1127de8bc27c05b52ce54f7d181f5a656d9eb797e5294", "size_in_bytes": 1621}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-313.pyc", "path_type": "hardlink", "sha256": "c9563232d903febaa6d37f63350529cade4346809cca4cade269fc0c4a2e6eae", "sha256_in_prefix": "c9563232d903febaa6d37f63350529cade4346809cca4cade269fc0c4a2e6eae", "size_in_bytes": 4215}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-313.pyc", "path_type": "hardlink", "sha256": "94e889371a4ed0f1910a220522702627e1c36a3d4580791da3135d052aac88c2", "sha256_in_prefix": "94e889371a4ed0f1910a220522702627e1c36a3d4580791da3135d052aac88c2", "size_in_bytes": 13395}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-313.pyc", "path_type": "hardlink", "sha256": "55c9e19b68354676ce15eabc6d328817724f7d67bb62fbb993555a4e952f44d8", "sha256_in_prefix": "55c9e19b68354676ce15eabc6d328817724f7d67bb62fbb993555a4e952f44d8", "size_in_bytes": 44875}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-313.pyc", "path_type": "hardlink", "sha256": "e9cd82f88c7b4d7617eaea6a056a07ee64d699be813ca21c3bb82b76cbe912f0", "sha256_in_prefix": "e9cd82f88c7b4d7617eaea6a056a07ee64d699be813ca21c3bb82b76cbe912f0", "size_in_bytes": 17069}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-313.pyc", "path_type": "hardlink", "sha256": "bfbf88583a693f044118e46368b92e60daa84e29213d371eccce45d442dabd40", "sha256_in_prefix": "bfbf88583a693f044118e46368b92e60daa84e29213d371eccce45d442dabd40", "size_in_bytes": 30678}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-313.pyc", "path_type": "hardlink", "sha256": "a2541ed442d4dd84267af3579279bc8ca977b24e2b1749bfd4cbe14382df74b4", "sha256_in_prefix": "a2541ed442d4dd84267af3579279bc8ca977b24e2b1749bfd4cbe14382df74b4", "size_in_bytes": 23029}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_depends.cpython-313.pyc", "path_type": "hardlink", "sha256": "90c541b7c86f23909ef03f16076a7eb547636706e8f1a3cc6c3408c2962172a6", "sha256_in_prefix": "90c541b7c86f23909ef03f16076a7eb547636706e8f1a3cc6c3408c2962172a6", "size_in_bytes": 938}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_develop.cpython-313.pyc", "path_type": "hardlink", "sha256": "fba8d295bfec9b78476e0e6e907107c7b35f9b0886c853d406ad7b5626f3b4cd", "sha256_in_prefix": "fba8d295bfec9b78476e0e6e907107c7b35f9b0886c853d406ad7b5626f3b4cd", "size_in_bytes": 8713}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_dist.cpython-313.pyc", "path_type": "hardlink", "sha256": "fa57798b6347ab96c18e3a1acaf657ebbfd594d6591868e45388d47fe5c65018", "sha256_in_prefix": "fa57798b6347ab96c18e3a1acaf657ebbfd594d6591868e45388d47fe5c65018", "size_in_bytes": 10999}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "88fe5855b9c863e7a35d75546ed9b8e279177a3a8790223ca7eb1076aee1ddce", "sha256_in_prefix": "88fe5855b9c863e7a35d75546ed9b8e279177a3a8790223ca7eb1076aee1ddce", "size_in_bytes": 11172}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-313.pyc", "path_type": "hardlink", "sha256": "77d5ae90928cb51914dfa56d0396c23bdab489c3187c45f9c56665ee745f2f5b", "sha256_in_prefix": "77d5ae90928cb51914dfa56d0396c23bdab489c3187c45f9c56665ee745f2f5b", "size_in_bytes": 8015}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-313.pyc", "path_type": "hardlink", "sha256": "cf6e240fe26387d04b39a399d867c6635191ca9c48a33b1523d3d36b5aea95c3", "sha256_in_prefix": "cf6e240fe26387d04b39a399d867c6635191ca9c48a33b1523d3d36b5aea95c3", "size_in_bytes": 71184}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-313.pyc", "path_type": "hardlink", "sha256": "916318f122e66fc6d025c8a023708859c51f55e7852ba5a4d3ff494a20a579a7", "sha256_in_prefix": "916318f122e66fc6d025c8a023708859c51f55e7852ba5a4d3ff494a20a579a7", "size_in_bytes": 57954}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-313.pyc", "path_type": "hardlink", "sha256": "cd4c758ea3905cf12784ca23fcfd57b59949a3e9c1dc5bf1692706260e109fe9", "sha256_in_prefix": "cd4c758ea3905cf12784ca23fcfd57b59949a3e9c1dc5bf1692706260e109fe9", "size_in_bytes": 48035}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_extern.cpython-313.pyc", "path_type": "hardlink", "sha256": "76c06f59490ff3fc3fa8d7986005c72c0d56fd9f5465672a69904abe6eea0966", "sha256_in_prefix": "76c06f59490ff3fc3fa8d7986005c72c0d56fd9f5465672a69904abe6eea0966", "size_in_bytes": 819}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-313.pyc", "path_type": "hardlink", "sha256": "09a8ba2b1525634054a3586e7b81633f16cc4fe74b44e025aa4642ef998e8574", "sha256_in_prefix": "09a8ba2b1525634054a3586e7b81633f16cc4fe74b44e025aa4642ef998e8574", "size_in_bytes": 12079}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-313.pyc", "path_type": "hardlink", "sha256": "cd616245b50e55310234e9ca90e2a3d77966cea2e3ec14e9f3b1ecdc43bfa6d6", "sha256_in_prefix": "cd616245b50e55310234e9ca90e2a3d77966cea2e3ec14e9f3b1ecdc43bfa6d6", "size_in_bytes": 3839}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_glob.cpython-313.pyc", "path_type": "hardlink", "sha256": "4a632e6337c4eee07d6376794b017298cdd0dc584d1cfe34777940ee4c4f113f", "sha256_in_prefix": "4a632e6337c4eee07d6376794b017298cdd0dc584d1cfe34777940ee4c4f113f", "size_in_bytes": 1234}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-313.pyc", "path_type": "hardlink", "sha256": "437abe4d2f1243f2891e31d95985e13ce6be068c19264d6d48dba37a59f766d6", "sha256_in_prefix": "437abe4d2f1243f2891e31d95985e13ce6be068c19264d6d48dba37a59f766d6", "size_in_bytes": 6104}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_logging.cpython-313.pyc", "path_type": "hardlink", "sha256": "1ed5b7196c39d42efc6bcff8545c8149a9de5f6adc1bf0402348c757c6bebe6f", "sha256_in_prefix": "1ed5b7196c39d42efc6bcff8545c8149a9de5f6adc1bf0402348c757c6bebe6f", "size_in_bytes": 3218}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-313.pyc", "path_type": "hardlink", "sha256": "9403349ea4ee56dea6cdc244298fd5d1a4f1a7216e028f6605f38666743b719a", "sha256_in_prefix": "9403349ea4ee56dea6cdc244298fd5d1a4f1a7216e028f6605f38666743b719a", "size_in_bytes": 26969}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-313.pyc", "path_type": "hardlink", "sha256": "b392e7c51e2caf4b9526777ae1397cf298646d6b1b2121078979f6c54050ded5", "sha256_in_prefix": "b392e7c51e2caf4b9526777ae1397cf298646d6b1b2121078979f6c54050ded5", "size_in_bytes": 5485}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-313.pyc", "path_type": "hardlink", "sha256": "725f3f18184421b4db3a9ef84e775f37e151cf11b415523ee948eb6f18b595ac", "sha256_in_prefix": "725f3f18184421b4db3a9ef84e775f37e151cf11b415523ee948eb6f18b595ac", "size_in_bytes": 18709}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-313.pyc", "path_type": "hardlink", "sha256": "32312b158c6cb061e77a404df24a632bfcc11d8165d0dd2a68879b69da00b740", "sha256_in_prefix": "32312b158c6cb061e77a404df24a632bfcc11d8165d0dd2a68879b69da00b740", "size_in_bytes": 9785}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "5885c7c8b7771d82f73b8a2f1dc30e52e5a0716d497013661716b2d40d6472ff", "sha256_in_prefix": "5885c7c8b7771d82f73b8a2f1dc30e52e5a0716d497013661716b2d40d6472ff", "size_in_bytes": 46279}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-313.pyc", "path_type": "hardlink", "sha256": "c57fa6cce030f40467f7699bb6f5606822dfdc071233cec37bf5074cb4af0323", "sha256_in_prefix": "c57fa6cce030f40467f7699bb6f5606822dfdc071233cec37bf5074cb4af0323", "size_in_bytes": 2854}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-313.pyc", "path_type": "hardlink", "sha256": "1f8578fb15c0129e91edfa8e4178de54c6a97f2dac54ce8c84edd16c428f055b", "sha256_in_prefix": "1f8578fb15c0129e91edfa8e4178de54c6a97f2dac54ce8c84edd16c428f055b", "size_in_bytes": 18059}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-313.pyc", "path_type": "hardlink", "sha256": "71b1bf08b84888c0df61a55e0ca65ebba591824c7854971406eea6b74d708711", "sha256_in_prefix": "71b1bf08b84888c0df61a55e0ca65ebba591824c7854971406eea6b74d708711", "size_in_bytes": 1342}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-313.pyc", "path_type": "hardlink", "sha256": "750980176d0ad0dc3d88179b7f855732b23fb9a47d1d1fd10668694f042ec2b1", "sha256_in_prefix": "750980176d0ad0dc3d88179b7f855732b23fb9a47d1d1fd10668694f042ec2b1", "size_in_bytes": 764}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-313.pyc", "path_type": "hardlink", "sha256": "f4294757d9a264f29216781993519b9e1515bb9f8a32116fec74d6ce20ee0d90", "sha256_in_prefix": "f4294757d9a264f29216781993519b9e1515bb9f8a32116fec74d6ce20ee0d90", "size_in_bytes": 4319}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-313.pyc", "path_type": "hardlink", "sha256": "e9332d27df3d1a1946969e1ab9a9f59e054cec5292593f76457b304362ed3aa1", "sha256_in_prefix": "e9332d27df3d1a1946969e1ab9a9f59e054cec5292593f76457b304362ed3aa1", "size_in_bytes": 4165}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-313.pyc", "path_type": "hardlink", "sha256": "8d60673e0353917434ec65eb53737599b3152d40b53bc7b69edab4d7aab70aca", "sha256_in_prefix": "8d60673e0353917434ec65eb53737599b3152d40b53bc7b69edab4d7aab70aca", "size_in_bytes": 19249}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-313.pyc", "path_type": "hardlink", "sha256": "f54ffef959b52ea9b35cfbb15d99d68d83b62cdefe354c86915551c6bf10df4c", "sha256_in_prefix": "f54ffef959b52ea9b35cfbb15d99d68d83b62cdefe354c86915551c6bf10df4c", "size_in_bytes": 10276}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/text.cpython-313.pyc", "path_type": "hardlink", "sha256": "f587459801050f4e17dcf15cf04ed7f30265331214779e21aebd15d68fe72e7d", "sha256_in_prefix": "f587459801050f4e17dcf15cf04ed7f30265331214779e21aebd15d68fe72e7d", "size_in_bytes": 548}, {"_path": "lib/python3.13/site-packages/setuptools/tests/__pycache__/textwrap.cpython-313.pyc", "path_type": "hardlink", "sha256": "75494cb412fdd53bffe9008b632826ac29a2d939c0a26df0fb5595bf4b563ec2", "sha256_in_prefix": "75494cb412fdd53bffe9008b632826ac29a2d939c0a26df0fb5595bf4b563ec2", "size_in_bytes": 408}, {"_path": "lib/python3.13/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "c94e1b5ae81e8d74031a7d20542161c2f0c3be0f44ef340d74c58e840b501883", "sha256_in_prefix": "c94e1b5ae81e8d74031a7d20542161c2f0c3be0f44ef340d74c58e840b501883", "size_in_bytes": 161}, {"_path": "lib/python3.13/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-313.pyc", "path_type": "hardlink", "sha256": "18280f0c97701c037938d899b54e03ed6c55e75b1bf3e2bd6653bdad0ff5ee2b", "sha256_in_prefix": "18280f0c97701c037938d899b54e03ed6c55e75b1bf3e2bd6653bdad0ff5ee2b", "size_in_bytes": 338}, {"_path": "lib/python3.13/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "c79a4a403f68307a26def5cad09cd725de1af3d782e2313885613380198377f5", "sha256_in_prefix": "c79a4a403f68307a26def5cad09cd725de1af3d782e2313885613380198377f5", "size_in_bytes": 161}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-313.pyc", "path_type": "hardlink", "sha256": "9b15c85d09c5735a5b603648a0d52604be052e6423f52dcef2e1a4abeaf660ca", "sha256_in_prefix": "9b15c85d09c5735a5b603648a0d52604be052e6423f52dcef2e1a4abeaf660ca", "size_in_bytes": 40031}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-313.pyc", "path_type": "hardlink", "sha256": "0d6bcf8507105f47ad0cbac385002751da304fa67b6adb2f4533676837fc3816", "sha256_in_prefix": "0d6bcf8507105f47ad0cbac385002751da304fa67b6adb2f4533676837fc3816", "size_in_bytes": 11753}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-313.pyc", "path_type": "hardlink", "sha256": "5416ff5ff5c2b25111a5848f03a523c0104d4b7ed561c58fb88a90ea8d1dbb6d", "sha256_in_prefix": "5416ff5ff5c2b25111a5848f03a523c0104d4b7ed561c58fb88a90ea8d1dbb6d", "size_in_bytes": 15827}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-313.pyc", "path_type": "hardlink", "sha256": "3dedacf792ea8c59182eeb77e8dc0ac92f197dd9a728058899ea785d3d09fd15", "sha256_in_prefix": "3dedacf792ea8c59182eeb77e8dc0ac92f197dd9a728058899ea785d3d09fd15", "size_in_bytes": 4168}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-313.pyc", "path_type": "hardlink", "sha256": "9867d02799a600562eed8e864d017eac31263f6dbc51de1369609762b8be13a0", "sha256_in_prefix": "9867d02799a600562eed8e864d017eac31263f6dbc51de1369609762b8be13a0", "size_in_bytes": 43916}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "6da7e34af58bf84c693fa0f4ef9acad52b704a11a627fde070540316e4e62da5", "sha256_in_prefix": "6da7e34af58bf84c693fa0f4ef9acad52b704a11a627fde070540316e4e62da5", "size_in_bytes": 3122}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-313.pyc", "path_type": "hardlink", "sha256": "61a621a2980a59a61901f0befe2df3680e69b572b2f0c0dec70edc8748029c7d", "sha256_in_prefix": "61a621a2980a59a61901f0befe2df3680e69b572b2f0c0dec70edc8748029c7d", "size_in_bytes": 761}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "lib/python3.13/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "lib/python3.13/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "lib/python3.13/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "lib/python3.13/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "lib/python3.13/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "lib/python3.13/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-313.pyc", "path_type": "hardlink", "sha256": "9746dbf7b8107d6ceeda8a8e5cab7eecaccc342ca5bd3582ddd46125ff2e6f98", "sha256_in_prefix": "9746dbf7b8107d6ceeda8a8e5cab7eecaccc342ca5bd3582ddd46125ff2e6f98", "size_in_bytes": 166}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-313.pyc", "path_type": "hardlink", "sha256": "38737b4311972d3c0e3d872c945c92690fc6823b37cc6ca45883ffcceec68bc9", "sha256_in_prefix": "38737b4311972d3c0e3d872c945c92690fc6823b37cc6ca45883ffcceec68bc9", "size_in_bytes": 4790}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-313.pyc", "path_type": "hardlink", "sha256": "7355bf1ddaf84e7ab7449ddcf6f0b58c5f9e5ecd6a44e6559b956dea17e2bca3", "sha256_in_prefix": "7355bf1ddaf84e7ab7449ddcf6f0b58c5f9e5ecd6a44e6559b956dea17e2bca3", "size_in_bytes": 9161}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "lib/python3.13/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "lib/python3.13/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "lib/python3.13/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "lib/python3.13/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "lib/python3.13/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "sha256_in_prefix": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "size_in_bytes": 34118}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "sha256_in_prefix": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "size_in_bytes": 8901}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "sha256_in_prefix": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "size_in_bytes": 7077}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "sha256_in_prefix": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "size_in_bytes": 53534}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "sha256_in_prefix": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "size_in_bytes": 44866}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "lib/python3.13/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "lib/python3.13/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "lib/python3.13/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "lib/python3.13/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "lib/python3.13/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "lib/python3.13/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "lib/python3.13/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "lib/python3.13/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "f713f171ef50ad8da299614d2b5217091cb9e1269031252355f40f679e4db988", "size": 2327789, "subdir": "osx-64", "timestamp": 1746025886000, "url": "https://repo.anaconda.com/pkgs/main/osx-64/setuptools-78.1.1-py313hecd8cb5_0.conda", "version": "78.1.1"}