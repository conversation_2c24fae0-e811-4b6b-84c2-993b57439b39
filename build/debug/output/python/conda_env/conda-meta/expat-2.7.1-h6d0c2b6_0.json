{"build": "h6d0c2b6_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["libcxx >=14.0.6"], "extracted_package_dir": "/Users/<USER>/miniconda3/pkgs/expat-2.7.1-h6d0c2b6_0", "files": ["bin/xmlwf", "include/expat.h", "include/expat_config.h", "include/expat_external.h", "lib/cmake/expat-2.7.1/expat-config-version.cmake", "lib/cmake/expat-2.7.1/expat-config.cmake", "lib/cmake/expat-2.7.1/expat-noconfig.cmake", "lib/cmake/expat-2.7.1/expat.cmake", "lib/libexpat.1.10.2.dylib", "lib/libexpat.1.dylib", "lib/libexpat.a", "lib/libexpat.dylib", "lib/pkgconfig/expat.pc", "share/doc/expat/AUTHORS", "share/doc/expat/changelog", "share/man/man1/xmlwf.1"], "fn": "expat-2.7.1-h6d0c2b6_0.conda", "license": "MIT", "link": {"source": "/Users/<USER>/miniconda3/pkgs/expat-2.7.1-h6d0c2b6_0", "type": 1}, "md5": "6cdc93776b7551083854e7f106a62720", "name": "expat", "package_tarball_full_path": "/Users/<USER>/miniconda3/pkgs/expat-2.7.1-h6d0c2b6_0.conda", "paths_data": {"paths": [{"_path": "bin/xmlwf", "path_type": "hardlink", "sha256": "4064ad44774e399938962b4c8674d57a02132e274ad3842b10b087ec2ba950cb", "sha256_in_prefix": "4064ad44774e399938962b4c8674d57a02132e274ad3842b10b087ec2ba950cb", "size_in_bytes": 35636}, {"_path": "include/expat.h", "path_type": "hardlink", "sha256": "7c16a5cf0eea844ae579db083b8d75f23a71859cac77e3c4cb7a8fa3b7621685", "sha256_in_prefix": "7c16a5cf0eea844ae579db083b8d75f23a71859cac77e3c4cb7a8fa3b7621685", "size_in_bytes": 44120}, {"_path": "include/expat_config.h", "path_type": "hardlink", "sha256": "48d5117b7e8376d435be3e9792de46e7cf4f3448b109e53bc17491b386bd7db0", "sha256_in_prefix": "48d5117b7e8376d435be3e9792de46e7cf4f3448b109e53bc17491b386bd7db0", "size_in_bytes": 4171}, {"_path": "include/expat_external.h", "path_type": "hardlink", "sha256": "7ca9ed28dd5e08eac425931894fabd4c876db8f4be26f9b9a33c2e4f70a0d6c3", "sha256_in_prefix": "7ca9ed28dd5e08eac425931894fabd4c876db8f4be26f9b9a33c2e4f70a0d6c3", "size_in_bytes": 6029}, {"_path": "lib/cmake/expat-2.7.1/expat-config-version.cmake", "path_type": "hardlink", "sha256": "5f42039a32496ebd9143f675bd8b3acc2136c2fc1efc585e9b12d338c2485342", "sha256_in_prefix": "5f42039a32496ebd9143f675bd8b3acc2136c2fc1efc585e9b12d338c2485342", "size_in_bytes": 2762}, {"_path": "lib/cmake/expat-2.7.1/expat-config.cmake", "path_type": "hardlink", "sha256": "33708544410c5b5fc7d1a25d1700804719deb4e4cb57358d4d9d2e7328418bcb", "sha256_in_prefix": "33708544410c5b5fc7d1a25d1700804719deb4e4cb57358d4d9d2e7328418bcb", "size_in_bytes": 3637}, {"_path": "lib/cmake/expat-2.7.1/expat-noconfig.cmake", "path_type": "hardlink", "sha256": "dd781a62a9557d5016d7b55bc267ab432bf941f7473a0a2f84b1346afad4cd0c", "sha256_in_prefix": "dd781a62a9557d5016d7b55bc267ab432bf941f7473a0a2f84b1346afad4cd0c", "size_in_bytes": 850}, {"_path": "lib/cmake/expat-2.7.1/expat.cmake", "path_type": "hardlink", "sha256": "861fd3e8d6ab95b5a4cfad1779367553b1275339d475b9089bc1e1e4a03a737d", "sha256_in_prefix": "861fd3e8d6ab95b5a4cfad1779367553b1275339d475b9089bc1e1e4a03a737d", "size_in_bytes": 4135}, {"_path": "lib/libexpat.1.10.2.dylib", "path_type": "hardlink", "sha256": "208c44771e06d42469b59544a2a21eed5bf8860099e7a3fed4b8660da5bd895e", "sha256_in_prefix": "208c44771e06d42469b59544a2a21eed5bf8860099e7a3fed4b8660da5bd895e", "size_in_bytes": 180272}, {"_path": "lib/libexpat.1.dylib", "path_type": "softlink", "sha256": "208c44771e06d42469b59544a2a21eed5bf8860099e7a3fed4b8660da5bd895e", "size_in_bytes": 180272}, {"_path": "lib/libexpat.a", "path_type": "hardlink", "sha256": "897ab3a22c0029c7a439ffec1aac6521e52f4552650ecf29343017111eba79ea", "sha256_in_prefix": "897ab3a22c0029c7a439ffec1aac6521e52f4552650ecf29343017111eba79ea", "size_in_bytes": 211976}, {"_path": "lib/libexpat.dylib", "path_type": "softlink", "sha256": "208c44771e06d42469b59544a2a21eed5bf8860099e7a3fed4b8660da5bd895e", "size_in_bytes": 180272}, {"_path": "lib/pkgconfig/expat.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/sy/f16zz6x50xz3113nwtb9bvq00000gp/T/abs_982nw1aks2/croot/expat_1744659830450/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "5d9e399084c8ea41a41a9b50b6ce093c034e5b5ca99b78929580dc2a439a6bf5", "sha256_in_prefix": "ec20323d86075ec2016af64fa80193eb3c817d2264b8a6ba1687facd26bb7867", "size_in_bytes": 525}, {"_path": "share/doc/expat/AUTHORS", "path_type": "hardlink", "sha256": "59f14371c6b75912cfebb46e6247ee5146766e803a0365b124e5d3011e7d0877", "sha256_in_prefix": "59f14371c6b75912cfebb46e6247ee5146766e803a0365b124e5d3011e7d0877", "size_in_bytes": 142}, {"_path": "share/doc/expat/changelog", "path_type": "hardlink", "sha256": "9d8bb6625d503b969882fa15178b450ac0ccffdfdac1c720f74d92de243e3580", "sha256_in_prefix": "9d8bb6625d503b969882fa15178b450ac0ccffdfdac1c720f74d92de243e3580", "size_in_bytes": 84961}, {"_path": "share/man/man1/xmlwf.1", "path_type": "hardlink", "sha256": "3d7a6e32305cb05a677cf7576ce6715f23cb613d542671509ea1f7eca81f7f25", "sha256_in_prefix": "3d7a6e32305cb05a677cf7576ce6715f23cb613d542671509ea1f7eca81f7f25", "size_in_bytes": 10944}], "paths_version": 1}, "requested_spec": "None", "sha256": "6ce290bed73e72a7cbff720d69be5a7d767e1bad4767c106b96578611b45d063", "size": 153146, "subdir": "osx-64", "timestamp": 1744659891000, "url": "https://repo.anaconda.com/pkgs/main/osx-64/expat-2.7.1-h6d0c2b6_0.conda", "version": "2.7.1"}