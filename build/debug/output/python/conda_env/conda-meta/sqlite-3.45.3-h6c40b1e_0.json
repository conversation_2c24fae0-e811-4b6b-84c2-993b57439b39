{"build": "h6c40b1e_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["ncurses >=6.4,<7.0a0", "readline >=8.0,<9.0a0", "zlib >=1.2.13,<1.3.0a0", "zlib >=1.2.13,<2.0a0"], "extracted_package_dir": "/Users/<USER>/miniconda3/pkgs/sqlite-3.45.3-h6c40b1e_0", "files": ["bin/sqlite3", "include/sqlite3.h", "include/sqlite3ext.h", "lib/libsqlite3.0.dylib", "lib/libsqlite3.dylib", "lib/pkgconfig/sqlite3.pc", "share/man/man1/sqlite3.1"], "fn": "sqlite-3.45.3-h6c40b1e_0.conda", "license": "blessing", "link": {"source": "/Users/<USER>/miniconda3/pkgs/sqlite-3.45.3-h6c40b1e_0", "type": 1}, "md5": "2edf909b937b3aad48322c9cb2e8f1a0", "name": "sqlite", "package_tarball_full_path": "/Users/<USER>/miniconda3/pkgs/sqlite-3.45.3-h6c40b1e_0.conda", "paths_data": {"paths": [{"_path": "bin/sqlite3", "path_type": "hardlink", "sha256": "4da655022f477cdde659ca91a94878889dd20a32dcfb262f848ff70810c80ce8", "sha256_in_prefix": "4da655022f477cdde659ca91a94878889dd20a32dcfb262f848ff70810c80ce8", "size_in_bytes": 1964924}, {"_path": "include/sqlite3.h", "path_type": "hardlink", "sha256": "882ad3c0448d0324fb3a6b1a85333a9173d539ac669c9972ae1f03722ff86282", "sha256_in_prefix": "882ad3c0448d0324fb3a6b1a85333a9173d539ac669c9972ae1f03722ff86282", "size_in_bytes": 641889}, {"_path": "include/sqlite3ext.h", "path_type": "hardlink", "sha256": "b184dd1586d935133d37ad76fa353faf0a1021ff2fdedeedcc3498fff74bbb94", "sha256_in_prefix": "b184dd1586d935133d37ad76fa353faf0a1021ff2fdedeedcc3498fff74bbb94", "size_in_bytes": 38149}, {"_path": "lib/libsqlite3.0.dylib", "path_type": "hardlink", "sha256": "762f5bf3cf9d590a6ffda43e2e35a0c57e35ba81220e5f48d3aaff298c823a29", "sha256_in_prefix": "762f5bf3cf9d590a6ffda43e2e35a0c57e35ba81220e5f48d3aaff298c823a29", "size_in_bytes": 1636032}, {"_path": "lib/libsqlite3.dylib", "path_type": "softlink", "sha256": "762f5bf3cf9d590a6ffda43e2e35a0c57e35ba81220e5f48d3aaff298c823a29", "size_in_bytes": 1636032}, {"_path": "lib/pkgconfig/sqlite3.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/c_/qfmhj66j0tn016nkx_th4hxm0000gp/T/abs_69mdgu2uiz/croot/sqlite_1714488255326/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeho", "sha256": "a2be56642bbb7e7672b05ae10b677bb3095f83a5c75f421ccebdcbcb23e0240d", "sha256_in_prefix": "8f6683af233c32fe7eb38b28d2e83c189e1bb0e3ba84278d95f6c2e583e87b3a", "size_in_bytes": 512}, {"_path": "share/man/man1/sqlite3.1", "path_type": "hardlink", "sha256": "161127f79ba85a39c43520a33174d817a2465159ddf034aaddd4a696fe133564", "sha256_in_prefix": "161127f79ba85a39c43520a33174d817a2465159ddf034aaddd4a696fe133564", "size_in_bytes": 4340}], "paths_version": 1}, "requested_spec": "None", "sha256": "46fe0c9f52bed450d7769cc1dfe684e26434e2b84b3b42cdf5d7535af64399a4", "size": 1300211, "subdir": "osx-64", "timestamp": 1714488351000, "url": "https://repo.anaconda.com/pkgs/main/osx-64/sqlite-3.45.3-h6c40b1e_0.conda", "version": "3.45.3"}