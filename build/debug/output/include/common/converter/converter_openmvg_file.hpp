#pragma once

#include <string>
#include <vector>
#include <memory>
#include <po_core.hpp> // 包含PoSDK的核心定义

namespace PoSDK
{
    namespace Converter
    {

        /**
         * @brief OpenMVG文件转换器
         * @details 直接解析OpenMVG工具输出的文件，不依赖OpenMVG库
         */
        class OpenMVGFileConverter
        {
        public:
            /**
             * @brief 加载特征点文件
             * @param features_file 特征文件路径
             * @param features 输出特征点
             * @return 是否成功
             */
            static bool LoadFeatures(
                const std::string &features_file,
                std::vector<types::FeaturePoint> &features);

            /**
             * @brief 加载匹配文件
             * @param matches_file 匹配文件路径
             * @param matches 输出匹配信息
             * @return 是否成功
             */
            static bool LoadMatches(
                const std::string &matches_file,
                types::Matches &matches);

            /**
             * @brief 加载SfM数据文件
             * @param sfm_data_file SfM数据文件路径
             * @param image_paths 输出图像路径
             * @param views_only 是否只加载视图信息
             * @return 是否成功
             */
            static bool LoadSfMData(
                const std::string &sfm_data_file,
                std::vector<std::pair<types::IndexT, std::string>> &image_paths,
                bool views_only = true);

            /**
             * @brief 将OpenMVG的SfM数据文件转换为PoSDK的图像数据
             * @param sfm_data_file SfM数据文件路径
             * @param images_base_dir 图像基础目录
             * @param images_data 输出图像数据
             * @return 是否成功
             */
            static bool ToDataImages(
                const std::string &sfm_data_file,
                const std::string &images_base_dir,
                Interface::DataPtr &images_data);

            /**
             * @brief 将OpenMVG的特征文件转换为PoSDK的特征数据
             * @param sfm_data_file SfM数据文件路径
             * @param features_dir 特征目录
             * @param images_base_dir 图像基础目录
             * @param features_data 输出特征数据
             * @return 是否成功
             */
            static bool ToDataFeatures(
                const std::string &sfm_data_file,
                const std::string &features_dir,
                const std::string &images_base_dir,
                Interface::DataPtr &features_data);

            /**
             * @brief 将OpenMVG的匹配文件转换为PoSDK的匹配数据
             * @param matches_file 匹配文件路径
             * @param matches_data 输出匹配数据
             * @return 是否成功
             */
            static bool ToDataMatches(
                const std::string &matches_file,
                Interface::DataPtr &matches_data);

            /**
             * @brief 加载SfM数据文件中的全局位姿信息
             * @param sfm_data_file SfM数据文件路径
             * @param global_poses 输出全局位姿信息
             * @return 是否成功
             */
            static bool LoadSfMData(
                const std::string &sfm_data_file,
                types::GlobalPoses &global_poses);

            /**
             * @brief 将OpenMVG的SfM数据文件转换为PoSDK的全局位姿数据
             * @param sfm_data_file SfM数据文件路径
             * @param global_poses_data 输出全局位姿数据
             * @return 是否成功
             */
            static bool ToDataGlobalPoses(
                const std::string &sfm_data_file,
                Interface::DataPtr &global_poses_data);
        };

    } // namespace Converter
} // namespace PoSDK
