/**
 * @file converter_opengv.hpp
 * @brief OpenGV数据类型转换器
 * @details 提供PoMVG与OpenGV数据类型之间的转换功能
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _CONVERTER_OPENGV_
#define _CONVERTER_OPENGV_

#include "converter_base.hpp"
#include <po_core/types.hpp>
#include <po_core/interfaces_robust_estimator.hpp>
#include <opengv/types.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include <Eigen/Core>
#include <memory>

namespace PoSDK
{
    namespace Converter
    {

        class OpenGVConverter : public ConverterBase
        {
        public:
            /**
             * @brief 将PoMVG匹配和特征数据转换为OpenGV的bearing vectors
             * @param matches 特征匹配
             * @param features_info 特征信息
             * @param camera_models 相机模型集合
             * @param view_pair 视图对
             * @param[out] bearingVectors1 第一帧的单位向量
             * @param[out] bearingVectors2 第二帧的单位向量
             * @return 转换是否成功
             */
            static bool MatchesToBearingVectors(
                const IdMatches &matches,
                const FeaturesInfo &features_info,
                const CameraModels &camera_models,
                const ViewPair &view_pair,
                opengv::bearingVectors_t &bearingVectors1,
                opengv::bearingVectors_t &bearingVectors2);

            /**
             * @brief 将PoMVG匹配样本转换为OpenGV的bearing vectors
             * @param matches_sample 匹配样本
             * @param features_info 特征信息
             * @param camera_models 相机模型集合
             * @param view_pair 视图对
             * @param[out] bearingVectors1 第一帧的单位向量
             * @param[out] bearingVectors2 第二帧的单位向量
             * @return 转换是否成功
             */
            static bool MatchesToBearingVectors(
                const DataSample<IdMatches> &matches_sample,
                const FeaturesInfo &features_info,
                const CameraModels &camera_models,
                const ViewPair &view_pair,
                opengv::bearingVectors_t &bearingVectors1,
                opengv::bearingVectors_t &bearingVectors2);

            /**
             * @brief OpenGV位姿结果转换为PoMVG相对位姿
             * @param T OpenGV的变换矩阵
             * @param[out] relative_pose PoMVG的相对位姿
             * @return 转换是否成功
             */
            static bool OpenGVPose2RelativePose(
                const opengv::transformation_t &T,
                RelativePose &relative_pose);

            /**
             * @brief 将PoMVG相机内参转换为OpenGV相机参数
             * @param camera_model PoMVG相机模型
             * @param[out] K OpenGV相机内参矩阵
             * @return 转换是否成功
             */
            static bool CameraModel2OpenGVCalibration(
                const CameraModel &camera_model,
                Eigen::Matrix3d &K);

            /**
             * @brief 将像素坐标转换为单位向量
             * @param pixel_coord 像素坐标
             * @param camera_model 相机模型
             * @return 单位向量
             */
            static opengv::bearingVector_t
            PixelToBearingVector(
                const Vector2d &pixel_coord,
                const CameraModel &camera_model);

            /**
             * @brief 检查像素坐标是否在图像范围内
             */
            static bool IsPixelInImage(
                const Vector2d &pixel_coord,
                const CameraModel &camera_model);

            /**
             * @brief 将BearingPairs转换为BearingVectors
             * @param bearing_pairs BearingPairs类型数据
             * @param bearing_vectors1 输出BearingVectors1
             * @param bearing_vectors2 输出BearingVectors2
             */
            static void BearingPairsToBearingVectors(const BearingPairs &bearing_pairs, BearingVectors &bearing_vectors1, BearingVectors &bearing_vectors2);

            /**
             * @brief 将BearingVectors转换为BearingPairs
             * @param bearing_vectors1 BearingVectors1类型数据
             * @param bearing_vectors2 BearingVectors2类型数据
             * @param bearing_pairs 输出BearingPairs
             */
            static void BearingVectorsToBearingPairs(const BearingVectors &bearing_vectors1, const BearingVectors &bearing_vectors2, BearingPairs &bearing_pairs);
        };

    } // namespace Converter
} // namespace PoSDK

#endif // _CONVERTER_OPENGV_
