//=========================== BehaviorPrest Section ==================================
// # Copyright (c) 2021 PO tools author: Qi <PERSON>.
// common tools for transformation between openMVG's and PoSDK's data type
//====================================================================================

#ifndef _OPENMVG_CONVERTER_
#define _OPENMVG_CONVERTER_

// 首先包含OpenMVG的头文件，确保它们的特化先生效
#include "openmvg_precomp/eigen_alias_definition.hpp"
#include "openMVG/matching/indMatch.hpp"          // 待转换，仅保留核心数据结构体，并实现一样的Save/Load
#include "openMVG/features/feature_container.hpp" // 待转换，仅保留核心数据结构体，并实现一样的Save/Load
#include "openMVG/tracks/tracks.hpp"              // 待转换，仅保留核心数据结构体，并实现一样的Save/Load
#include "openMVG/sfm/sfm_landmark.hpp"           // 待转换，仅保留核心数据结构体，并实现一样的Save/Load
#include "openMVG/sfm/sfm_data.hpp"               // 待转换，仅保留核心数据结构体，并实现一样的Save/Load

// 然后包含PoMVG的头文件
#include <po_core.hpp>

namespace PoSDK
{
    namespace Converter
    {

        // 仅保留需要的命名空间引用，避免冲突
        using namespace Interface;
        // 避免使用全局命名空间导入，防止冲突
        // using namespace PoSDK::types;
        // using namespace openMVG;
        // using namespace openMVG::matching;
        // using namespace openMVG::features;
        // using namespace openMVG::tracks;
        // using namespace openMVG::sfm;

        // ------------------------------------------------------------------------------------------------
        //     converters: OpenMVG >> PoSDK
        // ------------------------------------------------------------------------------------------------
        /**
         * @brief OpenMVG特征点映射类型定义，用于存储每个视图的特征点集合
         * 键为视图ID，值为该视图下的所有特征点
         */
        typedef openMVG::Hash_Map<openMVG::IndexT, openMVG::features::PointFeatures> FeatsPerView;

        /**
         * @brief 将OpenMVG格式的匹配转换为PoMVG格式
         * @param pairWise_matches OpenMVG的成对匹配数据
         * @param data_matches_ptr 输出的PoMVG匹配数据指针
         * @return 转换是否成功
         */
        bool MatchesOpenMVG2PoSDK(const openMVG::matching::PairWiseMatches &pairWise_matches,
                                  DataPtr &data_matches_ptr);

        /**
         * @brief 将OpenMVG格式的匹配转换为PoMVG格式
         * @param pairWise_matches OpenMVG的成对匹配数据
         * @param matches 输出的PoMVG匹配信息引用
         * @return 转换是否成功
         */
        bool MatchesOpenMVG2PoSDK(const openMVG::matching::PairWiseMatches &pairWise_matches,
                                  types::Matches &matches);

        /**
         * @brief 从OpenMVG的特征点和匹配信息创建PoMVG的轨迹
         * @param feats_per_view OpenMVG的每个视图的特征点集合
         * @param pairWise_matches OpenMVG的成对匹配数据
         * @param tracks 输出的PoMVG轨迹信息引用
         */
        void TracksOpenMVG2PoSDK(const FeatsPerView &feats_per_view,
                                 const openMVG::matching::PairWiseMatches &pairWise_matches,
                                 types::Tracks &tracks);

        /**
         * @brief 从OpenMVG的特征点和轨迹映射创建PoMVG的轨迹
         * @param feats_per_view OpenMVG的每个视图的特征点集合
         * @param map_tracks OpenMVG的轨迹映射数据
         * @param tracks 输出的PoMVG轨迹信息引用
         */
        void TracksOpenMVG2PoSDK(const FeatsPerView &feats_per_view,
                                 const openMVG::tracks::STLMAPTracks &map_tracks,
                                 types::Tracks &tracks);

        /**
         * @brief 从OpenMVG的三维点云数据创建PoMVG的轨迹
         * @param landmarks OpenMVG的三维点云数据
         * @param tracks 输出的PoMVG轨迹信息引用
         */
        void TracksOpenMVG2PoSDK(const openMVG::sfm::Landmarks &landmarks,
                                 types::Tracks &tracks);

        /**
         * @brief 从OpenMVG的特征文件中读取特征点并转换为PoMVG格式
         * @param sfm_data OpenMVG的SfM数据对象，包含视图和图像信息
         * @param features_dir 特征文件所在目录
         * @param images_base_dir 图像文件的基础目录，用于构建绝对路径
         * @param features_data_ptr 输出的PoMVG特征数据指针
         * @return 转换是否成功
         */
        bool FeaturesOpenMVG2PoSDK(
            const openMVG::sfm::SfM_Data &sfm_data,
            const std::string &features_dir,
            const std::string &images_base_dir,
            DataPtr &features_data_ptr);

        /**
         * @brief 从OpenMVG的特征文件中读取特征点并转换为PoMVG格式
         * @param sfm_data OpenMVG的SfM数据对象，包含视图和图像信息
         * @param features_dir 特征文件所在目录
         * @param images_base_dir 图像文件的基础目录，用于构建绝对路径
         * @param features_info 输出的PoMVG特征信息引用
         * @return 转换是否成功
         */
        bool FeaturesOpenMVG2PoSDK(
            const openMVG::sfm::SfM_Data &sfm_data,
            const std::string &features_dir,
            const std::string &images_base_dir,
            types::FeaturesInfo &features_info);

        /**
         * @brief 直接从OpenMVG的特征文件读取并转换为PoMVG格式
         * @param sfm_data_file OpenMVG的SfM数据文件路径
         * @param features_dir 特征文件所在目录
         * @param images_base_dir 图像文件的基础目录，用于构建绝对路径
         * @param features_data_ptr 输出的PoMVG特征数据指针
         * @return 转换是否成功
         */
        bool FeaturesOpenMVGFile2PoSDK(
            const std::string &sfm_data_file,
            const std::string &features_dir,
            const std::string &images_base_dir,
            DataPtr &features_data_ptr);

        /**
         * @brief 从OpenMVG的匹配文件中读取匹配信息并转换为PoMVG格式
         * @param matches_file OpenMVG的匹配文件路径
         * @param matches_data_ptr 输出的PoMVG匹配数据指针
         * @return 转换是否成功
         */
        bool MatchesOpenMVGFile2PoSDK(
            const std::string &matches_file,
            DataPtr &matches_data_ptr);

        /**
         * @brief 从OpenMVG的匹配文件中读取匹配信息并转换为PoMVG格式
         * @param matches_file OpenMVG的匹配文件路径
         * @param matches 输出的PoMVG匹配信息引用
         * @return 转换是否成功
         */
        bool MatchesOpenMVGFile2PoSDK(
            const std::string &matches_file,
            types::Matches &matches);

        /**
         * @brief 从OpenMVG的SfM数据创建PoMVG的图像数据
         * @param sfm_data OpenMVG的SfM数据对象
         * @param images_base_dir 图像文件的基础目录，用于构建绝对路径
         * @param images_data_ptr 输出的PoMVG图像数据指针
         * @return 转换是否成功
         */
        bool DataImagesOpenMVG2PoSDK(
            const openMVG::sfm::SfM_Data &sfm_data,
            const std::string &images_base_dir,
            DataPtr &images_data_ptr);

        /**
         * @brief 从OpenMVG的SfM数据文件创建PoMVG的图像数据
         * @param sfm_data_file OpenMVG的SfM数据文件路径
         * @param images_base_dir 图像文件的基础目录，用于构建绝对路径
         * @param images_data_ptr 输出的PoMVG图像数据指针
         * @return 转换是否成功
         */
        bool DataImagesOpenMVGFile2PoSDK(
            const std::string &sfm_data_file,
            const std::string &images_base_dir,
            DataPtr &images_data_ptr);
    }
}

#endif
