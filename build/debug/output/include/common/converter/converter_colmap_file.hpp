#pragma once

#include <string>
#include <vector>
#include <memory>
#include <po_core.hpp> // 包含PoSDK的核心定义

namespace PoSDK
{
    namespace Converter
    {
        namespace Colmap {
            /**
             * @brief 将Colmap的匹配文件转换为PoSDK的匹配数据
             * @param matches_file 匹配文件路径
             * @param matches_data 输出匹配数据
             * @return 是否成功
             */

            bool SfMFileToIdMap (const std::string &sfm_data_file, std::map<std::string, int> &file_name_to_id);

            bool ToDataMatches(
                const std::string &matches_folder,
                Interface::DataPtr &matches_data,
                std::map<std::string, int> &file_name_to_id);

            /**
             * @brief 将Colmap的SfM数据文件转换为PoSDK的全局位姿数据
             * @param sfm_data_file SfM数据文件路径
             * @param global_poses_data 输出全局位姿数据
             * @return 是否成功
             */
            bool ToDataGlobalPoses(
                const std::string &global_poses_file,
                Interface::DataPtr &global_poses_data,
                std::map<std::string, int> &file_name_to_id);

            bool LoadMatches(
                const std::string &matches_folder,
                types::Matches &matches,
                std::map<std::string, int> &file_name_to_id);
        }


    } // namespace Converter
} // namespace PoSDK
