/**
 * @file converter_opencv.hpp
 * @brief OpenCV数据类型转换器
 * @details 提供OpenCV与PoMVG数据类型之间的转换功能
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _CONVERTER_OPENCV_
#define _CONVERTER_OPENCV_

#include "converter_base.hpp"
#include "po_core.hpp"
#include <opencv2/core.hpp>
#include <opencv2/features2d.hpp>
#include <vector>

namespace PoSDK
{
    namespace Converter
    {

        using namespace PoSDK::types;

        /**
         * @brief OpenCV与PoMVG数据转换器
         */
        class OpenCVConverter
        {
        public:
            // 描述子类型枚举
            enum class DescriptorType
            {
                UINT8,  // 8位无符号整数类型(BRISK/ORB等算法使用)
                FLOAT32 // 32位浮点数类型(SIFT/SURF等算法使用)
            };

            /**
             * @brief 根据检测器类型获取对应的描述子类型
             */
            static DescriptorType ParseDescriptorType(const std::string &detector_type);

            /**
             * @brief OpenCV关键点转换为PoMVG特征点
             */
            static bool CVKeyPoint2FeaturePoint(
                const cv::KeyPoint &kp,
                FeaturePoint &feature_point);

            /**
             * @brief OpenCV关键点转换为PoMVG特征点(含独立描述子)
             */
            static bool CVKeyPoint2FeaturePointWithDesc(
                const cv::KeyPoint &kp,
                const cv::Mat &descriptors,
                size_t idx,
                FeaturePoint &feature_point,
                Descriptor &descriptor,
                DescriptorType desc_type = DescriptorType::FLOAT32);

            /**
             * @brief PoMVG特征点转换为OpenCV关键点
             */
            static bool FeaturePoint2CVKeyPoint(
                const FeaturePoint &feature_point,
                cv::KeyPoint &kp);

            /**
             * @brief PoMVG特征点转换为OpenCV关键点(含独立描述子)
             */
            static bool FeaturePoint2CVKeyPointWithDesc(
                const FeaturePoint &feature_point,
                const Descriptor &descriptor,
                cv::KeyPoint &kp,
                cv::Mat &descriptors,
                size_t idx,
                DescriptorType desc_type = DescriptorType::FLOAT32);

            /**
             * @brief 批量转换OpenCV特征为PoMVG特征信息
             */
            static bool CVFeatures2FeaturesInfo(
                const std::vector<cv::KeyPoint> &keypoints,
                FeaturesInfoPtr &features_info_ptr,
                const std::string &image_path);

            /**
             * @brief 批量转换OpenCV特征为PoMVG特征信息和描述子
             */
            static bool CVFeatures2FeaturesInfoWithDesc(
                const std::vector<cv::KeyPoint> &keypoints,
                const cv::Mat &descriptors,
                FeaturesInfoPtr &features_info_ptr,
                Descs &descriptors_out,
                const std::string &image_path,
                const std::string &detector_type = "SIFT");

            /**
             * @brief 批量转换PoMVG特征信息为OpenCV特征
             */
            static bool FeaturesInfo2CVFeatures(
                const ImageFeatureInfo &image_features,
                std::vector<cv::KeyPoint> &keypoints);

            /**
             * @brief 批量转换PoMVG特征信息和描述子为OpenCV特征
             */
            static bool FeaturesInfo2CVFeaturesWithDesc(
                const ImageFeatureInfo &image_features,
                const Descs &descriptors,
                std::vector<cv::KeyPoint> &keypoints,
                cv::Mat &descriptors_out,
                const std::string &detector_type = "SIFT");

            /**
             * @brief 转换OpenCV DMatch到PoMVG Matches
             */
            static bool CVDMatch2Matches(const std::vector<cv::DMatch> &cv_matches,
                                         const IndexT view_id1,
                                         const IndexT view_id2,
                                         MatchesPtr &matches_ptr);

            /**
             * @brief 转换OpenCV DMatch到PoMVG IdMatches (单对视图的匹配)
             */
            static bool CVDMatch2IdMatches(const std::vector<cv::DMatch> &cv_matches,
                                           IdMatches &matches);

            /**
             * @brief 转换OpenCV相机矩阵和畸变系数到PoMVG相机模型
             * @param camera_matrix OpenCV相机内参矩阵
             * @param dist_coeffs OpenCV畸变系数
             * @param image_size 图像尺寸
             * @param camera_model [out] PoMVG相机模型
             * @param distortion_type 畸变类型
             * @return 转换是否成功
             */
            static bool CVCalibration2CameraModel(const cv::Mat &camera_matrix,
                                                  const cv::Mat &dist_coeffs,
                                                  const cv::Size &image_size,
                                                  CameraModel &camera_model,
                                                  DistortionType distortion_type = DistortionType::BROWN_CONRADY);

            /**
             * @brief 转换PoMVG相机模型到OpenCV相机矩阵和畸变系数
             */
            static bool CameraModel2CVCalibration(const CameraModel &camera_model,
                                                  cv::Mat &camera_matrix,
                                                  cv::Mat &dist_coeffs);

            /**
             * @brief 转换PoMVG特征信息为OpenCV特征(描述子参数版)
             * @param image_features 图像特征信息
             * @param descriptors 特征描述子集合
             * @param keypoints [out] OpenCV关键点
             * @param descriptors_out [out] OpenCV描述子矩阵
             * @param detector_type 检测器类型，决定描述子格式
             * @return 转换是否成功
             */
            static bool PoSDK2CVFeatures(
                const ImageFeatureInfo &image_features,
                const Descs &descriptors,
                std::vector<cv::KeyPoint> &keypoints,
                cv::Mat &descriptors_out,
                const std::string &detector_type = "SIFT");

            /**
             * @brief 从OpenCV特征转换为PoMVG格式(描述子参数版)
             * @param keypoints OpenCV关键点
             * @param descriptors OpenCV描述子
             * @param features_info_ptr [out] 特征信息
             * @param descriptors_out [out] 特征描述子集合
             * @param image_path 图像路径
             * @param detector_type 检测器类型
             * @return 转换是否成功
             */
            static bool PoSDK2FeaturesInfo(
                const std::vector<cv::KeyPoint> &keypoints,
                const cv::Mat &descriptors,
                FeaturesInfoPtr &features_info_ptr,
                Descs &descriptors_out,
                const std::string &image_path,
                const std::string &detector_type = "SIFT");

            /**
             * @brief 将PoSDK匹配数据转换为OpenCV点集
             * @param matches 特征匹配
             * @param features_info 特征信息
             * @param camera_models 相机模型集合
             * @param view_pair 视图对
             * @param points1 [out] 第一视图点集
             * @param points2 [out] 第二视图点集
             * @return 转换是否成功
             */
            static bool PoSDKMatchesToOpenCVPoints(
                const IdMatches &matches,
                const FeaturesInfo &features_info,
                const CameraModels &camera_models,
                const ViewPair &view_pair,
                std::vector<cv::Point2f> &points1,
                std::vector<cv::Point2f> &points2);

            /**
             * @brief 将PoSDK匹配样本转换为OpenCV点集
             * @param matches_sample 匹配样本
             * @param features_info 特征信息
             * @param camera_models 相机模型集合
             * @param view_pair 视图对
             * @param points1 [out] 第一视图点集
             * @param points2 [out] 第二视图点集
             * @return 转换是否成功
             */
            static bool PoSDKMatchesToOpenCVPoints(
                const DataPtr &matches_sample,
                const FeaturesInfo &features_info,
                const CameraModels &camera_models,
                const ViewPair &view_pair,
                std::vector<cv::Point2f> &points1,
                std::vector<cv::Point2f> &points2);
        };

    } // namespace Converter
} // namespace PoSDK

#endif // _CONVERTER_OPENCV_
