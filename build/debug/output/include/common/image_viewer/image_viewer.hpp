#pragma once

#include <opencv2/core.hpp>
#include <opencv2/features2d.hpp>
#include <opencv2/imgproc.hpp>
#include <string>
#include <memory>
#include <unordered_map>
#include <sstream>
#include <iomanip>
#include <numeric>

namespace common {

class ImageViewer {
public:
    // 单例访问
    static ImageViewer& Instance();

    // 禁用拷贝和赋值
    ImageViewer(const ImageViewer&) = delete;
    ImageViewer& operator=(const ImageViewer&) = delete;

    // 原有的静态接口保持不变
    static void ShowImage(const cv::Mat& image,
                         const std::vector<cv::KeyPoint>& keypoints,
                         const std::string& window_name);
    static void WaitKey(int delay = 0);

    // 新增功能接口
    void SetWindowProperty(const std::string& window_name, 
                         int property_id, 
                         double value);
    
    void ResizeWindow(const std::string& window_name, 
                     int width, 
                     int height);
    
    void MoveWindow(const std::string& window_name, 
                   int x, 
                   int y);

    void SaveWindowImage(const std::string& window_name, 
                        const std::string& filename);

    void CloseWindow(const std::string& window_name);
    void CloseAllWindows();

    // 显示选项配置
    struct DisplayOptions {
        cv::Scalar keypoint_color = cv::Scalar(0, 255, 0);  // 特征点颜色
        int keypoint_size = 3;                              // 特征点大小
        bool show_orientation = true;                        // 是否显示方向
        bool show_scale = true;                             // 是否显示尺度
        float scale_factor = 1.0f;                          // 显示缩放因子
        
        // 新增显示控制选项
        bool auto_wait = true;           // 是否自动等待
        int wait_time = 0;              // 等待时间(ms)，0表示永久等待
        bool enable_window_control = true; // 是否启用窗口控制
        
        // 匹配线条样式
        cv::Scalar match_color = cv::Scalar(0, 255, 255);  // 默认黄色
        int match_thickness = 1;                           // 线条宽度
        float match_alpha = 0.7f;                         // 透明度
        bool use_random_colors = false;                   // 是否使用随机颜色
        float min_distance_percentile = 0.0f;             // 距离百分比下限
        float max_distance_percentile = 1.0f;             // 距离百分比上限
        
        // 匹配显示相关选项
        int initial_window_width = 1600;    // 初始窗口宽度
        int initial_window_height = 800;    // 初始窗口高度
        bool show_separator = true;         // 是否显示分割线
        int separator_width = 2;            // 分割线宽度
        cv::Scalar separator_color = cv::Scalar(200, 200, 200);  // 分割线颜色
        
        // 匹配线条样式
        bool use_quality_colormap = true;   // 是否使用质量颜色映射
        int colormap_type = cv::COLORMAP_JET;  // 颜色映射方案
        float line_transparency = 0.7;      // 线条透明度
        int line_thickness = 2;            // 线条粗细
        bool use_antialiasing = true;      // 是否使用抗锯齿
    };

    void SetDisplayOptions(const DisplayOptions& options);
    const DisplayOptions& GetDisplayOptions() const;

    // 添加新的显示接口
    void ShowMatches(const cv::Mat& img1, 
                    const cv::Mat& img2,
                    const std::vector<cv::KeyPoint>& keypoints1,
                    const std::vector<cv::KeyPoint>& keypoints2,
                    const std::vector<cv::DMatch>& matches,
                    const std::string& window_name);

    // 批量保存接口
    void SaveMatchVisualization(const std::string& prefix,
                              const std::vector<std::pair<int, int>>& image_pairs,
                              const std::vector<cv::Mat>& images,
                              const std::vector<std::vector<cv::KeyPoint>>& all_keypoints,
                              const std::vector<std::vector<cv::DMatch>>& all_matches);

private:
    ImageViewer();  // 私有构造函数
    ~ImageViewer();

    // 实际的显示实现
    void ShowImageImpl(const cv::Mat& image,
                      const std::vector<cv::KeyPoint>& keypoints,
                      const std::string& window_name);

    struct WindowInfo {
        cv::Mat current_image;
        std::vector<cv::KeyPoint> current_keypoints;
        bool is_visible;
    };

    std::unordered_map<std::string, WindowInfo> windows_;
    DisplayOptions display_options_;
    
    // 窗口管理
    void CreateWindowIfNeeded(const std::string& window_name);
    void UpdateWindow(const std::string& window_name);

    std::string current_window_;  // 添加当前窗口名称成员
};

} // namespace common