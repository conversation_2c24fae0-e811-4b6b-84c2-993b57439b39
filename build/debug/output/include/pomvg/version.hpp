// This file is auto-generated by <PERSON><PERSON><PERSON> for the PoSDK project

#ifndef POMVG_PROJECT_VERSION_HPP
#define POMVG_PROJECT_VERSION_HPP

// Version number definitions for PoSDK project
#define POMVG_VERSION_MAJOR @POMVG_VERSION_MAJOR @
#define POMVG_VERSION_MINOR @POMVG_VERSION_MINOR @
#define POMVG_VERSION_PATCH @POMVG_VERSION_PATCH @
#define POMVG_VERSION_TWEAK @POMVG_VERSION_TWEAK @

// Preprocessor string conversion
#define POMVG_DETAIL_TO_STRING_HELPER(x) #x
#define POMVG_DETAIL_TO_STRING(x) POMVG_DETAIL_TO_STRING_HELPER(x)

// Version string (e.g., "1.0.0.0")
#define POMVG_VERSION_STRING                    \
    POMVG_DETAIL_TO_STRING(POMVG_VERSION_MAJOR) \
    "." POMVG_DETAIL_TO_STRING(POMVG_VERSION_MINOR) "." POMVG_DETAIL_TO_STRING(POMVG_VERSION_PATCH) "." POMVG_DETAIL_TO_STRING(POMVG_VERSION_TWEAK)

// Combined version number as a hexadecimal value (e.g., 0x01000000 for 1.0.0.0)
// Useful for compile-time version comparisons
#define POMVG_VERSION_HEX          \
    ((POMVG_VERSION_MAJOR << 24) | \
     (POMVG_VERSION_MINOR << 16) | \
     (POMVG_VERSION_PATCH << 8) |  \
     (POMVG_VERSION_TWEAK))

#endif // POMVG_PROJECT_VERSION_HPP
