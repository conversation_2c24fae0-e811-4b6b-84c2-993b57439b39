#!/usr/bin/env python3

# Copyright (c), ETH Zurich and UNC Chapel Hill.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#
#     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
#       its contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.

"""
COLMAP数据库相机姿态导出工具

从COLMAP数据库中读取相机姿态信息，并导出为可读格式
支持输出格式：
- TXT格式：每行包含图像名、旋转四元数、平移向量
- CSV格式：便于在Excel或其他工具中分析
- JSON格式：结构化数据，便于程序处理
"""

import argparse
import json
import os
import sqlite3
import sys
import math

# 添加当前目录到 Python 路径以便导入 colmap_pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置conda环境 - 必须在导入numpy之前
from colmap_pipeline import setup_conda_environment
if not setup_conda_environment():
    sys.exit(1)

import numpy as np


def quaternion_to_rotation_matrix(qw, qx, qy, qz):
    """将四元数转换为旋转矩阵"""
    # 归一化四元数
    norm = math.sqrt(qw*qw + qx*qx + qy*qy + qz*qz)
    qw, qx, qy, qz = qw/norm, qx/norm, qy/norm, qz/norm
    
    # 构建旋转矩阵
    R = np.array([
        [1 - 2*(qy*qy + qz*qz), 2*(qx*qy - qw*qz), 2*(qx*qz + qw*qy)],
        [2*(qx*qy + qw*qz), 1 - 2*(qx*qx + qz*qz), 2*(qy*qz - qw*qx)],
        [2*(qx*qz - qw*qy), 2*(qy*qz + qw*qx), 1 - 2*(qx*qx + qy*qy)]
    ])
    
    return R


def rotation_matrix_to_euler_angles(R):
    """将旋转矩阵转换为欧拉角 (roll, pitch, yaw) 以度为单位"""
    # 使用ZYX顺序的欧拉角
    sy = math.sqrt(R[0,0]*R[0,0] + R[1,0]*R[1,0])
    
    singular = sy < 1e-6
    
    if not singular:
        x = math.atan2(R[2,1], R[2,2])  # roll
        y = math.atan2(-R[2,0], sy)     # pitch
        z = math.atan2(R[1,0], R[0,0])  # yaw
    else:
        x = math.atan2(-R[1,2], R[1,1])  # roll
        y = math.atan2(-R[2,0], sy)      # pitch
        z = 0                            # yaw
    
    # 转换为度
    return np.degrees([x, y, z])


def read_camera_poses_from_database(database_path):
    """从COLMAP数据库读取相机姿态信息"""
    connection = sqlite3.connect(database_path)
    cursor = connection.cursor()
    
    # 读取相机信息
    print("📷 读取相机信息...")
    cursor.execute("SELECT camera_id, model, width, height, params FROM cameras")
    cameras = {}
    for row in cursor.fetchall():
        camera_id, model, width, height, params = row
        # 解析相机参数
        if sys.version_info[0] >= 3:
            param_array = np.frombuffer(params, dtype=np.float64) if params else np.array([])
        else:
            param_array = np.fromstring(params, dtype=np.float64) if params else np.array([])
        
        cameras[camera_id] = {
            'model': model,
            'width': width,
            'height': height,
            'params': param_array
        }
    print(f"   找到 {len(cameras)} 个相机")
    
    # 读取图像姿态信息
    print("🎯 读取图像姿态信息...")
    cursor.execute("""
        SELECT image_id, name, camera_id, 
               prior_qw, prior_qx, prior_qy, prior_qz, 
               prior_tx, prior_ty, prior_tz,
               qw, qx, qy, qz, tx, ty, tz
        FROM images
    """)
    
    poses_data = []
    registered_count = 0
    
    for row in cursor.fetchall():
        (image_id, name, camera_id, 
         prior_qw, prior_qx, prior_qy, prior_qz,
         prior_tx, prior_ty, prior_tz,
         qw, qx, qy, qz, tx, ty, tz) = row
        
        # 检查图像是否已注册（有优化后的姿态）
        is_registered = (qw is not None and qx is not None and 
                        qy is not None and qz is not None and
                        tx is not None and ty is not None and tz is not None)
        
        if is_registered:
            registered_count += 1
            
        # 使用优化后的姿态，如果不存在则使用先验姿态
        if is_registered:
            final_qw, final_qx, final_qy, final_qz = qw, qx, qy, qz
            final_tx, final_ty, final_tz = tx, ty, tz
        elif (prior_qw is not None and prior_qx is not None and 
              prior_qy is not None and prior_qz is not None):
            final_qw, final_qx, final_qy, final_qz = prior_qw, prior_qx, prior_qy, prior_qz
            final_tx, final_ty, final_tz = prior_tx or 0, prior_ty or 0, prior_tz or 0
        else:
            # 如果没有姿态信息，跳过
            continue
        
        # 计算旋转矩阵和欧拉角
        try:
            R = quaternion_to_rotation_matrix(final_qw, final_qx, final_qy, final_qz)
            euler_angles = rotation_matrix_to_euler_angles(R)
            
            # 计算相机中心位置 (世界坐标系)
            # COLMAP中的t是从世界坐标系到相机坐标系的平移
            # 相机中心位置 = -R^T * t
            camera_center = -R.T @ np.array([final_tx, final_ty, final_tz])
            
            pose_data = {
                'image_id': image_id,
                'image_name': name,
                'camera_id': camera_id,
                'is_registered': is_registered,
                # 四元数 (w, x, y, z)
                'quaternion': [final_qw, final_qx, final_qy, final_qz],
                # 平移向量 (世界到相机)
                'translation': [final_tx, final_ty, final_tz],
                # 相机中心位置 (世界坐标系)
                'camera_center': camera_center.tolist(),
                # 欧拉角 (roll, pitch, yaw) 度
                'euler_angles': euler_angles.tolist(),
                # 旋转矩阵
                'rotation_matrix': R.tolist()
            }
            
            poses_data.append(pose_data)
            
        except Exception as e:
            print(f"   警告: 处理图像 {name} 的姿态时出错: {e}")
            continue
    
    print(f"   成功读取 {len(poses_data)} 个图像的姿态信息")
    print(f"   其中 {registered_count} 个图像已注册")
    
    connection.close()
    return poses_data, cameras


def export_poses_txt(poses_data, output_path):
    """导出为TXT格式"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# COLMAP相机姿态导出文件\n")
        f.write("# 格式: 图像名 注册状态 四元数(w,x,y,z) 平移(x,y,z) 相机中心(x,y,z) 欧拉角(roll,pitch,yaw)\n")
        f.write("# 注册状态: 1=已注册, 0=仅先验\n")
        f.write("#\n")
        
        for pose in poses_data:
            qw, qx, qy, qz = pose['quaternion']
            tx, ty, tz = pose['translation']
            cx, cy, cz = pose['camera_center']
            roll, pitch, yaw = pose['euler_angles']
            
            f.write(f"{pose['image_name']} {int(pose['is_registered'])} "
                   f"{qw:.8f} {qx:.8f} {qy:.8f} {qz:.8f} "
                   f"{tx:.8f} {ty:.8f} {tz:.8f} "
                   f"{cx:.8f} {cy:.8f} {cz:.8f} "
                   f"{roll:.6f} {pitch:.6f} {yaw:.6f}\n")


def export_poses_csv(poses_data, output_path):
    """导出为CSV格式"""
    with open(output_path, 'w', encoding='utf-8') as f:
        # CSV头部
        f.write("image_name,camera_id,is_registered,"
               "qw,qx,qy,qz,tx,ty,tz,"
               "camera_center_x,camera_center_y,camera_center_z,"
               "roll_deg,pitch_deg,yaw_deg\n")
        
        for pose in poses_data:
            qw, qx, qy, qz = pose['quaternion']
            tx, ty, tz = pose['translation']
            cx, cy, cz = pose['camera_center']
            roll, pitch, yaw = pose['euler_angles']
            
            f.write(f"{pose['image_name']},{pose['camera_id']},{int(pose['is_registered'])},"
                   f"{qw:.8f},{qx:.8f},{qy:.8f},{qz:.8f},"
                   f"{tx:.8f},{ty:.8f},{tz:.8f},"
                   f"{cx:.8f},{cy:.8f},{cz:.8f},"
                   f"{roll:.6f},{pitch:.6f},{yaw:.6f}\n")


def export_poses_json(poses_data, cameras, output_path):
    """导出为JSON格式"""
    output_data = {
        'cameras': cameras,
        'poses': poses_data,
        'metadata': {
            'total_images': len(poses_data),
            'registered_images': sum(1 for p in poses_data if p['is_registered']),
            'coordinate_system': 'COLMAP (右手坐标系)',
            'quaternion_format': 'w,x,y,z',
            'translation_description': '从世界坐标系到相机坐标系的平移',
            'camera_center_description': '相机在世界坐标系中的位置',
            'euler_angles_unit': 'degrees',
            'euler_angles_order': 'ZYX (roll, pitch, yaw)'
        }
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)


def main():
    parser = argparse.ArgumentParser(
        description="从COLMAP数据库导出相机姿态信息",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--database_path', required=True,
                       help='COLMAP数据库文件路径 (.db)')
    parser.add_argument('--output_folder', required=True,
                       help='输出文件夹路径')
    
    # 可选参数
    parser.add_argument('--format', choices=['txt', 'csv', 'json', 'all'], 
                       default='all', help='输出格式')
    parser.add_argument('--registered_only', action='store_true',
                       help='仅导出已注册的图像')
    
    args = parser.parse_args()
    
    # 检查输入
    if not os.path.exists(args.database_path):
        print(f"❌ 错误: 数据库文件不存在: {args.database_path}")
        sys.exit(1)
    
    try:
        print("="*60)
        print("COLMAP 相机姿态导出工具")
        print("="*60)
        print(f"数据库路径: {args.database_path}")
        print(f"输出文件夹: {args.output_folder}")
        print(f"输出格式: {args.format}")
        print(f"仅注册图像: {args.registered_only}")
        print()
        
        # 创建输出文件夹
        os.makedirs(args.output_folder, exist_ok=True)
        
        # 读取姿态数据
        poses_data, cameras = read_camera_poses_from_database(args.database_path)
        
        if len(poses_data) == 0:
            print("⚠️  警告: 没有找到姿态数据")
            return
        
        # 过滤仅注册的图像
        if args.registered_only:
            filtered_poses = [p for p in poses_data if p['is_registered']]
            print(f"🔍 过滤后保留 {len(filtered_poses)}/{len(poses_data)} 个已注册图像")
            poses_data = filtered_poses
        
        if len(poses_data) == 0:
            print("⚠️  警告: 过滤后没有图像数据")
            return
        
        # 导出文件
        base_name = "camera_poses"
        
        if args.format in ['txt', 'all']:
            txt_path = os.path.join(args.output_folder, f"{base_name}.txt")
            export_poses_txt(poses_data, txt_path)
            print(f"✅ TXT格式已导出: {txt_path}")
        
        if args.format in ['csv', 'all']:
            csv_path = os.path.join(args.output_folder, f"{base_name}.csv")
            export_poses_csv(poses_data, csv_path)
            print(f"✅ CSV格式已导出: {csv_path}")
        
        if args.format in ['json', 'all']:
            json_path = os.path.join(args.output_folder, f"{base_name}.json")
            export_poses_json(poses_data, cameras, json_path)
            print(f"✅ JSON格式已导出: {json_path}")
        
        # 统计信息
        registered_count = sum(1 for p in poses_data if p['is_registered'])
        
        print(f"\n📊 统计信息:")
        print(f"   总图像数: {len(poses_data)}")
        print(f"   已注册图像数: {registered_count}")
        print(f"   相机数: {len(cameras)}")
        
        if registered_count > 0:
            # 计算相机位置的范围
            centers = np.array([p['camera_center'] for p in poses_data if p['is_registered']])
            if len(centers) > 0:
                center_min = centers.min(axis=0)
                center_max = centers.max(axis=0)
                center_range = center_max - center_min
                
                print(f"\n📍 相机位置范围 (已注册图像):")
                print(f"   X: [{center_min[0]:.3f}, {center_max[0]:.3f}] (范围: {center_range[0]:.3f})")
                print(f"   Y: [{center_min[1]:.3f}, {center_max[1]:.3f}] (范围: {center_range[1]:.3f})")
                print(f"   Z: [{center_min[2]:.3f}, {center_max[2]:.3f}] (范围: {center_range[2]:.3f})")
        
        print(f"\n💡 使用说明:")
        print(f"   - 四元数格式: (w, x, y, z)")
        print(f"   - 坐标系: COLMAP右手坐标系")
        print(f"   - 平移向量: 从世界坐标系到相机坐标系")
        print(f"   - 相机中心: 相机在世界坐标系中的位置")
        print(f"   - 欧拉角: ZYX顺序 (roll, pitch, yaw)，单位为度")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 