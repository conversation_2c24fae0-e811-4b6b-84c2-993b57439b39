#!/usr/bin/env python3

# Copyright (c), ETH Zurich and UNC Chapel Hill.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#
#     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
#       its contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.

"""
COLMAP数据库匹配导出工具

从COLMAP数据库中读取匹配数据，并导出为匹配文件
每个匹配文件格式：
- 第一行：camera_id1 camera_id2
- 后续每行：X1 Y1 X2 Y2 (匹配点的图像坐标)
"""

import argparse
import os
import sqlite3
import sys

# 添加当前目录到 Python 路径以便导入 colmap_pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置conda环境 - 必须在导入numpy和pycolmap之前
from colmap_pipeline import setup_conda_environment
if not setup_conda_environment():
    sys.exit(1)

import numpy as np


def pair_id_to_image_ids(pair_id):
    """从pair_id解析出图像ID对"""
    MAX_IMAGE_ID = 2**31 - 1
    image_id2 = pair_id % MAX_IMAGE_ID
    image_id1 = (pair_id - image_id2) // MAX_IMAGE_ID
    return int(image_id1), int(image_id2)


def blob_to_array(blob, dtype, shape=(-1,)):
    """将数据库中的二进制blob转换为numpy数组"""
    if blob is None:
        return None
    if sys.version_info[0] >= 3:
        return np.frombuffer(blob, dtype=dtype).reshape(*shape)
    else:
        return np.fromstring(blob, dtype=dtype).reshape(*shape)


def read_database_matches(database_path):
    """从COLMAP数据库读取所有匹配数据"""
    connection = sqlite3.connect(database_path)
    cursor = connection.cursor()
    
    # 读取图像信息 (image_id -> camera_id, name)
    print("📷 读取图像信息...")
    cursor.execute("SELECT image_id, name, camera_id FROM images")
    images = {}
    for row in cursor.fetchall():
        image_id, name, camera_id = row
        images[image_id] = {
            'name': name,
            'camera_id': camera_id
        }
    print(f"   找到 {len(images)} 张图像")
    
    # 读取关键点数据 (image_id -> keypoints)
    print("🎯 读取关键点数据...")
    cursor.execute("SELECT image_id, rows, cols, data FROM keypoints")
    keypoints = {}
    for row in cursor.fetchall():
        image_id, rows, cols, data = row
        if data is not None:
            kpts = blob_to_array(data, np.float32, (-1, cols))
            keypoints[image_id] = kpts
    print(f"   找到 {len(keypoints)} 张图像的关键点")
    
    # 读取匹配数据
    print("🔗 读取匹配数据...")
    cursor.execute("SELECT pair_id, rows, cols, data FROM matches WHERE rows > 0")
    matches_data = []
    
    for row in cursor.fetchall():
        pair_id, rows, cols, data = row
        if data is not None and rows > 0:
            # 解析图像对
            image_id1, image_id2 = pair_id_to_image_ids(pair_id)
            
            # 确保两个图像都有信息
            if image_id1 not in images or image_id2 not in images:
                continue
                
            # 确保两个图像都有关键点
            if image_id1 not in keypoints or image_id2 not in keypoints:
                continue
            
            # 解析匹配数据
            matches = blob_to_array(data, np.uint32, (-1, 2))
            if matches is None or len(matches) == 0:
                continue
            
            # 获取匹配点的坐标
            kpts1 = keypoints[image_id1]
            kpts2 = keypoints[image_id2]
            
            match_coords = []
            for match in matches:
                idx1, idx2 = match
                if idx1 < len(kpts1) and idx2 < len(kpts2):
                    x1, y1 = kpts1[idx1][:2]  # 只取x,y坐标
                    x2, y2 = kpts2[idx2][:2]
                    match_coords.append([x1, y1, x2, y2])
            
            if len(match_coords) > 0:
                matches_data.append({
                    'image_id1': image_id1,
                    'image_id2': image_id2,
                    'camera_id1': images[image_id1]['camera_id'],
                    'camera_id2': images[image_id2]['camera_id'],
                    'image_name1': images[image_id1]['name'],
                    'image_name2': images[image_id2]['name'],
                    'matches': np.array(match_coords)
                })
    
    print(f"   找到 {len(matches_data)} 对有效匹配")
    
    connection.close()
    return matches_data


def export_matches_to_files(matches_data, output_folder):
    """将匹配数据导出为文件"""
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"\n 导出匹配文件到: {output_folder}")
    
    for i, match_data in enumerate(matches_data):
        # 生成文件名 (使用图像名，去掉扩展名)
        name1 = os.path.splitext(match_data['image_name1'])[0]
        name2 = os.path.splitext(match_data['image_name2'])[0]
        filename = f"matches_{name1}_{name2}.txt"
        filepath = os.path.join(output_folder, filename)
        
        # 写入匹配文件
        with open(filepath, 'w') as f:
            # 第一行：camera_id1 camera_id2
            f.write(f"{match_data['camera_id1']} {match_data['camera_id2']}\n")
            
            # 后续每行：X1 Y1 X2 Y2
            for match in match_data['matches']:
                x1, y1, x2, y2 = match
                f.write(f"{x1:.6f} {y1:.6f} {x2:.6f} {y2:.6f}\n")
        
        print(f"   [{i+1:3d}/{len(matches_data)}] {filename} - {len(match_data['matches'])} 匹配点")
    
    print(f"\n 成功导出 {len(matches_data)} 个匹配文件")


def main():
    parser = argparse.ArgumentParser(
        description="从COLMAP数据库导出匹配数据",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--database_path', required=True,
                       help='COLMAP数据库文件路径 (.db)')
    parser.add_argument('--output_folder', required=True,
                       help='输出文件夹路径')
    
    # 可选参数
    parser.add_argument('--min_matches', type=int, default=15,
                       help='最小匹配点数量阈值')
    
    args = parser.parse_args()
    
    # 检查输入
    if not os.path.exists(args.database_path):
        print(f"❌ 错误: 数据库文件不存在: {args.database_path}")
        sys.exit(1)
    
    try:
        print("="*60)
        print("COLMAP 数据库匹配导出工具")
        print("="*60)
        print(f"数据库路径: {args.database_path}")
        print(f"输出文件夹: {args.output_folder}")
        print(f"最小匹配数: {args.min_matches}")
        print()
        
        # 读取匹配数据
        matches_data = read_database_matches(args.database_path)
        
        # 过滤匹配数量
        filtered_matches = [m for m in matches_data if len(m['matches']) >= args.min_matches]
        if len(filtered_matches) < len(matches_data):
            print(f"🔍 过滤后保留 {len(filtered_matches)}/{len(matches_data)} 对匹配 (>= {args.min_matches} 匹配点)")
        
        if len(filtered_matches) == 0:
            print("⚠️  警告: 没有找到满足条件的匹配数据")
            return
        
        # 导出文件
        export_matches_to_files(filtered_matches, args.output_folder)
        
        # 统计信息
        total_matches = sum(len(m['matches']) for m in filtered_matches)
        avg_matches = total_matches / len(filtered_matches)
        
        print(f"\n📊 统计信息:")
        print(f"   总匹配对数: {len(filtered_matches)}")
        print(f"   总匹配点数: {total_matches}")
        print(f"   平均匹配点数: {avg_matches:.1f}")
        
        print("\n💡 使用示例:")
        print("   # 每个匹配文件格式:")
        print("   # 第一行: camera_id1 camera_id2")
        print("   # 后续每行: X1 Y1 X2 Y2")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 