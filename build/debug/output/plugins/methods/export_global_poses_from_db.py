#!/usr/bin/env python3

# Copyright (c), ETH Zurich and UNC Chapel Hill.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#
#     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
#       its contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.

"""
COLMAP数据库全局姿态导出工具

从COLMAP数据库中读取相机姿态信息，并导出为global_pose.txt文件
输出格式：
- 第一行：相机数量
- 后续每行：图像名 R00 R10 R20 R01 R11 R21 R02 R12 R22 tx ty tz
"""

import argparse
import os
import sqlite3
import sys
import math

# 添加当前目录到 Python 路径以便导入 colmap_pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置conda环境 - 必须在导入numpy之前
from colmap_pipeline import setup_conda_environment
if not setup_conda_environment():
    sys.exit(1)

import numpy as np


def quaternion_to_rotation_matrix(qw, qx, qy, qz):
    """将四元数转换为旋转矩阵"""
    # 归一化四元数
    norm = math.sqrt(qw*qw + qx*qx + qy*qy + qz*qz)
    qw, qx, qy, qz = qw/norm, qx/norm, qy/norm, qz/norm
    
    # 构建旋转矩阵
    R = np.array([
        [1 - 2*(qy*qy + qz*qz), 2*(qx*qy - qw*qz), 2*(qx*qz + qw*qy)],
        [2*(qx*qy + qw*qz), 1 - 2*(qx*qx + qz*qz), 2*(qy*qz - qw*qx)],
        [2*(qx*qz - qw*qy), 2*(qy*qz + qw*qx), 1 - 2*(qx*qx + qy*qy)]
    ])
    
    return R


def read_global_poses_from_database(database_path):
    """从COLMAP数据库读取全局姿态信息"""
    connection = sqlite3.connect(database_path)
    cursor = connection.cursor()
    
    # 首先检查数据库结构
    print("🔍 检查数据库结构...")
    cursor.execute("PRAGMA table_info(images)")
    columns = [row[1] for row in cursor.fetchall()]
    print(f"   images表包含列: {columns}")
    
    # 检查是否有四元数和平移数据
    has_prior = all(col in columns for col in ['prior_qw', 'prior_qx', 'prior_qy', 'prior_qz'])
    has_optimized = all(col in columns for col in ['qw', 'qx', 'qy', 'qz', 'tx', 'ty', 'tz'])
    
    print(f"   先验姿态可用: {has_prior}")
    print(f"   优化姿态可用: {has_optimized}")
    
    if not has_prior and not has_optimized:
        print("❌ 错误: 数据库中没有找到姿态信息")
        connection.close()
        return []
    
    # 读取图像姿态信息
    print("🎯 读取图像姿态信息...")
    
    if has_optimized:
        # 优先使用优化后的姿态
        cursor.execute("""
            SELECT image_id, name, camera_id, 
                   qw, qx, qy, qz, tx, ty, tz
            FROM images
            WHERE qw IS NOT NULL AND qx IS NOT NULL AND qy IS NOT NULL AND qz IS NOT NULL
            AND tx IS NOT NULL AND ty IS NOT NULL AND tz IS NOT NULL
        """)
        print("   使用优化后的姿态数据")
    elif has_prior:
        # 使用先验姿态
        cursor.execute("""
            SELECT image_id, name, camera_id, 
                   prior_qw, prior_qx, prior_qy, prior_qz, 
                   COALESCE(prior_tx, 0), COALESCE(prior_ty, 0), COALESCE(prior_tz, 0)
            FROM images
            WHERE prior_qw IS NOT NULL AND prior_qx IS NOT NULL 
            AND prior_qy IS NOT NULL AND prior_qz IS NOT NULL
        """)
        print("   使用先验姿态数据")
    
    poses_data = []
    
    for row in cursor.fetchall():
        (image_id, name, camera_id, qw, qx, qy, qz, tx, ty, tz) = row
        
        try:
            # 计算旋转矩阵
            R = quaternion_to_rotation_matrix(qw, qx, qy, qz)
            
            pose_data = {
                'image_id': image_id,
                'image_name': name,
                'camera_id': camera_id,
                'rotation_matrix': R,
                'translation': [tx, ty, tz]
            }
            
            poses_data.append(pose_data)
            
        except Exception as e:
            print(f"   警告: 处理图像 {name} 的姿态时出错: {e}")
            continue
    
    print(f"   成功读取 {len(poses_data)} 个图像的姿态信息")
    
    connection.close()
    return poses_data


def export_global_poses_txt(poses_data, output_path):
    """导出为global_pose.txt格式"""
    with open(output_path, 'w') as f:
        # 第一行：相机数量
        f.write(f"{len(poses_data)}\n")
        
        # 每行：图像名 + 旋转矩阵(按列优先) + 平移向量
        for pose in poses_data:
            R = pose['rotation_matrix']
            tx, ty, tz = pose['translation']
            
            # 按列优先顺序输出旋转矩阵：R(0,0) R(1,0) R(2,0) R(0,1) R(1,1) R(2,1) R(0,2) R(1,2) R(2,2)
            f.write(f"{pose['image_name']} "
                   f"{R[0,0]:.8f} {R[1,0]:.8f} {R[2,0]:.8f} "
                   f"{R[0,1]:.8f} {R[1,1]:.8f} {R[2,1]:.8f} "
                   f"{R[0,2]:.8f} {R[1,2]:.8f} {R[2,2]:.8f} "
                   f"{tx:.8f} {ty:.8f} {tz:.8f}\n")


def main():
    parser = argparse.ArgumentParser(
        description="从COLMAP数据库导出全局姿态到global_pose.txt",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--database_path', required=True,
                       help='COLMAP数据库文件路径 (.db)')
    parser.add_argument('--output_folder', required=True,
                       help='输出文件夹路径')
    
    args = parser.parse_args()
    
    # 检查输入
    if not os.path.exists(args.database_path):
        print(f"❌ 错误: 数据库文件不存在: {args.database_path}")
        sys.exit(1)
    
    try:
        print("="*60)
        print("COLMAP 全局姿态导出工具")
        print("="*60)
        print(f"数据库路径: {args.database_path}")
        print(f"输出文件夹: {args.output_folder}")
        print()
        
        # 创建输出文件夹
        os.makedirs(args.output_folder, exist_ok=True)
        
        # 读取姿态数据（仅已注册的图像）
        poses_data = read_global_poses_from_database(args.database_path)
        
        if len(poses_data) == 0:
            print("⚠️  警告: 没有找到已注册的图像姿态数据")
            return
        
        # 导出global_pose.txt文件
        output_path = os.path.join(args.output_folder, "global_pose.txt")
        export_global_poses_txt(poses_data, output_path)
        
        print(f"✅ 全局姿态已导出: {output_path}")
        
        # 统计信息
        print(f"\n📊 统计信息:")
        print(f"   已注册图像数: {len(poses_data)}")
        
        print(f"\n💡 输出格式说明:")
        print(f"   第一行: 相机数量")
        print(f"   后续每行: 图像名 R00 R10 R20 R01 R11 R21 R02 R12 R22 tx ty tz")
        print(f"   其中R为旋转矩阵(按列优先)，t为平移向量")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 