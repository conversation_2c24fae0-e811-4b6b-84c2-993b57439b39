#!/usr/bin/env python3
#use glomap to do the pipeline

import os
import sys
import shutil
import logging
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

def detect_existing_conda():
    """
    检测现有conda安装，与install_colmap.sh逻辑保持一致
    
    Returns:
        (conda_executable, conda_base_dir, use_existing_conda)
    """
    # 检测常见的conda安装位置和可执行文件
    conda_candidates = [
        "conda",
        "mamba", 
        "micromamba",
        str(Path.home() / "miniforge3" / "bin" / "conda"),
        str(Path.home() / "miniconda3" / "bin" / "conda"),
        str(Path.home() / "anaconda3" / "bin" / "conda"),
        "/opt/conda/bin/conda",
        "/opt/miniconda3/bin/conda",
        "/opt/anaconda3/bin/conda",
        "/usr/local/bin/conda",
        "/usr/bin/conda"
    ]
    
    # 如果conda已在PATH中，优先使用
    for candidate in conda_candidates:
        if shutil.which(candidate):
            try:
                # 测试conda是否正常工作
                result = subprocess.run([candidate, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✓ 发现现有conda: {candidate}")
                    
                    # 获取conda的base目录
                    info_result = subprocess.run([candidate, "info", "--base"], 
                                                capture_output=True, text=True, timeout=10)
                    if info_result.returncode == 0:
                        conda_base = Path(info_result.stdout.strip())
                        print(f"  conda base目录: {conda_base}")
                        return candidate, conda_base, True
            except (subprocess.TimeoutExpired, Exception) as e:
                print(f"  警告: 测试conda {candidate} 失败: {e}")
                continue
    
    print("! 未发现可用的conda安装")
    return None, None, False

def setup_conda_environment():
    """
    自动设置和激活conda环境，智能检测现有conda或本地miniforge
    
    Returns:
        是否成功设置环境
    """
    # 获取项目根目录和dependencies目录
    script_dir = Path(__file__).parent.absolute()
    project_dir = script_dir.parent.parent
    dependencies_dir = project_dir / "dependencies"
    
    # 步骤1: 智能检测现有conda环境
    conda_executable, conda_base_dir, use_existing_conda = detect_existing_conda()
    
    if use_existing_conda:
        # 使用现有conda环境
        print(f"✓ 使用现有conda环境: {conda_executable}")
        envs_dir = conda_base_dir / "envs"
    else:
        # 查找本地安装的miniforge
        miniforge_dir = dependencies_dir / "miniforge3"
        if not miniforge_dir.exists():
            print("❌ 错误: 既没有找到现有conda环境，也没有找到本地Miniforge3")
            print(f"   查找路径: {miniforge_dir}")
            print("\n解决方案:")
            print("1. 运行安装脚本: cd dependencies && ./install_colmap.sh")
            print("2. 或手动安装: ./dependencies/install_colmap.sh")
            return False
        
        print(f"✓ 使用本地miniforge: {miniforge_dir}")
        conda_executable = str(miniforge_dir / "bin" / "conda")
        envs_dir = miniforge_dir / "envs"
    
    # 步骤2: 查找pycolmap环境
    env_dir = envs_dir / "pycolmap_env"
    if not env_dir.exists():
        # 兼容旧环境名
        env_dir = envs_dir / "sfm_env"
        if not env_dir.exists():
            print("❌ 错误: pycolmap_env 或 sfm_env 环境不存在")
            print(f"   查找路径: {envs_dir / 'pycolmap_env'}")
            print(f"   备选路径: {envs_dir / 'sfm_env'}")
            print("\n解决方案: 重新运行安装脚本")
            return False
    
    print(f"✓ 找到conda环境: {env_dir}")
    
    # 步骤3: 获取conda环境中的Python路径
    conda_python = env_dir / "bin" / "python"
    if not conda_python.exists():
        print("❌ 错误: conda 环境中的Python不存在")
        print(f"   查找路径: {conda_python}")
        return False
    
    # 步骤4: 检查是否已经在使用conda环境的Python
    current_python = Path(sys.executable).resolve()
    target_python = conda_python.resolve()
    
    if current_python != target_python:
        print("🔄 切换到conda环境中的Python解释器...")
        print(f"   当前Python: {current_python}")
        print(f"   目标Python: {target_python}")
        
        # 重新启动脚本使用conda环境的Python
        try:
            # 使用conda环境的Python重新运行当前脚本
            result = subprocess.run([str(conda_python)] + sys.argv, 
                                  cwd=os.getcwd())
            sys.exit(result.returncode)
        except Exception as e:
            print(f"❌ 重新启动脚本失败: {e}")
            return False
    
    # 步骤5: 设置环境变量
    conda_prefix = str(env_dir)
    os.environ['CONDA_PREFIX'] = conda_prefix
    # 根据实际环境目录设置环境名
    env_name = env_dir.name  # 获取环境目录名称
    os.environ['CONDA_DEFAULT_ENV'] = env_name
    
    # 更新PATH
    bin_paths = [str(env_dir / "bin")]
    
    if use_existing_conda:
        # 现有conda环境，添加conda的bin路径
        bin_paths.append(str(conda_base_dir / "bin"))
        if (conda_base_dir / "condabin").exists():
            bin_paths.append(str(conda_base_dir / "condabin"))
    else:
        # 本地miniforge，添加miniforge的路径
        miniforge_dir = dependencies_dir / "miniforge3"
        bin_paths.extend([
            str(miniforge_dir / "bin"),
            str(miniforge_dir / "condabin")
        ])
    
    current_path = os.environ.get('PATH', '')
    new_path = ':'.join(bin_paths + [current_path])
    os.environ['PATH'] = new_path
    
    print(f"✅ 已激活conda环境 {env_name}")
    print(f"   Python路径: {sys.executable}")
    
    return True

# 自动设置conda环境
if not setup_conda_environment():
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_glomap_command(cmd_args: list, description: str = "") -> bool:
    """
    运行 GLOMAP 命令
    
    Args:
        cmd_args: 命令参数列表
        description: 命令描述
        
    Returns:
        是否成功执行
    """
    try:
        if description:
            logger.info(f"执行: {description}")
        
        logger.info(f"命令: {' '.join(cmd_args)}")
        
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            check=False
        )
        
        if result.returncode == 0:
            logger.info(f"✓ {description} 成功完成")
            if result.stdout.strip():
                logger.debug(f"输出: {result.stdout}")
            return True
        else:
            logger.error(f"✗ {description} 失败")
            logger.error(f"错误代码: {result.returncode}")
            if result.stderr.strip():
                logger.error(f"错误信息: {result.stderr}")
            if result.stdout.strip():
                logger.error(f"输出信息: {result.stdout}")
            return False
            
    except FileNotFoundError:
        logger.error(f"✗ GLOMAP 命令未找到，请检查环境配置")
        return False
    except Exception as e:
        logger.error(f"✗ 执行命令时发生异常: {e}")
        return False

def glomap_mapper(database_path: str, image_path: str, output_path: str, glomap_bin_path: str) -> bool:
    """
    GLOMAP Mapper - 全局优化的SfM重建
    
    Args:
        database_path: 数据库路径
        image_path: 图像路径
        output_path: 输出路径
        glomap_bin_path: glomap二进制文件路径
        
    Returns:
        是否成功
    """
    # 构建glomap完整路径
    glomap_executable = os.path.join(glomap_bin_path, "glomap")
    
    cmd = [
        glomap_executable, "mapper",
        "--database_path", database_path,
        "--image_path", image_path,
        "--output_path", output_path
    ]
    
    return run_glomap_command(cmd, "GLOMAP 全局优化重建")

def glomap_pipeline(database_path: str, image_path: str, output_path: str, glomap_bin_path: str) -> bool:
    """
    GLOMAP 重建完整流程
    
    Args:
        database_path: 数据库路径 (通常由COLMAP生成)
        image_path: 图像路径
        output_path: 输出路径
        glomap_bin_path: glomap二进制文件路径
        
    Returns:
        是否成功完成重建
    """
    try:
        logger.info("="*60)
        logger.info("开始 GLOMAP 全局优化重建流程")
        logger.info("="*60)
        
        # 验证输入路径
        db_path = Path(database_path)
        if not db_path.exists():
            logger.error(f"数据库文件不存在: {db_path}")
            return False
        
        img_path = Path(image_path)
        if not img_path.exists():
            logger.error(f"图像路径不存在: {img_path}")
            return False
        
        # 创建输出目录
        out_path = Path(output_path)
        out_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据库路径: {db_path}")
        logger.info(f"图像路径: {img_path}")
        logger.info(f"输出路径: {out_path}")
        
        # 执行GLOMAP映射
        logger.info("\n--- 执行 GLOMAP 全局优化重建 ---")
        if not glomap_mapper(
            database_path=str(db_path),
            image_path=str(img_path),
            output_path=str(out_path),
            glomap_bin_path=glomap_bin_path
        ):
            return False
        
        # 检查结果
        # GLOMAP通常会在输出路径直接生成模型文件
        model_files = ["cameras.txt", "images.txt", "points3D.txt"]
        success_count = 0
        
        for file in model_files:
            file_path = out_path / file
            if file_path.exists():
                logger.info(f"  - {file}: ✓")
                success_count += 1
            else:
                logger.warning(f"  - {file}: ✗")
        
        if success_count > 0:
            logger.info(f"\n✓ 重建成功！模型保存在: {out_path}")
            logger.info(f"  生成文件数: {success_count}/{len(model_files)}")
        else:
            logger.warning("重建完成但未找到预期的模型文件")
        
        logger.info("="*60)
        logger.info("GLOMAP 全局优化重建流程完成!")
        logger.info(f"结果保存在: {out_path}")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"流程执行失败: {e}")
        return False

def main():
    """
    命令行入口函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(
        description='GLOMAP 全局优化重建流程',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--database_path', required=True,
                       help='数据库文件路径 (通常由COLMAP生成的database.db)')
    parser.add_argument('--image_path', required=True,
                       help='图像文件夹路径')
    parser.add_argument('--output_path', required=True,
                       help='输出文件夹路径')
    parser.add_argument('--glomap_bin_path', required=True,
                       help='GLOMAP二进制文件目录路径')
    
    args = parser.parse_args()
    
    success = glomap_pipeline(
        database_path=args.database_path,
        image_path=args.image_path,
        output_path=args.output_path,
        glomap_bin_path=args.glomap_bin_path
    )
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main() 