#!/usr/bin/env python3
"""
COLMAP数据库加载器

读取COLMAP生成的数据库文件，提取特征数据、匹配数据和SfM数据
使用pycolmap进行数据读取和处理
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# 添加当前目录到 Python 路径以便导入 colmap_pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置conda环境 - 必须在导入numpy和pycolmap之前
from colmap_pipeline import setup_conda_environment
if not setup_conda_environment():
    sys.exit(1)

# 环境设置完成后再导入需要conda环境的包
try:
    import numpy as np
    import sqlite3
    import struct
    import logging
    from dataclasses import dataclass
    print("✓ 成功加载 numpy 和 sqlite3")
except ImportError as e:
    print("❌ 错误: 无法导入必要模块")
    print(f"   详细错误: {e}")
    print("解决方案: 重新运行安装脚本添加numpy: cd dependencies && ./install_colmap.sh")
    sys.exit(1)

try:
    import pycolmap
    print(f"✓ 成功加载 pycolmap 版本: {pycolmap.__version__}")
    
    # 展示pycolmap的主要模块和功能
    print("\n📦 pycolmap 可用模块:")
    print("="*50)
    
    # 获取所有公共属性
    pycolmap_attrs = [attr for attr in dir(pycolmap) if not attr.startswith('_')]
    
    # 分类展示
    classes = []
    functions = []
    modules = []
    others = []
    
    for attr in pycolmap_attrs:
        obj = getattr(pycolmap, attr)
        if hasattr(obj, '__module__') and hasattr(obj, '__name__'):
            if str(type(obj)) == "<class 'type'>":  # 类
                classes.append(attr)
            elif callable(obj):  # 函数
                functions.append(attr)
            elif hasattr(obj, '__path__') or str(type(obj)).find('module') != -1:  # 模块
                modules.append(attr)
            else:
                others.append(attr)
        else:
            others.append(attr)
    
    if classes:
        print(f"🏗️  主要类 ({len(classes)}):")
        for cls in sorted(classes):
            try:
                doc = getattr(pycolmap, cls).__doc__
                desc = doc.split('\n')[0] if doc else "无描述"
                print(f"   • {cls:<20} - {desc[:60]}")
            except:
                print(f"   • {cls}")
    
    if functions:
        print(f"\n🔧 主要函数 ({len(functions)}):")
        for func in sorted(functions):
            try:
                doc = getattr(pycolmap, func).__doc__
                desc = doc.split('\n')[0] if doc else "无描述"
                print(f"   • {func:<20} - {desc[:60]}")
            except:
                print(f"   • {func}")
    
    if modules:
        print(f"\n📁 子模块 ({len(modules)}):")
        for mod in sorted(modules):
            print(f"   • {mod}")
    
    if others:
        print(f"\n📋 其他属性 ({len(others)}):")
        for other in sorted(others):
            print(f"   • {other}")
    
    print("="*50)
    
    # 检查是否有Database类
    if not hasattr(pycolmap, 'Database'):
        print("\n⚠️  注意: pycolmap 0.5.0版本没有Database类")
        print("   将使用sqlite3直接读取COLMAP数据库")
    
except ImportError as e:
    print("❌ 错误: 无法导入 pycolmap")
    print(f"   详细错误: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ImageInfo:
    """图像信息"""
    image_id: int
    name: str
    camera_id: int
    qvec: np.ndarray = None  # 四元数 (w, x, y, z)
    tvec: np.ndarray = None  # 平移向量 (x, y, z)
    point2D_ids: np.ndarray = None  # 2D点ID数组
    xys: np.ndarray = None  # 2D坐标数组

@dataclass
class CameraInfo:
    """相机信息"""
    camera_id: int
    model: str
    width: int
    height: int
    params: np.ndarray

@dataclass 
class Point3DInfo:
    """3D点信息"""
    point3D_id: int
    xyz: np.ndarray  # 3D坐标
    rgb: np.ndarray  # RGB颜色
    error: float     # 重投影误差
    track: List[Tuple[int, int]]  # (image_id, point2D_idx)

@dataclass
class FeatureInfo:
    """特征点信息"""
    image_id: int
    keypoints: np.ndarray  # 关键点坐标 (N, 2)
    descriptors: Optional[np.ndarray] = None  # 描述子 (N, 128)

@dataclass
class MatchInfo:
    """匹配信息"""
    image_id1: int
    image_id2: int
    matches: np.ndarray  # 匹配对 (N, 2)

class ColmapDatabaseLoader:
    """COLMAP数据库加载器"""
    
    def __init__(self, database_path: str, reconstruction_path: Optional[str] = None):
        """
        初始化加载器
        
        Args:
            database_path: COLMAP数据库文件路径
            reconstruction_path: 重建结果路径（包含sparse文件夹或模型文件）
        """
        self.database_path = Path(database_path)
        self.reconstruction_path = Path(reconstruction_path) if reconstruction_path else None
        
        if not self.database_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {self.database_path}")
        
        # 打开数据库
        try:
            if hasattr(pycolmap, 'Database'):
                # pycolmap有Database类的版本
                self.database = pycolmap.Database(str(self.database_path))
                self.use_pycolmap_db = True
                logger.info(f"✓ 使用pycolmap.Database打开数据库: {self.database_path}")
            else:
                # pycolmap没有Database类，使用sqlite3
                self.connection = sqlite3.connect(str(self.database_path))
                self.connection.row_factory = sqlite3.Row
                self.use_pycolmap_db = False
                logger.info(f"✓ 使用sqlite3打开数据库: {self.database_path}")
        except Exception as e:
            logger.error(f"无法打开数据库: {e}")
            raise
        
        # 加载重建结果（如果提供）
        self.reconstruction = None
        if self.reconstruction_path and self.reconstruction_path.exists():
            try:
                # 检查是否是sparse文件夹
                if self.reconstruction_path.is_dir():
                    model_path = self.reconstruction_path / "0"  # 默认模型0
                    if model_path.exists():
                        self.reconstruction = pycolmap.Reconstruction(str(model_path))
                    else:
                        self.reconstruction = pycolmap.Reconstruction(str(self.reconstruction_path))
                else:
                    self.reconstruction = pycolmap.Reconstruction(str(self.reconstruction_path))
                
                logger.info(f"✓ 加载重建结果: {self.reconstruction_path}")
            except Exception as e:
                logger.warning(f"无法加载重建结果: {e}")
    
    def _blob_to_array(self, blob, dtype=np.float32, shape=(-1,)):
        """将数据库中的二进制blob转换为numpy数组"""
        if blob is None:
            return None
        if sys.version_info[0] >= 3:
            return np.fromstring(blob, dtype=dtype).reshape(*shape)
        else:
            return np.frombuffer(blob, dtype=dtype).reshape(*shape)
    
    def _image_ids_to_pair_id(self, image_id1, image_id2):
        """计算图像对的pair_id"""
        MAX_IMAGE_ID = 2**31 - 1
        if image_id1 > image_id2:
            image_id1, image_id2 = image_id2, image_id1
        return image_id1 * MAX_IMAGE_ID + image_id2
    
    def _pair_id_to_image_ids(self, pair_id):
        """从pair_id解析出图像ID"""
        MAX_IMAGE_ID = 2**31 - 1
        image_id2 = pair_id % MAX_IMAGE_ID
        image_id1 = (pair_id - image_id2) // MAX_IMAGE_ID
        return int(image_id1), int(image_id2)
    
    def _read_images_sqlite(self) -> Dict[int, ImageInfo]:
        """使用sqlite3读取图像信息"""
        images_info = {}
        cursor = self.connection.cursor()
        cursor.execute("SELECT image_id, name, camera_id FROM images")
        
        for row in cursor.fetchall():
            images_info[row['image_id']] = ImageInfo(
                image_id=row['image_id'],
                name=row['name'],
                camera_id=row['camera_id'],
                qvec=np.array([1.0, 0.0, 0.0, 0.0]),  # 默认四元数
                tvec=np.array([0.0, 0.0, 0.0])        # 默认平移
            )
        
        return images_info
    
    def _read_cameras_sqlite(self) -> Dict[int, CameraInfo]:
        """使用sqlite3读取相机信息"""
        cameras_info = {}
        cursor = self.connection.cursor()
        cursor.execute("SELECT camera_id, model, width, height, params FROM cameras")
        
        for row in cursor.fetchall():
            # 解析相机参数 (blob -> float64 array)
            params = self._blob_to_array(row['params'], dtype=np.float64)
            
            cameras_info[row['camera_id']] = CameraInfo(
                camera_id=row['camera_id'],
                model=self._get_camera_model_name(row['model']),
                width=row['width'],
                height=row['height'],
                params=params
            )
        
        return cameras_info
    
    def _get_camera_model_name(self, model_id):
        """根据模型ID获取模型名称"""
        model_names = {
            0: 'SIMPLE_PINHOLE',
            1: 'PINHOLE', 
            2: 'SIMPLE_RADIAL',
            3: 'RADIAL',
            4: 'OPENCV',
            5: 'OPENCV_FISHEYE',
            6: 'FULL_OPENCV',
            7: 'FOV',
            8: 'SIMPLE_RADIAL_FISHEYE',
            9: 'RADIAL_FISHEYE',
            10: 'THIN_PRISM_FISHEYE'
        }
        return model_names.get(model_id, f'UNKNOWN_{model_id}')
    
    def _read_keypoints_sqlite(self, image_id: int) -> Optional[np.ndarray]:
        """使用sqlite3读取关键点"""
        cursor = self.connection.cursor()
        cursor.execute("SELECT rows, cols, data FROM keypoints WHERE image_id = ?", (image_id,))
        row = cursor.fetchone()
        
        if row is None:
            return None
        
        # 解析关键点数据 (blob -> float32 array -> reshape)
        keypoints = self._blob_to_array(row['data'], dtype=np.float32, shape=(-1, row['cols']))
        return keypoints
    
    def _read_descriptors_sqlite(self, image_id: int) -> Optional[np.ndarray]:
        """使用sqlite3读取描述子"""
        cursor = self.connection.cursor()
        cursor.execute("SELECT rows, cols, data FROM descriptors WHERE image_id = ?", (image_id,))
        row = cursor.fetchone()
        
        if row is None:
            return None
        
        # 解析描述子数据 (blob -> uint8 array -> reshape)  
        descriptors = self._blob_to_array(row['data'], dtype=np.uint8, shape=(-1, row['cols']))
        return descriptors
    
    def _read_matches_sqlite(self, image_id1: int, image_id2: int) -> Optional[np.ndarray]:
        """使用sqlite3读取匹配"""
        # 计算pair_id (使用正确的COLMAP算法)
        pair_id = self._image_ids_to_pair_id(image_id1, image_id2)
            
        cursor = self.connection.cursor()
        cursor.execute("SELECT rows, cols, data FROM matches WHERE pair_id = ?", (pair_id,))
        row = cursor.fetchone()
        
        if row is None:
            return None
        
        # 解析匹配数据 (blob -> uint32 array -> reshape)
        matches = self._blob_to_array(row['data'], dtype=np.uint32, shape=(-1, 2))
        return matches

    def get_images_info(self) -> Dict[int, ImageInfo]:
        """获取所有图像信息"""
        images_info = {}
        
        if self.reconstruction:
            # 从重建结果获取图像信息
            for image_id, image in self.reconstruction.images.items():
                images_info[image_id] = ImageInfo(
                    image_id=image_id,
                    name=image.name,
                    camera_id=image.camera_id,
                    qvec=image.qvec,
                    tvec=image.tvec,
                    point2D_ids=image.point2D_ids,
                    xys=image.points2D
                )
        elif self.use_pycolmap_db:
            # 使用pycolmap Database API
            images = self.database.read_all_images()
            for image in images:
                images_info[image.image_id] = ImageInfo(
                    image_id=image.image_id,
                    name=image.name,
                    camera_id=image.camera_id,
                    qvec=np.array([1.0, 0.0, 0.0, 0.0]),  # 默认四元数
                    tvec=np.array([0.0, 0.0, 0.0])        # 默认平移
                )
        else:
            # 使用sqlite3直接读取
            images_info = self._read_images_sqlite()
        
        logger.info(f"✓ 加载了 {len(images_info)} 张图像信息")
        return images_info
    
    def get_cameras_info(self) -> Dict[int, CameraInfo]:
        """获取所有相机信息"""
        cameras_info = {}
        
        if self.reconstruction:
            # 从重建结果获取相机信息
            for camera_id, camera in self.reconstruction.cameras.items():
                cameras_info[camera_id] = CameraInfo(
                    camera_id=camera_id,
                    model=camera.model_name,
                    width=camera.width,
                    height=camera.height,
                    params=camera.params
                )
        elif self.use_pycolmap_db:
            # 使用pycolmap Database API
            cameras = self.database.read_all_cameras()
            for camera in cameras:
                cameras_info[camera.camera_id] = CameraInfo(
                    camera_id=camera.camera_id,
                    model=camera.model_name,
                    width=camera.width,
                    height=camera.height,
                    params=camera.params
                )
        else:
            # 使用sqlite3直接读取
            cameras_info = self._read_cameras_sqlite()
        
        logger.info(f"✓ 加载了 {len(cameras_info)} 个相机信息")
        return cameras_info
    
    def get_points3D_info(self) -> Dict[int, Point3DInfo]:
        """获取所有3D点信息"""
        points3D_info = {}
        
        if self.reconstruction:
            for point3D_id, point3D in self.reconstruction.points3D.items():
                track = [(track_element.image_id, track_element.point2D_idx) 
                        for track_element in point3D.track.elements]
                
                points3D_info[point3D_id] = Point3DInfo(
                    point3D_id=point3D_id,
                    xyz=point3D.xyz,
                    rgb=point3D.color,
                    error=point3D.error,
                    track=track
                )
            
            logger.info(f"✓ 加载了 {len(points3D_info)} 个3D点")
        else:
            logger.warning("没有重建结果，无法获取3D点信息")
        
        return points3D_info
    
    def get_features(self, image_id: int) -> Optional[FeatureInfo]:
        """获取指定图像的特征点"""
        try:
            if self.use_pycolmap_db:
                # 使用pycolmap Database API
                keypoints = self.database.read_keypoints(image_id)
                descriptors = self.database.read_descriptors(image_id)
            else:
                # 使用sqlite3直接读取
                keypoints = self._read_keypoints_sqlite(image_id)
                descriptors = self._read_descriptors_sqlite(image_id)
            
            if keypoints is not None:
                return FeatureInfo(
                    image_id=image_id,
                    keypoints=keypoints,
                    descriptors=descriptors
                )
            else:
                return None
        except Exception as e:
            logger.warning(f"无法读取图像 {image_id} 的特征: {e}")
            return None
    
    def get_all_features(self) -> Dict[int, FeatureInfo]:
        """获取所有图像的特征点"""
        features = {}
        images_info = self.get_images_info()
        
        for image_id in images_info.keys():
            feature_info = self.get_features(image_id)
            if feature_info:
                features[image_id] = feature_info
        
        logger.info(f"✓ 加载了 {len(features)} 张图像的特征")
        return features
    
    def get_matches(self, image_id1: int, image_id2: int) -> Optional[MatchInfo]:
        """获取两张图像之间的匹配"""
        try:
            if self.use_pycolmap_db:
                # 使用pycolmap Database API
                matches = self.database.read_matches(image_id1, image_id2)
            else:
                # 使用sqlite3直接读取
                matches = self._read_matches_sqlite(image_id1, image_id2)
            
            if matches is not None and len(matches) > 0:
                return MatchInfo(
                    image_id1=image_id1,
                    image_id2=image_id2,
                    matches=matches
                )
            else:
                return None
        except Exception as e:
            logger.warning(f"无法读取图像 {image_id1}-{image_id2} 的匹配: {e}")
            return None
    
    def get_all_matches(self) -> List[MatchInfo]:
        """获取所有图像对的匹配"""
        matches = []
        
        if self.use_pycolmap_db:
            # 使用pycolmap Database API
            match_pairs = self.database.read_all_matches()
            
            for (image_id1, image_id2), match_array in match_pairs.items():
                if len(match_array) > 0:
                    matches.append(MatchInfo(
                        image_id1=image_id1,
                        image_id2=image_id2,
                        matches=match_array
                    ))
        else:
            # 使用sqlite3直接读取所有匹配
            cursor = self.connection.cursor()
            cursor.execute("SELECT pair_id, rows, cols, data FROM matches WHERE rows > 0")
            
            for row in cursor.fetchall():
                # 解析pair_id获取image_id1和image_id2 (使用正确的COLMAP算法)
                pair_id = row['pair_id']
                image_id1, image_id2 = self._pair_id_to_image_ids(pair_id)
                
                # 解析匹配数据
                match_array = self._blob_to_array(row['data'], dtype=np.uint32, shape=(-1, 2))
                
                if match_array is not None and len(match_array) > 0:
                    matches.append(MatchInfo(
                        image_id1=image_id1,
                        image_id2=image_id2,
                        matches=match_array
                    ))
        
        logger.info(f"✓ 加载了 {len(matches)} 对图像匹配")
        return matches
    
    def print_summary(self):
        """打印数据库摘要信息"""
        print("\n" + "="*60)
        print("COLMAP 数据库摘要")
        print("="*60)
        
        # 基本信息
        print(f"数据库路径: {self.database_path}")
        if self.reconstruction_path:
            print(f"重建路径: {self.reconstruction_path}")
        
        # 图像和相机信息
        images_info = self.get_images_info()
        cameras_info = self.get_cameras_info()
        print(f"\n📷 图像和相机:")
        print(f"  - 图像数量: {len(images_info)}")
        print(f"  - 相机数量: {len(cameras_info)}")
        
        # 特征信息
        features = self.get_all_features()
        if features:
            total_features = sum(len(f.keypoints) for f in features.values())
            print(f"\n🎯 特征点:")
            print(f"  - 有特征的图像: {len(features)}")
            print(f"  - 总特征点数: {total_features}")
            avg_features = total_features / len(features) if features else 0
            print(f"  - 平均特征点数: {avg_features:.1f}")
        
        # 匹配信息
        matches = self.get_all_matches()
        if matches:
            total_matches = sum(len(m.matches) for m in matches)
            print(f"\n🔗 匹配:")
            print(f"  - 匹配对数: {len(matches)}")
            print(f"  - 总匹配点数: {total_matches}")
            avg_matches = total_matches / len(matches) if matches else 0
            print(f"  - 平均匹配点数: {avg_matches:.1f}")
        
        # 3D点信息（如果有重建结果）
        if self.reconstruction:
            points3D = self.get_points3D_info()
            registered_images = len([img for img in images_info.values() 
                                   if img.point2D_ids is not None and len(img.point2D_ids) > 0])
            print(f"\n🌍 3D重建:")
            print(f"  - 3D点数量: {len(points3D)}")
            print(f"  - 注册图像数: {registered_images}")
            
            if points3D:
                errors = [p.error for p in points3D.values()]
                print(f"  - 平均重投影误差: {np.mean(errors):.3f}")
        
        print("="*60)

def main():
    """命令行入口函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='COLMAP 数据库加载器和分析工具',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--database_path', required=True,
                       help='COLMAP数据库文件路径 (.db)')
    
    # 可选参数
    parser.add_argument('--reconstruction_path',
                       help='重建结果路径 (sparse文件夹或模型文件夹)')
    parser.add_argument('--summary', action='store_true', default=True,
                       help='显示数据库摘要信息')
    
    args = parser.parse_args()
    
    try:
        # 创建加载器
        loader = ColmapDatabaseLoader(
            database_path=args.database_path,
            reconstruction_path=args.reconstruction_path
        )
        
        # 显示摘要
        if args.summary:
            loader.print_summary()
        
        print("\n💡 使用示例:")
        print("# 在Python中使用:")
        print("from load_colmap_db import ColmapDatabaseLoader")
        print("loader = ColmapDatabaseLoader('database.db', 'sparse/0')")
        print("features = loader.get_all_features()")
        print("matches = loader.get_all_matches()")
        print("points3D = loader.get_points3D_info()")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()