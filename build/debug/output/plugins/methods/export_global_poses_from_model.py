#!/usr/bin/env python3

# Copyright (c), ETH Zurich and UNC Chapel Hill.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#
#     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
#       its contributors may be used to endorse or promote products derived
#       from this software without specific blame or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.

"""
COLMAP重建模型全局姿态导出工具

从COLMAP重建结果文件中读取相机姿态信息，并导出为global_poses.txt文件
输入可以是：
1. 包含cameras.txt/cameras.bin, images.txt/images.bin的模型文件夹
2. 数据库文件 + sparse重建结果文件夹

输出格式：
- 第一行：相机数量
- 后续每行：图像名 R00 R10 R20 R01 R11 R21 R02 R12 R22 tx ty tz
"""

import argparse
import os
import sys
import struct
import collections
import sqlite3

# 添加当前目录到 Python 路径以便导入 colmap_pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置conda环境 - 必须在导入numpy之前
from colmap_pipeline import setup_conda_environment
if not setup_conda_environment():
    sys.exit(1)

import numpy as np


# COLMAP数据结构定义
BaseImage = collections.namedtuple(
    "Image", ["id", "qvec", "tvec", "camera_id", "name", "xys", "point3D_ids"]
)

class Image(BaseImage):
    def qvec2rotmat(self):
        return qvec2rotmat(self.qvec)


def qvec2rotmat(qvec):
    """将四元数转换为旋转矩阵"""
    return np.array([
        [1 - 2 * qvec[2]**2 - 2 * qvec[3]**2,
         2 * qvec[1] * qvec[2] - 2 * qvec[0] * qvec[3],
         2 * qvec[3] * qvec[1] + 2 * qvec[0] * qvec[2]],
        [2 * qvec[1] * qvec[2] + 2 * qvec[0] * qvec[3],
         1 - 2 * qvec[1]**2 - 2 * qvec[3]**2,
         2 * qvec[2] * qvec[3] - 2 * qvec[0] * qvec[1]],
        [2 * qvec[3] * qvec[1] - 2 * qvec[0] * qvec[2],
         2 * qvec[2] * qvec[3] + 2 * qvec[0] * qvec[1],
         1 - 2 * qvec[1]**2 - 2 * qvec[2]**2]
    ])


def read_next_bytes(fid, num_bytes, format_char_sequence, endian_character="<"):
    """从二进制文件读取并解包字节"""
    data = fid.read(num_bytes)
    return struct.unpack(endian_character + format_char_sequence, data)


def read_images_text(path):
    """读取COLMAP文本格式的images文件"""
    images = {}
    with open(path, "r") as fid:
        while True:
            line = fid.readline()
            if not line:
                break
            line = line.strip()
            if len(line) > 0 and line[0] != "#":
                elems = line.split()
                image_id = int(elems[0])
                qvec = np.array(tuple(map(float, elems[1:5])))
                tvec = np.array(tuple(map(float, elems[5:8])))
                camera_id = int(elems[8])
                image_name = elems[9]
                elems = fid.readline().split()
                xys = np.column_stack([
                    tuple(map(float, elems[0::3])),
                    tuple(map(float, elems[1::3]))
                ])
                point3D_ids = np.array(tuple(map(int, elems[2::3])))
                images[image_id] = Image(
                    id=image_id, qvec=qvec, tvec=tvec,
                    camera_id=camera_id, name=image_name,
                    xys=xys, point3D_ids=point3D_ids
                )
    return images


def read_images_binary(path_to_model_file):
    """读取COLMAP二进制格式的images文件"""
    images = {}
    with open(path_to_model_file, "rb") as fid:
        num_reg_images = read_next_bytes(fid, 8, "Q")[0]
        for _ in range(num_reg_images):
            binary_image_properties = read_next_bytes(
                fid, num_bytes=64, format_char_sequence="idddddddi"
            )
            image_id = binary_image_properties[0]
            qvec = np.array(binary_image_properties[1:5])
            tvec = np.array(binary_image_properties[5:8])
            camera_id = binary_image_properties[8]
            binary_image_name = b""
            current_char = read_next_bytes(fid, 1, "c")[0]
            while current_char != b"\x00":
                binary_image_name += current_char
                current_char = read_next_bytes(fid, 1, "c")[0]
            image_name = binary_image_name.decode("utf-8")
            num_points2D = read_next_bytes(fid, num_bytes=8, format_char_sequence="Q")[0]
            x_y_id_s = read_next_bytes(
                fid, num_bytes=24 * num_points2D, 
                format_char_sequence="ddq" * num_points2D
            )
            xys = np.column_stack([
                tuple(map(float, x_y_id_s[0::3])),
                tuple(map(float, x_y_id_s[1::3]))
            ])
            point3D_ids = np.array(tuple(map(int, x_y_id_s[2::3])))
            images[image_id] = Image(
                id=image_id, qvec=qvec, tvec=tvec,
                camera_id=camera_id, name=image_name,
                xys=xys, point3D_ids=point3D_ids
            )
    return images


def detect_model_format(path, ext):
    """检测模型文件格式"""
    return (os.path.isfile(os.path.join(path, f"cameras{ext}")) and 
            os.path.isfile(os.path.join(path, f"images{ext}")))


def read_images_from_model(model_path):
    """从COLMAP模型文件夹读取images"""
    print("🎯 从模型文件读取图像姿态...")
    
    # 自动检测格式
    if detect_model_format(model_path, ".bin"):
        ext = ".bin"
        print("   检测到二进制格式 (.bin)")
    elif detect_model_format(model_path, ".txt"):
        ext = ".txt"
        print("   检测到文本格式 (.txt)")
    else:
        raise ValueError(f"在 {model_path} 中找不到有效的COLMAP模型文件")
    
    images_path = os.path.join(model_path, f"images{ext}")
    
    if ext == ".txt":
        images = read_images_text(images_path)
    else:
        images = read_images_binary(images_path)
    
    print(f"   成功读取 {len(images)} 个图像的姿态信息")
    return images


def find_sparse_model(base_path):
    """查找sparse重建结果文件夹"""
    possible_paths = [
        os.path.join(base_path, "sparse", "0"),
        os.path.join(base_path, "sparse"),
        base_path
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            if detect_model_format(path, ".bin") or detect_model_format(path, ".txt"):
                return path
    
    return None


def export_global_poses_txt(images, output_path):
    """导出为global_poses.txt格式"""
    with open(output_path, 'w') as f:
        # 第一行：相机数量
        f.write(f"{len(images)}\n")
        
        # 每行：图像名 + 旋转矩阵(按列优先) + 平移向量
        for image in images.values():
            R = image.qvec2rotmat()
            tx, ty, tz = image.tvec
            
            # 按列优先顺序输出旋转矩阵：R(0,0) R(1,0) R(2,0) R(0,1) R(1,1) R(2,1) R(0,2) R(1,2) R(2,2)
            f.write(f"{image.name} "
                   f"{R[0,0]:.8f} {R[1,0]:.8f} {R[2,0]:.8f} "
                   f"{R[0,1]:.8f} {R[1,1]:.8f} {R[2,1]:.8f} "
                   f"{R[0,2]:.8f} {R[1,2]:.8f} {R[2,2]:.8f} "
                   f"{tx:.8f} {ty:.8f} {tz:.8f}\n")


def main():
    parser = argparse.ArgumentParser(
        description="从COLMAP模型文件导出全局姿态到global_poses.txt",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 输入参数 - 支持多种输入方式
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--model_folder', 
                           help='COLMAP模型文件夹路径 (包含cameras.txt/bin, images.txt/bin)')
    input_group.add_argument('--workspace_path',
                           help='COLMAP工作空间路径 (自动查找sparse重建结果)')
    
    # 输出参数
    parser.add_argument('--output_folder', required=True,
                       help='输出文件夹路径')
    
    args = parser.parse_args()
    
    try:
        print("="*60)
        print("COLMAP 全局姿态导出工具")
        print("="*60)
        print(f"输出文件夹: {args.output_folder}")
        
        # 确定模型路径
        if args.model_folder:
            model_path = args.model_folder
            print(f"模型路径: {model_path}")
        else:
            print(f"工作空间路径: {args.workspace_path}")
            model_path = find_sparse_model(args.workspace_path)
            if model_path is None:
                print("❌ 错误: 在工作空间中找不到sparse重建结果")
                sys.exit(1)
            print(f"找到模型路径: {model_path}")
        
        # 检查模型路径
        if not os.path.exists(model_path):
            print(f"❌ 错误: 模型路径不存在: {model_path}")
            sys.exit(1)
        
        print()
        
        # 创建输出文件夹
        os.makedirs(args.output_folder, exist_ok=True)
        
        # 读取图像姿态
        images = read_images_from_model(model_path)
        
        if len(images) == 0:
            print("⚠️  警告: 没有找到图像姿态数据")
            return
        
        # 导出global_poses.txt文件
        output_path = os.path.join(args.output_folder, "global_poses.txt")
        export_global_poses_txt(images, output_path)
        
        print(f"✅ 全局姿态已导出: {output_path}")
        
        # 统计信息
        print(f"\n📊 统计信息:")
        print(f"   图像数: {len(images)}")
        
        # 显示前几个图像信息
        print(f"\n📋 示例数据:")
        for i, (_, image) in enumerate(images.items()):
            if i >= 3:  # 只显示前3个
                break
            print(f"   {image.name}: camera_id={image.camera_id}")
        
        print(f"\n💡 输出格式说明:")
        print(f"   第一行: 相机数量")
        print(f"   后续每行: 图像名 R00 R10 R20 R01 R11 R21 R02 R12 R22 tx ty tz")
        print(f"   其中R为旋转矩阵(按列优先)，t为平移向量")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 