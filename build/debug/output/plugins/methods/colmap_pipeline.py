#!/usr/bin/env python3
#use colmap to do the pipeline

import os
import sys
import shutil
import logging
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

def detect_existing_conda():
    """
    检测现有conda安装，与install_colmap.sh逻辑保持一致
    
    Returns:
        (conda_executable, conda_base_dir, use_existing_conda)
    """
    # 检测常见的conda安装位置和可执行文件
    conda_candidates = [
        "conda",
        "mamba", 
        "micromamba",
        str(Path.home() / "miniforge3" / "bin" / "conda"),
        str(Path.home() / "miniconda3" / "bin" / "conda"),
        str(Path.home() / "anaconda3" / "bin" / "conda"),
        "/opt/conda/bin/conda",
        "/opt/miniconda3/bin/conda",
        "/opt/anaconda3/bin/conda",
        "/usr/local/bin/conda",
        "/usr/bin/conda"
    ]
    
    # 如果conda已在PATH中，优先使用
    for candidate in conda_candidates:
        if shutil.which(candidate):
            try:
                # 测试conda是否正常工作
                result = subprocess.run([candidate, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✓ 发现现有conda: {candidate}")
                    
                    # 获取conda的base目录
                    info_result = subprocess.run([candidate, "info", "--base"], 
                                                capture_output=True, text=True, timeout=10)
                    if info_result.returncode == 0:
                        conda_base = Path(info_result.stdout.strip())
                        print(f"  conda base目录: {conda_base}")
                        return candidate, conda_base, True
            except (subprocess.TimeoutExpired, Exception) as e:
                print(f"  警告: 测试conda {candidate} 失败: {e}")
                continue
    
    print("! 未发现可用的conda安装")
    return None, None, False

def setup_conda_environment():
    """
    自动设置和激活conda环境，智能检测现有conda或本地miniforge
    
    Returns:
        是否成功设置环境
    """
    # 获取项目根目录和dependencies目录
    script_dir = Path(__file__).parent.absolute()
    project_dir = script_dir.parent.parent
    dependencies_dir = project_dir / "dependencies"
    
    # 步骤1: 智能检测现有conda环境
    conda_executable, conda_base_dir, use_existing_conda = detect_existing_conda()
    
    if use_existing_conda:
        # 使用现有conda环境
        print(f"✓ 使用现有conda环境: {conda_executable}")
        envs_dir = conda_base_dir / "envs"
    else:
        # 查找本地安装的miniforge
        miniforge_dir = dependencies_dir / "miniforge3"
        if not miniforge_dir.exists():
            print("❌ 错误: 既没有找到现有conda环境，也没有找到本地Miniforge3")
            print(f"   查找路径: {miniforge_dir}")
            print("\n解决方案:")
            print("1. 运行安装脚本: cd dependencies && ./install_colmap.sh")
            print("2. 或手动安装: ./dependencies/install_colmap.sh")
            return False
        
        print(f"✓ 使用本地miniforge: {miniforge_dir}")
        conda_executable = str(miniforge_dir / "bin" / "conda")
        envs_dir = miniforge_dir / "envs"
    
    # 步骤2: 查找pycolmap环境
    env_dir = envs_dir / "pycolmap_env"
    if not env_dir.exists():
        # 兼容旧环境名
        env_dir = envs_dir / "sfm_env"
        if not env_dir.exists():
            print("❌ 错误: pycolmap_env 或 sfm_env 环境不存在")
            print(f"   查找路径: {envs_dir / 'pycolmap_env'}")
            print(f"   备选路径: {envs_dir / 'sfm_env'}")
            print("\n解决方案: 重新运行安装脚本")
            return False
    
    print(f"✓ 找到conda环境: {env_dir}")
    
    # 步骤3: 获取conda环境中的Python路径
    conda_python = env_dir / "bin" / "python"
    if not conda_python.exists():
        print("❌ 错误: conda 环境中的Python不存在")
        print(f"   查找路径: {conda_python}")
        return False
    
    # 步骤4: 检查是否已经在使用conda环境的Python
    current_python = Path(sys.executable).resolve()
    target_python = conda_python.resolve()
    
    if current_python != target_python:
        print("🔄 切换到conda环境中的Python解释器...")
        print(f"   当前Python: {current_python}")
        print(f"   目标Python: {target_python}")
        
        # 重新启动脚本使用conda环境的Python
        try:
            # 使用conda环境的Python重新运行当前脚本
            result = subprocess.run([str(conda_python)] + sys.argv, 
                                  cwd=os.getcwd())
            sys.exit(result.returncode)
        except Exception as e:
            print(f"❌ 重新启动脚本失败: {e}")
            return False
    
    # 步骤5: 设置环境变量
    conda_prefix = str(env_dir)
    os.environ['CONDA_PREFIX'] = conda_prefix
    # 根据实际环境目录设置环境名
    env_name = env_dir.name  # 获取环境目录名称
    os.environ['CONDA_DEFAULT_ENV'] = env_name
    
    # 更新PATH
    bin_paths = [str(env_dir / "bin")]
    
    if use_existing_conda:
        # 现有conda环境，添加conda的bin路径
        bin_paths.append(str(conda_base_dir / "bin"))
        if (conda_base_dir / "condabin").exists():
            bin_paths.append(str(conda_base_dir / "condabin"))
    else:
        # 本地miniforge，添加miniforge的路径
        miniforge_dir = dependencies_dir / "miniforge3"
        bin_paths.extend([
            str(miniforge_dir / "bin"),
            str(miniforge_dir / "condabin")
        ])
    
    current_path = os.environ.get('PATH', '')
    new_path = ':'.join(bin_paths + [current_path])
    os.environ['PATH'] = new_path
    
    print(f"✅ 已激活conda环境 {env_name}")
    print(f"   Python路径: {sys.executable}")
    
    return True

# 自动设置conda环境
if not setup_conda_environment():
    sys.exit(1)

# 尝试导入pycolmap
try:
    import pycolmap
    print(f"✓ 成功加载 pycolmap 版本: {pycolmap.__version__}")
except ImportError as e:
    print("❌ 错误: 无法导入 pycolmap")
    print(f"   详细错误: {e}")
    print("\n解决方案:")
    print("1. 检查环境是否正确安装: ./dependencies/install_colmap.sh")
    print("2. 手动测试: source dependencies/miniforge3/bin/activate && conda activate sfm_env && python -c 'import pycolmap'")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 使用pycolmap库进行无头处理，避免Qt和OpenGL依赖

# 注意：这个函数现在不再使用，因为我们使用pycolmap库
# def run_colmap_command(...): 已移除，现在使用pycolmap API

def colmap_feature_extractor(database_path: str, image_path: str,
                           camera_model: str = "PINHOLE",
                           num_threads: int = 8) -> bool:
    """
    COLMAP 特征提取 (使用pycolmap)
    
    Args:
        database_path: 数据库路径
        image_path: 图像路径
        camera_model: 相机模型
        num_threads: 线程数
        
    Returns:
        是否成功
    """
    try:
        logger.info("开始特征提取...")
        logger.info("使用默认的特征提取选项")
        
        # 执行特征提取 - 使用默认选项
        pycolmap.extract_features(database_path, image_path)
        
        logger.info("✓ 特征提取成功完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 特征提取失败: {e}")
        return False

def colmap_exhaustive_matcher(database_path: str,
                            num_threads: int = 8,
                            max_ratio: float = 0.8,
                            max_distance: float = 0.7,
                            cross_check: bool = True) -> bool:
    """
    COLMAP 穷举匹配 (使用pycolmap)
    
    Args:
        database_path: 数据库路径
        num_threads: 线程数
        max_ratio: 最大比值
        max_distance: 最大距离
        cross_check: 是否交叉检查
        
    Returns:
        是否成功
    """
    try:
        logger.info("开始特征匹配...")
        logger.info("使用默认的匹配选项")
        
        # 执行穷举匹配 - 使用默认选项
        pycolmap.match_exhaustive(database_path)
        
        logger.info("✓ 特征匹配成功完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 特征匹配失败: {e}")
        return False

def colmap_mapper(database_path: str, image_path: str, output_path: str,
                 num_threads: int = 8,
                 init_min_num_inliers: int = 100,
                 extract_colors: bool = True,
                 min_num_matches: int = 15) -> bool:
    """
    COLMAP 增量式重建 (使用pycolmap)
    
    Args:
        database_path: 数据库路径
        image_path: 图像路径
        output_path: 输出路径
        num_threads: 线程数
        init_min_num_inliers: 初始最小内点数
        extract_colors: 是否提取颜色
        min_num_matches: 最小匹配数
        
    Returns:
        是否成功
    """
    try:
        logger.info("开始增量式重建...")
        
        # 使用默认选项，避免复杂的参数设置
        logger.info("使用默认的重建选项")
        
        # 执行增量式重建 - 使用默认选项
        maps = pycolmap.incremental_mapping(
            database_path=database_path,
            image_path=image_path,
            output_path=output_path
        )
        
        if maps:
            logger.info(f"✓ 增量式重建成功完成，生成了 {len(maps)} 个重建模型")
            return True
        else:
            logger.warning("增量式重建完成，但未生成有效模型")
            return False
        
    except Exception as e:
        logger.error(f"✗ 增量式重建失败: {e}")
        return False

def colmap_pipeline(image_folder: str, output_folder: str,
                   camera_model: str = "PINHOLE",
                   num_threads: int = 8,
                   max_ratio: float = 0.8,
                   max_distance: float = 0.7,
                   init_min_num_inliers: int = 100,
                   min_num_matches: int = 15) -> bool:
    """
    COLMAP 增量式重建完整流程
    
    Args:
        image_folder: 输入图像文件夹路径
        output_folder: 输出文件夹路径
        camera_model: 相机模型 (PINHOLE, RADIAL, OPENCV, etc.)
        num_threads: 线程数
        max_ratio: SIFT匹配最大比值
        max_distance: SIFT匹配最大距离
        init_min_num_inliers: 初始最小内点数
        min_num_matches: 最小匹配数
        
    Returns:
        是否成功完成重建
    """
    try:
        logger.info("="*60)
        logger.info("开始 COLMAP 增量式重建流程 (使用pycolmap库)")
        logger.info("="*60)
        
        # 验证输入路径
        image_path = Path(image_folder)
        if not image_path.exists():
            logger.error(f"输入图像路径不存在: {image_path}")
            return False
        
        # 创建输出目录
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 设置路径
        database_path = output_path / "database.db"
        sparse_path = output_path / "sparse"
        sparse_path.mkdir(exist_ok=True)
        
        logger.info(f"图像路径: {image_path}")
        logger.info(f"输出路径: {output_path}")
        logger.info(f"数据库路径: {database_path}")
        logger.info(f"稀疏模型路径: {sparse_path}")
        
        # 步骤1: 特征提取
        logger.info("\n--- 步骤 1: 特征提取 ---")
        if not colmap_feature_extractor(
            database_path=str(database_path),
            image_path=str(image_path),
            camera_model=camera_model,
            num_threads=num_threads
        ):
            return False
        
        # 步骤2: 特征匹配
        logger.info("\n--- 步骤 2: 特征匹配 ---")
        if not colmap_exhaustive_matcher(
            database_path=str(database_path),
            num_threads=num_threads,
            max_ratio=max_ratio,
            max_distance=max_distance
        ):
            return False
        
        # 步骤3: 增量式重建
        logger.info("\n--- 步骤 3: 增量式重建 ---")
        if not colmap_mapper(
            database_path=str(database_path),
            image_path=str(image_path),
            output_path=str(sparse_path),
            num_threads=num_threads,
            init_min_num_inliers=init_min_num_inliers,
            min_num_matches=min_num_matches
        ):
            return False
        
        # 检查结果
        model_path = sparse_path / "0"
        if model_path.exists():
            logger.info(f"\n✓ 重建成功！模型保存在: {model_path}")
            
            # 显示模型文件
            files = ["cameras.txt", "images.txt", "points3D.txt"]
            for file in files:
                file_path = model_path / file
                if file_path.exists():
                    logger.info(f"  - {file}: ✓")
                else:
                    logger.warning(f"  - {file}: ✗")
        else:
            logger.warning("重建完成但未找到模型文件")
        
        logger.info("="*60)
        logger.info("COLMAP 增量式重建流程完成!")
        logger.info(f"结果保存在: {output_path}")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"流程执行失败: {e}")
        return False

def main():
    """
    命令行入口函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(
        description='COLMAP 增量式重建流程 (使用pycolmap库)',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 必需参数
    parser.add_argument('--image_folder', required=True,
                       help='输入图像文件夹路径')
    parser.add_argument('--output_folder', required=True,
                       help='输出文件夹路径')
    
    # 可选参数
    parser.add_argument('--camera_model', default='PINHOLE',
                       choices=['PINHOLE', 'RADIAL', 'OPENCV', 'OPENCV_FISHEYE', 'FULL_OPENCV'],
                       help='相机模型')
    parser.add_argument('--num_threads', type=int, default=8,
                       help='线程数')
    parser.add_argument('--max_ratio', type=float, default=0.8,
                       help='SIFT匹配最大比值')
    parser.add_argument('--max_distance', type=float, default=0.7,
                       help='SIFT匹配最大距离')
    parser.add_argument('--init_min_num_inliers', type=int, default=100,
                       help='初始最小内点数')
    parser.add_argument('--min_num_matches', type=int, default=15,
                       help='最小匹配数')
    
    args = parser.parse_args()
    
    success = colmap_pipeline(
        image_folder=args.image_folder,
        output_folder=args.output_folder,
        camera_model=args.camera_model,
        num_threads=args.num_threads,
        max_ratio=args.max_ratio,
        max_distance=args.max_distance,
        init_min_num_inliers=args.init_min_num_inliers,
        min_num_matches=args.min_num_matches
    )
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()