{"BUILD_DIR": "/Users/<USER>/Documents/PoMVG/build/debug/gui_app/pomvg_gui_app_autogen", "CMAKE_BINARY_DIR": "/Users/<USER>/Documents/PoMVG/build/debug", "CMAKE_CURRENT_BINARY_DIR": "/Users/<USER>/Documents/PoMVG/build/debug/gui_app", "CMAKE_CURRENT_SOURCE_DIR": "/Users/<USER>/Documents/PoMVG/src/gui_app", "CMAKE_SOURCE_DIR": "/Users/<USER>/Documents/PoMVG/src", "CROSS_CONFIG": false, "GENERATOR": "Ninja", "INCLUDE_DIR": "/Users/<USER>/Documents/PoMVG/build/debug/gui_app/pomvg_gui_app_autogen/include", "INPUTS": ["/Users/<USER>/Documents/PoMVG/src/gui_app/qml/main.qml", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/qtquickcontrols2.conf", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/qmldir", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1Content/App.qml", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1Content/Screen01.ui.qml", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1/EventListSimulator.qml", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1/EventListModel.qml", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1/qmldir", "/Users/<USER>/Documents/PoMVG/src/gui_app/qml/untitled1/untitled1/Constants.qml"], "LOCK_FILE": "/Users/<USER>/Documents/PoMVG/build/debug/gui_app/CMakeFiles/pomvg_gui_app_autogen.dir/AutoRcc_resources_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "resources"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_resources.cpp", "RCC_EXECUTABLE": "/opt/homebrew/share/qt/libexec/rcc", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "/Users/<USER>/Documents/PoMVG/build/debug/gui_app/CMakeFiles/pomvg_gui_app_autogen.dir/AutoRcc_resources_EWIEGA46WW_Used.txt", "SOURCE": "/Users/<USER>/Documents/PoMVG/src/gui_app/resources.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}