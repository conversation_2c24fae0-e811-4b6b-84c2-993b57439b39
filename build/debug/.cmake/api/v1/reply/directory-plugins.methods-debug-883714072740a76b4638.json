{"backtraceGraph": {"commands": ["install", "_handle_plugin_headers", "add_pomvg_plugin"], "files": ["plugins/cmake/plugin-config.cmake", "plugins/methods/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 1}, {"command": 0, "file": 0, "line": 129, "parent": 2}, {"command": 0, "file": 0, "line": 263, "parent": 1}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 5}, {"command": 0, "file": 0, "line": 129, "parent": 6}, {"command": 0, "file": 0, "line": 263, "parent": 5}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 9}, {"command": 0, "file": 0, "line": 129, "parent": 10}, {"command": 0, "file": 0, "line": 263, "parent": 9}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 13}, {"command": 0, "file": 0, "line": 129, "parent": 14}, {"command": 0, "file": 0, "line": 263, "parent": 13}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 17}, {"command": 0, "file": 0, "line": 129, "parent": 18}, {"command": 0, "file": 0, "line": 263, "parent": 17}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 21}, {"command": 0, "file": 0, "line": 129, "parent": 22}, {"command": 0, "file": 0, "line": 263, "parent": 21}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 25}, {"command": 0, "file": 0, "line": 129, "parent": 26}, {"command": 0, "file": 0, "line": 263, "parent": 25}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 29}, {"command": 0, "file": 0, "line": 129, "parent": 30}, {"command": 0, "file": 0, "line": 263, "parent": 29}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 33}, {"command": 0, "file": 0, "line": 129, "parent": 34}, {"command": 0, "file": 0, "line": 263, "parent": 33}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 37}, {"command": 0, "file": 0, "line": 129, "parent": 38}, {"command": 0, "file": 0, "line": 263, "parent": 37}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 41}, {"command": 0, "file": 0, "line": 129, "parent": 42}, {"command": 0, "file": 0, "line": 263, "parent": 41}, {"command": 2, "file": 1, "line": 195, "parent": 0}, {"command": 1, "file": 0, "line": 260, "parent": 45}, {"command": 0, "file": 0, "line": 129, "parent": 46}, {"command": 0, "file": 0, "line": 263, "parent": 45}, {"command": 0, "file": 1, "line": 356, "parent": 0}]}, "installers": [{"backtrace": 3, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_img2features_plugin.dylib"], "targetId": "method_img2features_plugin::@cc456a8fe8dcffc3a94f", "targetIndex": 5, "type": "target"}, {"backtrace": 7, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 8, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_img2matches_plugin.dylib"], "targetId": "method_img2matches_plugin::@cc456a8fe8dcffc3a94f", "targetIndex": 6, "type": "target"}, {"backtrace": 11, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 12, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_calibrator_plugin.dylib"], "targetId": "method_calibrator_plugin::@cc456a8fe8dcffc3a94f", "targetIndex": 4, "type": "target"}, {"backtrace": 15, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libopengv_model_estimator.dylib"], "targetId": "opengv_model_estimator::@cc456a8fe8dcffc3a94f", "targetIndex": 11, "type": "target"}, {"backtrace": 19, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libposelib_model_estimator.dylib"], "targetId": "poselib_model_estimator::@cc456a8fe8dcffc3a94f", "targetIndex": 17, "type": "target"}, {"backtrace": 23, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 24, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libtwo_view_estimator.dylib"], "targetId": "two_view_estimator::@cc456a8fe8dcffc3a94f", "targetIndex": 19, "type": "target"}, {"backtrace": 27, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 28, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_rotation_averaging.dylib"], "targetId": "method_rotation_averaging::@cc456a8fe8dcffc3a94f", "targetIndex": 8, "type": "target"}, {"backtrace": 31, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libopenmvg_preprocess.dylib"], "targetId": "openmvg_preprocess::@cc456a8fe8dcffc3a94f", "targetIndex": 12, "type": "target"}, {"backtrace": 35, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 36, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_rotation_averaging_Chatterjee.dylib"], "targetId": "method_rotation_averaging_Chatterjee::@cc456a8fe8dcffc3a94f", "targetIndex": 9, "type": "target"}, {"backtrace": 39, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 40, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libmethod_matches_visualizer.dylib"], "targetId": "method_matches_visualizer::@cc456a8fe8dcffc3a94f", "targetIndex": 7, "type": "target"}, {"backtrace": 43, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 44, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libopencv_two_view_estimator.dylib"], "targetId": "opencv_two_view_estimator::@cc456a8fe8dcffc3a94f", "targetIndex": 10, "type": "target"}, {"backtrace": 47, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/ChessboardPatternDetector.hpp", "plugins/methods/ChessboardPatternDetectorCQ.hpp", "plugins/methods/CirclesPatternDetector.hpp", "plugins/methods/barath_two_view_estimator.hpp", "plugins/methods/method_calibrator_plugin.hpp", "plugins/methods/method_img2features_plugin.hpp", "plugins/methods/method_img2matches_plugin.hpp", "plugins/methods/method_matches_visualizer.hpp", "plugins/methods/method_rotation_averaging.hpp", "plugins/methods/method_rotation_averaging_Chatterjee.hpp", "plugins/methods/opencv_two_view_estimator.hpp", "plugins/methods/opengv_model_estimator.hpp", "plugins/methods/openmvg_preprocess.hpp", "plugins/methods/poselib_model_estimator.hpp", "plugins/methods/two_view_estimator.hpp"], "type": "file"}, {"backtrace": 48, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libbarath_two_view_estimator.dylib"], "targetId": "barath_two_view_estimator::@cc456a8fe8dcffc3a94f", "targetIndex": 0, "type": "target"}, {"backtrace": 49, "component": "Unspecified", "destination": "/usr/local/plugins/methods/configs", "paths": ["plugins/methods/colmap_preprocess.ini", "plugins/methods/configs/barath_two_view_estimator.ini", "plugins/methods/configs/method_calibrator.ini", "plugins/methods/configs/method_img2features.ini", "plugins/methods/configs/method_img2matches.ini", "plugins/methods/configs/method_matches_visualizer.ini", "plugins/methods/configs/method_rotation_averaging.ini", "plugins/methods/configs/opencv_two_view_estimator.ini", "plugins/methods/configs/opengv_model_estimator.ini", "plugins/methods/configs/openmvg_img2matches.ini", "plugins/methods/configs/openmvg_preprocess.ini", "plugins/methods/configs/poselib_model_estimator.ini", "plugins/methods/configs/two_view_estimator.ini", "plugins/methods/glomap_preprocess.ini"], "type": "file"}], "paths": {"build": "plugins/methods", "source": "plugins/methods"}}