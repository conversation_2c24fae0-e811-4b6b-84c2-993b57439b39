{"kind": "toolchains", "toolchains": [{"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"], "linkDirectories": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift"], "linkFrameworkDirectories": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks"], "linkLibraries": ["c++"]}, "path": "/usr/bin/clang++", "version": "16.0.0.16000026"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "m", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}