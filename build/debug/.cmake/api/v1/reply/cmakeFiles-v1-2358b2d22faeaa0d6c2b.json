{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "/Users/<USER>/Documents/PoMVG/build/debug/CMakeFiles/3.31.5/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "/Users/<USER>/Documents/PoMVG/build/debug/CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"path": "version.hpp.in"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/cmake/po_core/po_core-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/cmake/po_core/po_core-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfig.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/BoostDetectToolset-1.87.0.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-shared.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-static.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/BoostDetectToolset-1.87.0.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-shared.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-static.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/BoostDetectToolset-1.87.0.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-shared.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-static.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindProtobuf.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindSuiteSparse.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindBLAS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindLAPACK.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindBLAS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBConfig.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-modules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/absl/abslTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/cmake/po_core/po_core-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindGTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/GoogleTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestTargets-release.cmake"}, {"path": "cmake/FindOpenGV.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVModules-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindSuiteSparse.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindBLAS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindLAPACK.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindBLAS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBConfig.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/TBB/TBBTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-modules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"path": "dependencies/lemon/lemon/config.h"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "common/CMakeLists.txt"}, {"path": "common/converter/CMakeLists.txt"}, {"path": "cmake/FindOpenGV.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/share/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/share/cmake/nlohmann_json/nlohmann_jsonTargets.cmake"}, {"path": "common/image_viewer/CMakeLists.txt"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "plugins/CMakeLists.txt"}, {"path": "plugins/cmake/plugin-config.cmake"}, {"path": "plugins/data/CMakeLists.txt"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "plugins/methods/CMakeLists.txt"}, {"path": "plugins/cmake/plugin-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindOpenGL.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glfw3/glfw3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glfw3/glfw3Config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glfw3/glfw3Targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glfw3/glfw3Targets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "plugins/methods/colmap_preprocess.ini"}, {"path": "plugins/methods/configs/barath_two_view_estimator.ini"}, {"path": "plugins/methods/configs/method_calibrator.ini"}, {"path": "plugins/methods/configs/method_img2features.ini"}, {"path": "plugins/methods/configs/method_img2matches.ini"}, {"path": "plugins/methods/configs/method_matches_visualizer.ini"}, {"path": "plugins/methods/configs/method_rotation_averaging.ini"}, {"path": "plugins/methods/configs/opencv_two_view_estimator.ini"}, {"path": "plugins/methods/configs/opengv_model_estimator.ini"}, {"path": "plugins/methods/configs/openmvg_img2matches.ini"}, {"path": "plugins/methods/configs/openmvg_preprocess.ini"}, {"path": "plugins/methods/configs/poselib_model_estimator.ini"}, {"path": "plugins/methods/configs/two_view_estimator.ini"}, {"path": "plugins/methods/glomap_preprocess.ini"}, {"path": "plugins/methods/GLOMAP/CMakeLists.txt"}, {"path": "plugins/cmake/plugin-config.cmake"}, {"path": "plugins/methods/GLOMAP/colmap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/example_load_colmap.py"}, {"path": "plugins/methods/GLOMAP/export_camera_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_model.py"}, {"path": "plugins/methods/GLOMAP/export_matches_from_db.py"}, {"path": "plugins/methods/GLOMAP/glomap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/load_colmap_db.py"}, {"path": "plugins/methods/GLOMAP/colmap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/example_load_colmap.py"}, {"path": "plugins/methods/GLOMAP/export_camera_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_model.py"}, {"path": "plugins/methods/GLOMAP/export_matches_from_db.py"}, {"path": "plugins/methods/GLOMAP/glomap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/load_colmap_db.py"}, {"path": "plugins/methods/GLOMAP/colmap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/example_load_colmap.py"}, {"path": "plugins/methods/GLOMAP/export_camera_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_model.py"}, {"path": "plugins/methods/GLOMAP/export_matches_from_db.py"}, {"path": "plugins/methods/GLOMAP/glomap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/load_colmap_db.py"}, {"path": "plugins/methods/GLOMAP/colmap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/example_load_colmap.py"}, {"path": "plugins/methods/GLOMAP/export_camera_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_db.py"}, {"path": "plugins/methods/GLOMAP/export_global_poses_from_model.py"}, {"path": "plugins/methods/GLOMAP/export_matches_from_db.py"}, {"path": "plugins/methods/GLOMAP/glomap_pipeline.py"}, {"path": "plugins/methods/GLOMAP/load_colmap_db.py"}, {"path": "tests/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindGTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/GoogleTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/miniconda3/lib/cmake/GTest/GTestTargets.cmake"}, {"path": "cmake/FindOpenGV.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/PoMVG/build/debug", "source": "/Users/<USER>/Documents/PoMVG/src"}, "version": {"major": 1, "minor": 1}}