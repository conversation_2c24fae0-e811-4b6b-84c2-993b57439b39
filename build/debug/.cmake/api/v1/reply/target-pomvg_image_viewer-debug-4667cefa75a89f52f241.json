{"artifacts": [{"path": "output/common/libpomvg_image_viewer.dylib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["common/image_viewer/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 31, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=apple-m1 -g -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC"}, {"backtrace": 3, "fragment": "-fPIC"}], "defines": [{"define": "pomvg_image_viewer_EXPORTS"}], "includes": [{"backtrace": 4, "path": "/Users/<USER>/Documents/PoMVG/src/include"}, {"backtrace": 4, "path": "/Users/<USER>/Documents/PoMVG/src/common/image_viewer"}, {"backtrace": 4, "isSystem": true, "path": "/opt/homebrew/Cellar/opencv/4.11.0/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0]}], "id": "pomvg_image_viewer::@69e5550d01a123ae2921", "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_gapi.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_stitching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_alphamat.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_aruco.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_bgsegm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_bioinspired.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_ccalib.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_dnn_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_dnn_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_dpm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_face.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_freetype.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_fuzzy.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_hfs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_img_hash.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_intensity_transform.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_line_descriptor.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_mcc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_quality.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_rapid.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_reg.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_rgbd.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_saliency.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_sfm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_signal.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_stereo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_structured_light.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_surface_matching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_tracking.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_videostab.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_viz.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_wechat_qrcode.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_xfeatures2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_xobjdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_xphoto.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_shape.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_highgui.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_datasets.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_plot.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_text.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_ml.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_phase_unwrapping.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_optflow.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_ximgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_video.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_videoio.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_imgcodecs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_calib3d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_dnn.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_features2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_flann.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_photo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_imgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libopencv_core.4.11.0.dylib", "role": "libraries"}], "language": "CXX"}, "name": "pomvg_image_viewer", "nameOnDisk": "libpomvg_image_viewer.dylib", "paths": {"build": "common/image_viewer", "source": "common/image_viewer"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "common/image_viewer/image_viewer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "common/image_viewer/image_viewer.hpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}