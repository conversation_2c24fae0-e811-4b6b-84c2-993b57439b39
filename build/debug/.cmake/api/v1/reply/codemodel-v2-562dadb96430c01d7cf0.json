{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 4, 8], "hasInstallRule": true, "jsonFile": "directory-.-debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [16]}, {"build": "common", "childIndexes": [2, 3], "hasInstallRule": true, "jsonFile": "directory-common-debug-d277230d03bda981b5d1.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 1, "source": "common", "targetIndexes": [13]}, {"build": "common/converter", "jsonFile": "directory-common.converter-debug-a7da69284f1afbbb6f3a.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 1, "projectIndex": 1, "source": "common/converter", "targetIndexes": [14]}, {"build": "common/image_viewer", "jsonFile": "directory-common.image_viewer-debug-788c6a3bcf4e1479eb20.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 1, "projectIndex": 1, "source": "common/image_viewer", "targetIndexes": [15]}, {"build": "plugins", "childIndexes": [5, 6], "hasInstallRule": true, "jsonFile": "directory-plugins-debug-55275e3f2f02dcd473ff.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 0, "source": "plugins"}, {"build": "plugins/data", "hasInstallRule": true, "jsonFile": "directory-plugins.data-debug-03763dffd4a29bab8b83.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 4, "projectIndex": 2, "source": "plugins/data", "targetIndexes": [2]}, {"build": "plugins/methods", "childIndexes": [7], "hasInstallRule": true, "jsonFile": "directory-plugins.methods-debug-883714072740a76b4638.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 4, "projectIndex": 3, "source": "plugins/methods", "targetIndexes": [0, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 19]}, {"build": "plugins/methods/GLOMAP", "hasInstallRule": true, "jsonFile": "directory-plugins.methods.GLOMAP-debug-d8bbf3600b1af54f600c.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 6, "projectIndex": 3, "source": "plugins/methods/GLOMAP", "targetIndexes": [1, 3]}, {"build": "tests", "jsonFile": "directory-tests-debug-b01f6abb0ab61427ef3b.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [18]}], "name": "debug", "projects": [{"childIndexes": [1, 2, 3], "directoryIndexes": [0, 4, 8], "name": "PoSDK", "targetIndexes": [16, 18]}, {"directoryIndexes": [1, 2, 3], "name": "pomvg_common", "parentIndex": 0, "targetIndexes": [13, 14, 15]}, {"directoryIndexes": [5], "name": "data_plugin", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [6, 7], "name": "method_plugins", "parentIndex": 0, "targetIndexes": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 19]}], "targets": [{"directoryIndex": 6, "id": "barath_two_view_estimator::@cc456a8fe8dcffc3a94f", "jsonFile": "target-barath_two_view_estimator-debug-cd1dec8d5444333e84e4.json", "name": "barath_two_view_estimator", "projectIndex": 3}, {"directoryIndex": 7, "id": "colmap_preprocess::@0fdc463f5dc386ea47fb", "jsonFile": "target-colmap_preprocess-debug-86909d35733144eed996.json", "name": "colmap_preprocess", "projectIndex": 3}, {"directoryIndex": 5, "id": "data_features_plugin::@3f05962956fc2025963e", "jsonFile": "target-data_features_plugin-debug-cb9ca02fd5be689c5cba.json", "name": "data_features_plugin", "projectIndex": 2}, {"directoryIndex": 7, "id": "glomap_preprocess::@0fdc463f5dc386ea47fb", "jsonFile": "target-glomap_preprocess-debug-6b31aa7178f53a3f1e1a.json", "name": "glomap_preprocess", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_calibrator_plugin::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_calibrator_plugin-debug-2db6774cd5a40cb1aa92.json", "name": "method_calibrator_plugin", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_img2features_plugin::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_img2features_plugin-debug-b047f28398da62c5afa1.json", "name": "method_img2features_plugin", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_img2matches_plugin::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_img2matches_plugin-debug-1429ffa8e74cfdf4e9b2.json", "name": "method_img2matches_plugin", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_matches_visualizer::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_matches_visualizer-debug-f6d515e50bede6379c01.json", "name": "method_matches_visualizer", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_rotation_averaging::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_rotation_averaging-debug-be5e3007a1b4e98570cb.json", "name": "method_rotation_averaging", "projectIndex": 3}, {"directoryIndex": 6, "id": "method_rotation_averaging_Chatterjee::@cc456a8fe8dcffc3a94f", "jsonFile": "target-method_rotation_averaging_Cha<PERSON><PERSON>-debug-cfe1962590a559a83903.json", "name": "method_rotation_averaging_<PERSON><PERSON><PERSON>", "projectIndex": 3}, {"directoryIndex": 6, "id": "opencv_two_view_estimator::@cc456a8fe8dcffc3a94f", "jsonFile": "target-opencv_two_view_estimator-debug-c85b19e14d23771162cd.json", "name": "opencv_two_view_estimator", "projectIndex": 3}, {"directoryIndex": 6, "id": "opengv_model_estimator::@cc456a8fe8dcffc3a94f", "jsonFile": "target-opengv_model_estimator-debug-01cd52f9348ddd9405fe.json", "name": "opengv_model_estimator", "projectIndex": 3}, {"directoryIndex": 6, "id": "openmvg_preprocess::@cc456a8fe8dcffc3a94f", "jsonFile": "target-openmvg_preprocess-debug-d0520aa13ad02d93d05d.json", "name": "openmvg_preprocess", "projectIndex": 3}, {"directoryIndex": 1, "id": "pomvg_common::@bfc844598bd03e848b3c", "jsonFile": "target-pomvg_common-debug-d70a8cd54ddd8519673d.json", "name": "pomvg_common", "projectIndex": 1}, {"directoryIndex": 2, "id": "pomvg_converter::@233c8346e5fddfdb784e", "jsonFile": "target-pomvg_converter-debug-2e1a5638d92e270e9d29.json", "name": "pomvg_converter", "projectIndex": 1}, {"directoryIndex": 3, "id": "pomvg_image_viewer::@69e5550d01a123ae2921", "jsonFile": "target-pomvg_image_viewer-debug-4667cefa75a89f52f241.json", "name": "pomvg_image_viewer", "projectIndex": 1}, {"directoryIndex": 0, "id": "posdk_agg::@6890427a1f51a3e7e1df", "jsonFile": "target-posdk_agg-debug-3bc39c9cdba30efcdc54.json", "name": "posdk_agg", "projectIndex": 0}, {"directoryIndex": 6, "id": "poselib_model_estimator::@cc456a8fe8dcffc3a94f", "jsonFile": "target-poselib_model_estimator-debug-18eb22b3fb93f0507f08.json", "name": "poselib_model_estimator", "projectIndex": 3}, {"directoryIndex": 8, "id": "test_all::@a44f0ac069e85531cdee", "jsonFile": "target-test_all-debug-a76f35f0b71d06a42493.json", "name": "test_all", "projectIndex": 0}, {"directoryIndex": 6, "id": "two_view_estimator::@cc456a8fe8dcffc3a94f", "jsonFile": "target-two_view_estimator-debug-bd13a564e6ad3bf80498.json", "name": "two_view_estimator", "projectIndex": 3}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/PoMVG/build/debug", "source": "/Users/<USER>/Documents/PoMVG/src"}, "version": {"major": 2, "minor": 7}}