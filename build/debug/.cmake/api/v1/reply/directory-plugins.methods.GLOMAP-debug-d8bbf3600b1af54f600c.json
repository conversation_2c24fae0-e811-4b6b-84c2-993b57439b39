{"backtraceGraph": {"commands": ["install", "_handle_plugin_headers", "add_posdk_plugin", "_handle_plugin_python_files"], "files": ["plugins/cmake/plugin-config.cmake", "plugins/methods/GLOMAP/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 19, "parent": 0}, {"command": 1, "file": 0, "line": 77, "parent": 1}, {"command": 0, "file": 0, "line": 129, "parent": 2}, {"command": 3, "file": 0, "line": 83, "parent": 1}, {"command": 0, "file": 0, "line": 195, "parent": 4}, {"command": 0, "file": 0, "line": 198, "parent": 4}, {"command": 0, "file": 0, "line": 86, "parent": 1}, {"command": 2, "file": 1, "line": 35, "parent": 0}, {"command": 1, "file": 0, "line": 77, "parent": 8}, {"command": 0, "file": 0, "line": 129, "parent": 9}, {"command": 3, "file": 0, "line": 83, "parent": 8}, {"command": 0, "file": 0, "line": 195, "parent": 11}, {"command": 0, "file": 0, "line": 198, "parent": 11}, {"command": 0, "file": 0, "line": 86, "parent": 8}, {"command": 0, "file": 1, "line": 100, "parent": 0}]}, "installers": [{"backtrace": 3, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/GLOMAP/colmap_preprocess.hpp", "plugins/methods/GLOMAP/glomap_preprocess.hpp"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "python/colmap_preprocess", "paths": ["plugins/methods/GLOMAP/colmap_pipeline.py", "plugins/methods/GLOMAP/example_load_colmap.py", "plugins/methods/GLOMAP/export_camera_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_model.py", "plugins/methods/GLOMAP/export_matches_from_db.py", "plugins/methods/GLOMAP/glomap_pipeline.py", "plugins/methods/GLOMAP/load_colmap_db.py"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "plugins/methods", "paths": ["plugins/methods/GLOMAP/colmap_pipeline.py", "plugins/methods/GLOMAP/example_load_colmap.py", "plugins/methods/GLOMAP/export_camera_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_model.py", "plugins/methods/GLOMAP/export_matches_from_db.py", "plugins/methods/GLOMAP/glomap_pipeline.py", "plugins/methods/GLOMAP/load_colmap_db.py"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libcolmap_preprocess.dylib"], "targetId": "colmap_preprocess::@0fdc463f5dc386ea47fb", "targetIndex": 1, "type": "target"}, {"backtrace": 10, "component": "Unspecified", "destination": "include/plugins/methods", "paths": ["plugins/methods/GLOMAP/colmap_preprocess.hpp", "plugins/methods/GLOMAP/glomap_preprocess.hpp"], "type": "file"}, {"backtrace": 12, "component": "Unspecified", "destination": "python/glomap_preprocess", "paths": ["plugins/methods/GLOMAP/colmap_pipeline.py", "plugins/methods/GLOMAP/example_load_colmap.py", "plugins/methods/GLOMAP/export_camera_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_model.py", "plugins/methods/GLOMAP/export_matches_from_db.py", "plugins/methods/GLOMAP/glomap_pipeline.py", "plugins/methods/GLOMAP/load_colmap_db.py"], "type": "file"}, {"backtrace": 13, "component": "Unspecified", "destination": "plugins/methods", "paths": ["plugins/methods/GLOMAP/colmap_pipeline.py", "plugins/methods/GLOMAP/example_load_colmap.py", "plugins/methods/GLOMAP/export_camera_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_db.py", "plugins/methods/GLOMAP/export_global_poses_from_model.py", "plugins/methods/GLOMAP/export_matches_from_db.py", "plugins/methods/GLOMAP/glomap_pipeline.py", "plugins/methods/GLOMAP/load_colmap_db.py"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "plugins/methods", "paths": ["output/plugins/methods/libglomap_preprocess.dylib"], "targetId": "glomap_preprocess::@0fdc463f5dc386ea47fb", "targetIndex": 3, "type": "target"}, {"backtrace": 15, "component": "Unspecified", "destination": "docs/plugins/GLOMAP", "paths": ["/Users/<USER>/Documents/PoMVG/build/debug/plugins/methods/GLOMAP/plugin_info.txt"], "type": "file"}], "paths": {"build": "plugins/methods/GLOMAP", "source": "plugins/methods/GLOMAP"}}