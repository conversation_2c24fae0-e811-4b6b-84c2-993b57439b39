{"artifacts": [{"path": "output/plugins/methods/libmethod_img2features_plugin.dylib"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "add_pomvg_plugin", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "set_property", "boost_find_component", "find_dependency", "target_compile_definitions", "target_include_directories"], "files": ["plugins/cmake/plugin-config.cmake", "plugins/methods/CMakeLists.txt", "common/CMakeLists.txt", "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/cmake/po_core/po_core-targets.cmake", "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/cmake/po_core/po_core-config.cmake", "CMakeLists.txt", "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config.cmake", "/opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfig.cmake", "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/absl/abslTargets.cmake", "/opt/homebrew/lib/cmake/absl/abslConfig.cmake", "/opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake", "/opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake", "common/converter/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 195, "parent": 0}, {"command": 0, "file": 0, "line": 228, "parent": 1}, {"command": 2, "file": 0, "line": 263, "parent": 1}, {"command": 3, "file": 0, "line": 240, "parent": 1}, {"file": 2}, {"command": 3, "file": 2, "line": 18, "parent": 5}, {"file": 5}, {"command": 6, "file": 5, "line": 222, "parent": 7}, {"file": 4, "parent": 8}, {"command": 5, "file": 4, "line": 78, "parent": 9}, {"file": 3, "parent": 10}, {"command": 4, "file": 3, "line": 55, "parent": 11}, {"command": 4, "file": 3, "line": 76, "parent": 11}, {"command": 4, "file": 3, "line": 69, "parent": 11}, {"command": 4, "file": 3, "line": 83, "parent": 11}, {"command": 9, "file": 4, "line": 31, "parent": 9}, {"command": 6, "file": 8, "line": 76, "parent": 16}, {"file": 7, "parent": 17}, {"command": 8, "file": 7, "line": 262, "parent": 18}, {"command": 6, "file": 7, "line": 141, "parent": 19}, {"file": 6, "parent": 20}, {"command": 7, "file": 6, "line": 103, "parent": 21}, {"command": 6, "file": 5, "line": 132, "parent": 7}, {"file": 10, "parent": 23}, {"command": 5, "file": 10, "line": 32, "parent": 24}, {"file": 9, "parent": 25}, {"command": 4, "file": 9, "line": 905, "parent": 26}, {"command": 4, "file": 3, "line": 62, "parent": 11}, {"command": 4, "file": 9, "line": 851, "parent": 26}, {"command": 4, "file": 9, "line": 923, "parent": 26}, {"command": 4, "file": 9, "line": 1563, "parent": 26}, {"command": 4, "file": 9, "line": 1031, "parent": 26}, {"command": 4, "file": 9, "line": 797, "parent": 26}, {"command": 4, "file": 9, "line": 1787, "parent": 26}, {"command": 4, "file": 9, "line": 1760, "parent": 26}, {"command": 4, "file": 9, "line": 1121, "parent": 26}, {"command": 4, "file": 9, "line": 1689, "parent": 26}, {"command": 4, "file": 9, "line": 1698, "parent": 26}, {"command": 4, "file": 9, "line": 1715, "parent": 26}, {"command": 4, "file": 9, "line": 1527, "parent": 26}, {"command": 4, "file": 9, "line": 168, "parent": 26}, {"command": 4, "file": 9, "line": 536, "parent": 26}, {"command": 4, "file": 9, "line": 545, "parent": 26}, {"command": 4, "file": 9, "line": 581, "parent": 26}, {"command": 4, "file": 9, "line": 608, "parent": 26}, {"command": 4, "file": 9, "line": 599, "parent": 26}, {"command": 3, "file": 1, "line": 209, "parent": 0}, {"command": 9, "file": 4, "line": 34, "parent": 9}, {"command": 6, "file": 8, "line": 76, "parent": 48}, {"file": 12, "parent": 49}, {"command": 5, "file": 12, "line": 272, "parent": 50}, {"file": 11, "parent": 51}, {"command": 4, "file": 11, "line": 61, "parent": 52}, {"file": 13}, {"command": 3, "file": 13, "line": 89, "parent": 54}, {"command": 10, "file": 1, "line": 343, "parent": 0}, {"command": 11, "file": 0, "line": 231, "parent": 1}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=apple-m1 -g -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC"}], "defines": [{"backtrace": 4, "define": "BOOST_ATOMIC_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 4, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 4, "define": "GFLAGS_IS_A_DLL=0"}, {"backtrace": 4, "define": "GLOG_CUSTOM_PREFIX_SUPPORT"}, {"backtrace": 56, "define": "POMVG_INSTALL_PREFIX=\"/usr/local\""}, {"backtrace": 56, "define": "POMVG_OUTPUT_DIR=\"/Users/<USER>/Documents/PoMVG/build/debug/output\""}, {"backtrace": 56, "define": "PROJECT_SOURCE_DIR=\"/Users/<USER>/Documents/PoMVG/src\""}, {"define": "method_img2features_plugin_EXPORTS"}], "includes": [{"backtrace": 57, "path": "/Users/<USER>/Documents/PoMVG/src/plugins/methods"}, {"backtrace": 57, "path": "/Users/<USER>/Documents/PoMVG/build/debug/output/include"}, {"backtrace": 4, "path": "/Users/<USER>/Documents/PoMVG/src/common"}, {"backtrace": 4, "path": "/Users/<USER>/Documents/PoMVG/src/include"}, {"backtrace": 57, "isSystem": true, "path": "/opt/homebrew/Cellar/opencv/4.11.0/include/opencv4"}, {"backtrace": 57, "isSystem": true, "path": "/opt/homebrew/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/homebrew/include"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include/proto"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/miniconda3/include"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "pomvg_common::@bfc844598bd03e848b3c"}, {"backtrace": 4, "id": "pomvg_converter::@233c8346e5fddfdb784e"}, {"backtrace": 4, "id": "pomvg_image_viewer::@69e5550d01a123ae2921"}], "id": "method_img2features_plugin::@cc456a8fe8dcffc3a94f", "install": {"destinations": [{"backtrace": 3, "path": "plugins/methods"}, {"backtrace": 3, "path": "plugins/methods"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/Users/<USER>/Documents/PoMVG/build/debug/output/common -Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 4, "fragment": "output/common/libpomvg_common.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "output/common/libpomvg_converter.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/libpo_core.dylib", "role": "libraries"}, {"backtrace": 6, "fragment": "output/common/libpomvg_image_viewer.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/libpomvg_factory.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/libpomvg_file_io.dylib", "role": "libraries"}, {"backtrace": 13, "fragment": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/libpomvg_internal.dylib", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/homebrew/lib/libceres.2.2.0.dylib", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/homebrew/lib/libboost_filesystem.dylib", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/homebrew/lib/libboost_system.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libboost_atomic.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib/libpomvg_proto.dylib", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/homebrew/lib/libabsl_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/homebrew/lib/libabsl_city.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 39, "fragment": "-Wl,-framework,CoreFoundation", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_strings.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/homebrew/lib/libabsl_int128.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_base.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_gapi.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_stitching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_alphamat.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_aruco.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_bgsegm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_bioinspired.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_ccalib.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_dnn_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_dnn_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_dpm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_face.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_freetype.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_fuzzy.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_hfs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_img_hash.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_intensity_transform.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_line_descriptor.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_mcc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_quality.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_rapid.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_reg.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_rgbd.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_saliency.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_sfm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/homebrew/lib/libglog.0.6.0.dylib", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/homebrew/lib/libgflags.2.2.2.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_signal.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_stereo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_structured_light.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_phase_unwrapping.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_optflow.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_surface_matching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_tracking.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_highgui.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_datasets.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_plot.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_text.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_videostab.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_videoio.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_viz.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_wechat_qrcode.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_xfeatures2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_ml.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_shape.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_ximgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_video.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_xobjdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_imgcodecs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_calib3d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_dnn.4.11.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libprotobuf.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_features2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_flann.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_xphoto.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_photo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_imgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/homebrew/lib/libopencv_core.4.11.0.dylib", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/homebrew/lib/libopengv.dylib", "role": "libraries"}], "language": "CXX"}, "name": "method_img2features_plugin", "nameOnDisk": "libmethod_img2features_plugin.dylib", "paths": {"build": "plugins/methods", "source": "plugins/methods"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "plugins/methods/method_img2features_plugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "plugins/methods/method_img2features_plugin.hpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}