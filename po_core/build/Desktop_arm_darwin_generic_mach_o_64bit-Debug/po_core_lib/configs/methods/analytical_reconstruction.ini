# AnalyticalReconstruction 方法配置文件
# 解析式三角化重建方法参数配置

[analytical_reconstruction]

# ============================================================================
# 基本重建参数
# ============================================================================

# 三角化阈值 (m3_ratio)
# 用于判断点是否可重建的最小视差角阈值
# 值越大，重建的点越可靠，但重建的点数量会减少
# 设置为0.0时不进行视差角检查
# 推荐值：0.0 (普通重建), 0.05-0.1 (鲁棒重建)
m3_ratio = 0.0

# 每个点的最小观测数量 (min_num_observations_per_point)
# 重建3D点所需的最少观测数量
# 至少需要2个观测才能进行三角化
# 增加此值可提高重建质量但会减少重建点数量
# 推荐值：2 (普通重建), 3-4 (高质量重建)
min_num_observations_per_point = 2

# 重建模式 (reconstruction_mode)
# 可选值：
#   - normal: 普通重建模式，快速重建所有可能的点
#   - robust_reconstruction: 鲁棒重建模式，使用视差角检查过滤不可靠的点
#   - remove_outliers: 异常值检测模式，仅识别并输出异常值轨迹ID
reconstruction_mode = normal

# 日志等级 (log_level)
# 0: 无日志输出
# 1: 普通日志 (默认)
# 2: 详细日志 (包含调试信息)
log_level = 1

# ============================================================================
# 输入文件路径配置 (可选，通常通过数据包提供)
# ============================================================================

# 轨迹文件路径 (tracks_file)
# 如果为空，将从输入数据包中获取
tracks_file = 

# 全局旋转文件路径 (global_rotation_file)
# 如果为空，将从输入数据包中获取
global_rotation_file = 

# 全局平移文件路径 (global_translation_file)
# 如果为空，将从输入数据包中获取
global_translation_file = 

# ============================================================================
# 输出文件路径配置
# ============================================================================

# 重建点云输出文件路径 (points_output_file)
# 支持格式：.txt, .ply (自动生成.ids文件), .pb (回退到txt格式)
# 如果为空，不保存到文件
points_output_file = reconstructed_points.txt

# 异常值轨迹ID输出文件路径 (outliers_output_file)
# 仅在remove_outliers模式下使用
# 保存检测到的异常值轨迹ID列表
outliers_output_file = outlier_ids.txt


# ============================================================================
# 配置文件说明
# ============================================================================

# 方法描述
ProfileCommit = 解析式三角化重建方法：基于已知全局位姿进行快速3D点云重建。支持普通重建、异常值检测和鲁棒重建三种模式。输入要求：轨迹数据(data_tracks)、全局位姿(data_global_poses)、相机模型(data_camera_models，可选)。

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估


# 典型使用场景配置示例：
# 
# 1. 快速重建 (最大点数量):
#    m3_ratio = 0.0
#    min_num_observations_per_point = 2
#    reconstruction_mode = normal
#
# 2. 高质量重建 (平衡质量和数量):
#    m3_ratio = 0.05
#    min_num_observations_per_point = 3
#    reconstruction_mode = robust_reconstruction
#
# 3. 异常值检测:
#    m3_ratio = 0.1
#    reconstruction_mode = remove_outliers
#
# 4. 调试模式:
#    log_level = 2 