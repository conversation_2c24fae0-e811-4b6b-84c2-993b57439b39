[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpo_core_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/po_core.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/po_core.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/Rt_check.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/Rt_check.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_pose.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_pose.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_residuals.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_residuals.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_features.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_features.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_poses.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_poses.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_rotations.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_rotations.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_translations.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_translations.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_images.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_images.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_matches.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_matches.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_points_3d.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_points_3d.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_rotations.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_rotations.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_tracks.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_tracks.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/evaluator.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/evaluator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/executable_path.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/executable_path.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/LiGT/LiGT.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/LiGT/LiGT.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/experiment_helpers.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/experiment_helpers.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/random_generators.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/random_generators.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/time_measurement.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/time_measurement.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/CeresOptimizer.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/CeresOptimizer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/analytical_reconstruction.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/analytical_reconstruction.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiGT.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiGT.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRP.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRP.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRPFast.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRPFast.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_PA.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_PA.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_global_outlier_removal.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_global_outlier_removal.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_matches2tracks.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_matches2tracks.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_povgSixPoint.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_povgSixPoint.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_relative_cost.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_relative_cost.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_robustLiRP.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_robustLiRP.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/opengv_simulator.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/opengv_simulator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/visual_simulator.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/visual_simulator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/Rt_check.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/Rt_check.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/compute_coefficients.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/compute_coefficients.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/costor_info.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/costor_info.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_pose.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_pose.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_residuals.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_residuals.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_camera_models.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_camera_models.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_features.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_features.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_poses.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_poses.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_rotations.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_rotations.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_translations.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_translations.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_images.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_images.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_matches.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_matches.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_points_3d.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_points_3d.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_poses.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_poses.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_rotations.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_rotations.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_tracks.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_tracks.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/evaluator.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/evaluator.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/executable_path.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/executable_path.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_lookup.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_lookup.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_template_impl.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_template_impl.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_utils.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory_utils.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/gnc_irls.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/gnc_irls.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/inifile.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/inifile.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset_profiler.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset_profiler.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_robust_estimator.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_robust_estimator.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/LiGT/LiGT.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/LiGT/LiGT.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/experiment_helpers.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/experiment_helpers.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/random_generators.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/random_generators.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/time_measurement.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/time_measurement.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/PA/pa_reprojection_error_calibrated.h"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/PA/pa_reprojection_error_calibrated.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/CeresOptimizer.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/CeresOptimizer.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/TwoViewOptimizerBase.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/TwoViewOptimizerBase.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/analytical_reconstruction.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/analytical_reconstruction.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiGT.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiGT.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRP.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRP.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRPFast.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRPFast.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_PA.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_PA.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_global_outlier_removal.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_global_outlier_removal.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_matches2tracks.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_matches2tracks.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_povgSixPoint.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_povgSixPoint.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_relative_cost.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_relative_cost.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_robustLiRP.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_robustLiRP.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/opengv_simulator.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/opengv_simulator.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/visual_simulator.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/visual_simulator.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pb_dataio.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pb_dataio.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin_register.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin_register.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/ransac_estimator.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/ransac_estimator.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DBOOST_ATOMIC_DYN_LINK", "-DBOOST_ATOMIC_NO_LIB", "-DBOOST_FILESYSTEM_DYN_LINK", "-DBOOST_FILESYSTEM_NO_LIB", "-DBOOST_SYSTEM_DYN_LINK", "-DBOOST_SYSTEM_NO_LIB", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\"", "-DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\"", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_factory_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory", "-I/Users/<USER>/Documents/PoMVG/po_core/src/relative_process", "-I/Users/<USER>/Documents/spectra-master", "-I/Users/<USER>/Documents/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/union_find.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/union_find.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_file_io_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/file_io.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/file_io.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_file_io_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/g2o_io.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/g2o_io.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_file_io_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/file_io.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/file_io.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_file_io_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/g2o_io.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/g2o_io.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_internal_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DUSE_OPENMP", "-D_DEBUG", "-Dpomvg_internal_EXPORTS", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/types.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Xpreprocessor", "-fopenmp", "-g", "-std=gnu++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIE", "-fcolor-diagnostics", "-Wall", "-Wextra", "-Wpedantic", "-Xpreprocessor", "-fopenmp", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DAPPLE_M1", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DPROJECT_SOURCE_DIR=\"/Users/<USER>/Documents/PoMVG/po_core\"", "-DUSE_OPENMP", "-D_DEBUG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include", "-I/Users/<USER>/Documents/PoMVG/po_core/src", "-I/Users/<USER>/Documents/PoMVG/po_core/src/internal", "-I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto", "-isystem", "/opt/homebrew/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/opt/libomp/include", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/tests/test_rtcheck_types_performance.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/tests/test_rtcheck_types_performance.cpp"}]