# ninja log v6
15	62	1752040966252387388	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/relative_poses.pb.h	892523dafd989c59
2	24	1752040966041575777	clean	4cb08d18fdd1150f
14	60	1752040966250794037	po_core_lib/include/proto/matches.pb.cc	b9f88238ce85ffa8
11176	11199	1752040977368618738	CMakeFiles/po_core.dir/po_core.cpp.o	56367e30e1e7ec3f
2039	4983	1752040968231522740	src/internal/CMakeFiles/pomvg_factory.dir/data/data_relative_rotations.cpp.o	c0620f94e60ad197
64	2039	1752040966256841145	src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/relative_poses.pb.cc.o	703e5b9a69a79cc1
14	60	1752040966250794037	po_core_lib/include/proto/matches.pb.h	b9f88238ce85ffa8
14	60	1752040966251657088	po_core_lib/include/proto/features.pb.cc	57e0d247de3f6d1d
12	6152	1752079774898749154	src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_pose.cpp.o	ef706dff05278655
14	60	1752040966250794037	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/matches.pb.h	b9f88238ce85ffa8
4308	6603	1752040970500205318	src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/experiment_helpers.cpp.o	8e16ffd1f91bc0dd
14	60	1752040966251657088	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/features.pb.cc	57e0d247de3f6d1d
72	4791	1752040966264634731	src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_rotations.cpp.o	336e63c5f58562ee
64	2058	1752040966256707935	src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/camera_model.pb.cc.o	743d95adb4f98b3c
18	5701	1752144400066859444	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_relative_cost.cpp.o	ed769f4447e050
13	6873	1752079774899360184	src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/CeresOptimizer.cpp.o	939f9e478cb8b565
11059	14078	1752040977251354740	src/internal/CMakeFiles/pomvg_factory.dir/pomvg_plugin.cpp.o	41e3439cddb26bf
15	62	1752040966252387388	po_core_lib/include/proto/relative_poses.pb.cc	892523dafd989c59
13848	13893	1752144413896288792	po_core_lib/lib/libpo_core.0.11.4.0.dylib	ef24f2221df53709
3444	3915	1752040969636807529	src/internal/CMakeFiles/pomvg_factory.dir/executable_path.cpp.o	962b43dee7867f05
15	62	1752040966252387388	po_core_lib/include/proto/relative_poses.pb.h	892523dafd989c59
14	60	1752040966250794037	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/matches.pb.cc	b9f88238ce85ffa8
4287	4308	1752040970479817344	po_core_lib/lib/libpomvg_internal.0.dylib	e1feb49a143923fa
14	61	1752040966252092051	po_core_lib/include/proto/tracks.pb.h	af4a04e06708692b
1919	6253	1752040968111486087	src/internal/CMakeFiles/pomvg_factory.dir/data/data_matches.cpp.o	db1164fc6c299a97
15	62	1752040966252387388	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/relative_poses.pb.cc	892523dafd989c59
15	61	1752040966252163677	po_core_lib/include/proto/camera_model.pb.cc	69929b62022e8326
15	10830	1752144400064034380	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRPFast.cpp.o	f0645ff699b9ab5b
62	1962	1752040966254982166	src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/tracks.pb.cc.o	f0dee077c4e6dee9
15	61	1752040966252163677	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/camera_model.pb.cc	69929b62022e8326
15	7070	1752079774901496808	tests/CMakeFiles/test_all.dir/test_nullspace_performance.cpp.o	c26bcafa4125d43f
14	61	1752040966252092051	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/tracks.pb.h	af4a04e06708692b
1962	6435	1752040968154636727	src/internal/CMakeFiles/pomvg_factory.dir/data/data_points_3d.cpp.o	8b46580662429f31
12	5300	1752079774899140271	src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_residuals.cpp.o	6e586e8001a65e1c
15	4934	1752144400063836053	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiGT.cpp.o	9d5f799a5d75abe3
5530	11774	1751884104323206104	src/internal/CMakeFiles/pomvg_factory.dir/methods/alytical_reconstruction.cpp.o	8e0bc6c24c16dc6c
14	61	1752040966252092051	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/tracks.pb.cc	af4a04e06708692b
2059	2118	1752040968251133580	po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib	f4c75243dbde8323
15	61	1752040966252163677	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/camera_model.pb.h	69929b62022e8326
15	2734	1752144400063232700	src/internal/CMakeFiles/pomvg_factory.dir/evaluator.cpp.o	8a06b9e09153ce93
16	5166	1752144400064745354	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_matches2tracks.cpp.o	7a430d0ce5e9018f
13839	13848	1752144413887174579	po_core_lib/lib/libpomvg_factory.dylib	86a87a091f9d18a
13674	13839	1752144413722021704	po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib	4188098061fc0da1
67	4664	1752040966259591883	src/internal/CMakeFiles/pomvg_factory.dir/data/data_features.cpp.o	84afa5a882a5f14f
15	4083	1752040966207552353	src/fileIO/CMakeFiles/pomvg_file_io.dir/g2o_io.cpp.o	66ada1eef1920c41
4664	5982	1752040970856666114	src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/random_generators.cpp.o	7796d73c661ea63b
13899	13968	1752144413947497445	po_core_lib/bin/test_all	8652a108d51fb6ff
14	60	1752040966251657088	po_core_lib/include/proto/features.pb.h	57e0d247de3f6d1d
11160	11176	1752040977352791647	po_core_lib/lib/libpomvg_file_io.dylib	ddcb16277f9c4749
14	4224	1752040966206220213	src/internal/CMakeFiles/pomvg_internal.dir/types.cpp.o	c28a941c23df02f5
4791	4842	1752040970983836930	src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/time_measurement.cpp.o	39a9ae17ebce5c34
73	3542	1752040966265967079	src/internal/CMakeFiles/pomvg_factory.dir/data/data_images.cpp.o	d306557d1b6fd02d
2118	2137	1752040968311013947	po_core_lib/lib/libpomvg_proto.0.dylib	526056faf6c306a1
4224	4287	1752040970416730650	po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib	2c6c2f786e47834
15	5786	1752144400063732849	src/internal/CMakeFiles/pomvg_factory.dir/methods/analytical_reconstruction.cpp.o	15b14874c8f250fe
4287	4308	1752040970479817344	po_core_lib/lib/libpomvg_internal.dylib	e1feb49a143923fa
14	61	1752040966252092051	po_core_lib/include/proto/tracks.pb.cc	af4a04e06708692b
4084	11366	1752040970276108687	src/internal/CMakeFiles/pomvg_factory.dir/methods/LiGT/LiGT.cpp.o	8591fbefc2791a22
62	2046	1752040966254447035	src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/features.pb.cc.o	72d2c8f859f77dd0
2046	6807	1752040968238093354	src/internal/CMakeFiles/pomvg_factory.dir/data/data_tracks.cpp.o	3de6529294a58448
2118	2137	1752040968311013947	po_core_lib/lib/libpomvg_proto.dylib	526056faf6c306a1
72	5170	1752040966264415562	src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_poses.cpp.o	21e884a5401e5fb9
15	5785	1752144400063633811	src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp.o	54a9b9de58741def
11199	14695	1752040977391793701	tests/CMakeFiles/test_all.dir/test_rtcheck_types_performance.cpp.o	34d69e8fc5a7f248
6989	7044	1752040973181897147	po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib	b7cc7918055a94e7
13	6752	1752079774899677511	src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/EigenLMOptimizer.cpp.o	e4e5922e0a9b3fd6
11160	11176	1752040977352791647	po_core_lib/lib/libpomvg_file_io.0.dylib	ddcb16277f9c4749
3916	9241	1752040970108065546	src/internal/CMakeFiles/pomvg_factory.dir/interfaces_preset.cpp.o	923e1de4a6b8704f
16	5725	1752144400064137917	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_PA.cpp.o	66ececec061b7b7e
21	6243	1752144400069287815	src/internal/CMakeFiles/pomvg_factory.dir/methods/opengv_simulator.cpp.o	aaa804e37894a05e
72	3444	1752040966264788149	src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_translations.cpp.o	676c2c15bf011f4a
11604	15057	1751884110396902010	tests/CMakeFiles/test_all.dir/test_two_view_optimizer.cpp.o	3331bcdd33b6d813
15	13674	1752144400063936716	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRP.cpp.o	91d25b5ffa1600f7
16	6002	1752144400064246330	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_global_outlier_removal.cpp.o	7e78532a3ab85d1d
15	61	1752040966252163677	po_core_lib/include/proto/camera_model.pb.h	69929b62022e8326
21	6511	1752144400069583596	src/internal/CMakeFiles/pomvg_factory.dir/methods/visual_simulator.cpp.o	3e8ed0f926b9471b
14	60	1752040966251657088	/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/features.pb.h	57e0d247de3f6d1d
62	1919	1752040966254690246	src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/matches.pb.cc.o	4c3c2fddaf9bf1ee
15	7157	1752144400063520065	src/internal/CMakeFiles/pomvg_factory.dir/factory/factory.cpp.o	cd9a8fca76ef7112
13893	13899	1752144413941141341	po_core_lib/lib/libpo_core.0.dylib	446687ca57a7d006
20	6143	1752144400068536926	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_robustLiRP.cpp.o	76130ae6fd0fca04
13893	13899	1752144413941141341	po_core_lib/lib/libpo_core.dylib	446687ca57a7d006
17	5847	1752144400065268585	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_povgSixPoint.cpp.o	a7ce8674cb1bc1b
13839	13848	1752144413887174579	po_core_lib/lib/libpomvg_factory.0.dylib	86a87a091f9d18a
15	6989	1752040966207390684	src/fileIO/CMakeFiles/pomvg_file_io.dir/file_io.cpp.o	372a14d9a604b41c
65	5162	1752040966257863906	src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/Rt_check.cpp.o	f9ceda4df861170a
49	2755	1753071224158692756	tests/CMakeFiles/test_all.dir/test_rtcheck_types_performance.cpp.o	34d69e8fc5a7f248
49	2906	1753071224158463090	src/internal/CMakeFiles/pomvg_factory.dir/methods/analytical_reconstruction.cpp.o	15b14874c8f250fe
49	4226	1753071224158196591	src/internal/CMakeFiles/pomvg_factory.dir/factory/factory.cpp.o	cd9a8fca76ef7112
49	7598	1753071224158582673	src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRPFast.cpp.o	f0645ff699b9ab5b
7599	7735	1753071231708006584	po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib	4188098061fc0da1
7735	7743	1753071231844775789	po_core_lib/lib/libpomvg_factory.0.dylib	86a87a091f9d18a
7735	7743	1753071231844775789	po_core_lib/lib/libpomvg_factory.dylib	86a87a091f9d18a
7743	7785	1753071231852228565	po_core_lib/lib/libpo_core.0.11.4.0.dylib	ef24f2221df53709
7785	7792	1753071231894809515	po_core_lib/lib/libpo_core.0.dylib	446687ca57a7d006
7785	7792	1753071231894809515	po_core_lib/lib/libpo_core.dylib	446687ca57a7d006
7792	7850	1753071231901574917	po_core_lib/bin/test_all	12c917aaa87fca26
