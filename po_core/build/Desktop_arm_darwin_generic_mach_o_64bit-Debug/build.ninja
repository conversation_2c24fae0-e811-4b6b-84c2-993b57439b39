# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: po_core_lib
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/
# =============================================================================
# Object build statements for SHARED_LIBRARY target po_core


#############################################
# Order-only phony target for po_core

build cmake_object_order_depends_target_po_core: phony || cmake_object_order_depends_target_pomvg_factory cmake_object_order_depends_target_pomvg_file_io cmake_object_order_depends_target_pomvg_internal cmake_object_order_depends_target_pomvg_proto

build CMakeFiles/po_core.dir/po_core.cpp.o: CXX_COMPILER__po_core_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/po_core.cpp || cmake_object_order_depends_target_po_core
  DEFINES = -DAPPLE_M1 -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DUSE_OPENMP -D_DEBUG -Dpo_core_EXPORTS
  DEP_FILE = CMakeFiles/po_core.dir/po_core.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/po_core.dir
  OBJECT_FILE_DIR = CMakeFiles/po_core.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target po_core


#############################################
# Link the shared library po_core_lib/lib/libpo_core.0.11.4.0.dylib

build po_core_lib/lib/libpo_core.0.11.4.0.dylib: CXX_SHARED_LIBRARY_LINKER__po_core_Debug CMakeFiles/po_core.dir/po_core.cpp.o | po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib /opt/homebrew/lib/libprotobuf.dylib /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_city.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_base.2407.0.0.dylib /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib /opt/homebrew/lib/libceres.2.2.0.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib || po_core_lib/lib/libpomvg_factory.dylib po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpomvg_factory.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp   -current_version 0.11.4
  LINK_LIBRARIES = -Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib  po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib  po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib  po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib  /opt/homebrew/lib/libprotobuf.dylib  /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib  /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib  /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_city.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib  /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib  /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib  -Wl,-framework,CoreFoundation  /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib  /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib  /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib  /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib  /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_base.2407.0.0.dylib  /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib  /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib  /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib  /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib  po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib  /opt/homebrew/lib/libceres.2.2.0.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib
  OBJECT_DIR = CMakeFiles/po_core.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libpo_core.0.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = po_core_lib/lib/libpo_core.0.11.4.0.dylib
  TARGET_PDB = po_core.dylib.dbg


#############################################
# Create library symlink po_core_lib/lib/libpo_core.dylib

build po_core_lib/lib/libpo_core.0.dylib po_core_lib/lib/libpo_core.dylib: CMAKE_SYMLINK_LIBRARY po_core_lib/lib/libpo_core.0.11.4.0.dylib
  POST_BUILD = :


#############################################
# Utility command for test

build CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug && /opt/homebrew/Cellar/cmake/3.31.5/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build test: phony CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug && /opt/homebrew/Cellar/cmake/3.31.5/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target pomvg_proto


#############################################
# Order-only phony target for pomvg_proto

build cmake_object_order_depends_target_pomvg_proto: phony || po_core_lib/include/proto/camera_model.pb.cc po_core_lib/include/proto/camera_model.pb.h po_core_lib/include/proto/features.pb.cc po_core_lib/include/proto/features.pb.h po_core_lib/include/proto/matches.pb.cc po_core_lib/include/proto/matches.pb.h po_core_lib/include/proto/relative_poses.pb.cc po_core_lib/include/proto/relative_poses.pb.h po_core_lib/include/proto/tracks.pb.cc po_core_lib/include/proto/tracks.pb.h

build src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/features.pb.cc.o: CXX_COMPILER__pomvg_proto_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/features.pb.cc || cmake_object_order_depends_target_pomvg_proto
  DEFINES = -DAPPLE_M1 -DUSE_OPENMP -D_DEBUG -Dpomvg_proto_EXPORTS
  DEP_FILE = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/features.pb.cc.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  OBJECT_FILE_DIR = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto

build src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/matches.pb.cc.o: CXX_COMPILER__pomvg_proto_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/matches.pb.cc || cmake_object_order_depends_target_pomvg_proto
  DEFINES = -DAPPLE_M1 -DUSE_OPENMP -D_DEBUG -Dpomvg_proto_EXPORTS
  DEP_FILE = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/matches.pb.cc.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  OBJECT_FILE_DIR = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto

build src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/tracks.pb.cc.o: CXX_COMPILER__pomvg_proto_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/tracks.pb.cc || cmake_object_order_depends_target_pomvg_proto
  DEFINES = -DAPPLE_M1 -DUSE_OPENMP -D_DEBUG -Dpomvg_proto_EXPORTS
  DEP_FILE = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/tracks.pb.cc.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  OBJECT_FILE_DIR = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto

build src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/camera_model.pb.cc.o: CXX_COMPILER__pomvg_proto_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/camera_model.pb.cc || cmake_object_order_depends_target_pomvg_proto
  DEFINES = -DAPPLE_M1 -DUSE_OPENMP -D_DEBUG -Dpomvg_proto_EXPORTS
  DEP_FILE = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/camera_model.pb.cc.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  OBJECT_FILE_DIR = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto

build src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/relative_poses.pb.cc.o: CXX_COMPILER__pomvg_proto_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto/relative_poses.pb.cc || cmake_object_order_depends_target_pomvg_proto
  DEFINES = -DAPPLE_M1 -DUSE_OPENMP -D_DEBUG -Dpomvg_proto_EXPORTS
  DEP_FILE = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/relative_poses.pb.cc.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  OBJECT_FILE_DIR = src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto


# =============================================================================
# Link build statements for SHARED_LIBRARY target pomvg_proto


#############################################
# Link the shared library po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib

build po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib: CXX_SHARED_LIBRARY_LINKER__pomvg_proto_Debug src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/features.pb.cc.o src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/matches.pb.cc.o src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/tracks.pb.cc.o src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/camera_model.pb.cc.o src/proto/CMakeFiles/pomvg_proto.dir/__/__/po_core_lib/include/proto/relative_poses.pb.cc.o | /opt/homebrew/lib/libprotobuf.dylib /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_city.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib /opt/homebrew/lib/libabsl_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib /opt/homebrew/lib/libabsl_base.2407.0.0.dylib /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp   -current_version 0.11.4
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/lib  /opt/homebrew/lib/libprotobuf.dylib  /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib  /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib  /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_city.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib  /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib  /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib  /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib  /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib  /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib  -Wl,-framework,CoreFoundation  /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib  /opt/homebrew/lib/libabsl_base.2407.0.0.dylib  /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib  /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib  /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib  /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib
  OBJECT_DIR = src/proto/CMakeFiles/pomvg_proto.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libpomvg_proto.0.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib
  TARGET_PDB = pomvg_proto.dylib.dbg


#############################################
# Create library symlink po_core_lib/lib/libpomvg_proto.dylib

build po_core_lib/lib/libpomvg_proto.0.dylib po_core_lib/lib/libpomvg_proto.dylib: CMAKE_SYMLINK_LIBRARY po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib
  POST_BUILD = :


#############################################
# Utility command for test

build src/proto/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/Cellar/cmake/3.31.5/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/proto/test: phony src/proto/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/proto/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/Cellar/cmake/3.31.5/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/proto/edit_cache: phony src/proto/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/proto/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/proto/rebuild_cache: phony src/proto/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for po_core_lib/include/proto/features.pb.h

build po_core_lib/include/proto/features.pb.h po_core_lib/include/proto/features.pb.cc | ${cmake_ninja_workdir}po_core_lib/include/proto/features.pb.h ${cmake_ninja_workdir}po_core_lib/include/proto/features.pb.cc: CUSTOM_COMMAND /Users/<USER>/Documents/PoMVG/po_core/src/proto/features.proto /opt/homebrew/bin/protoc
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -I /Users/<USER>/Documents/PoMVG/po_core/src/proto /Users/<USER>/Documents/PoMVG/po_core/src/proto/features.proto
  DESC = Running cpp protocol buffer compiler on /Users/<USER>/Documents/PoMVG/po_core/src/proto/features.proto
  restat = 1


#############################################
# Custom command for po_core_lib/include/proto/matches.pb.h

build po_core_lib/include/proto/matches.pb.h po_core_lib/include/proto/matches.pb.cc | ${cmake_ninja_workdir}po_core_lib/include/proto/matches.pb.h ${cmake_ninja_workdir}po_core_lib/include/proto/matches.pb.cc: CUSTOM_COMMAND /Users/<USER>/Documents/PoMVG/po_core/src/proto/matches.proto /opt/homebrew/bin/protoc
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -I /Users/<USER>/Documents/PoMVG/po_core/src/proto /Users/<USER>/Documents/PoMVG/po_core/src/proto/matches.proto
  DESC = Running cpp protocol buffer compiler on /Users/<USER>/Documents/PoMVG/po_core/src/proto/matches.proto
  restat = 1


#############################################
# Custom command for po_core_lib/include/proto/tracks.pb.h

build po_core_lib/include/proto/tracks.pb.h po_core_lib/include/proto/tracks.pb.cc | ${cmake_ninja_workdir}po_core_lib/include/proto/tracks.pb.h ${cmake_ninja_workdir}po_core_lib/include/proto/tracks.pb.cc: CUSTOM_COMMAND /Users/<USER>/Documents/PoMVG/po_core/src/proto/tracks.proto /opt/homebrew/bin/protoc
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -I /Users/<USER>/Documents/PoMVG/po_core/src/proto /Users/<USER>/Documents/PoMVG/po_core/src/proto/tracks.proto
  DESC = Running cpp protocol buffer compiler on /Users/<USER>/Documents/PoMVG/po_core/src/proto/tracks.proto
  restat = 1


#############################################
# Custom command for po_core_lib/include/proto/camera_model.pb.h

build po_core_lib/include/proto/camera_model.pb.h po_core_lib/include/proto/camera_model.pb.cc | ${cmake_ninja_workdir}po_core_lib/include/proto/camera_model.pb.h ${cmake_ninja_workdir}po_core_lib/include/proto/camera_model.pb.cc: CUSTOM_COMMAND /Users/<USER>/Documents/PoMVG/po_core/src/proto/camera_model.proto /opt/homebrew/bin/protoc
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -I /Users/<USER>/Documents/PoMVG/po_core/src/proto /Users/<USER>/Documents/PoMVG/po_core/src/proto/camera_model.proto
  DESC = Running cpp protocol buffer compiler on /Users/<USER>/Documents/PoMVG/po_core/src/proto/camera_model.proto
  restat = 1


#############################################
# Custom command for po_core_lib/include/proto/relative_poses.pb.h

build po_core_lib/include/proto/relative_poses.pb.h po_core_lib/include/proto/relative_poses.pb.cc | ${cmake_ninja_workdir}po_core_lib/include/proto/relative_poses.pb.h ${cmake_ninja_workdir}po_core_lib/include/proto/relative_poses.pb.cc: CUSTOM_COMMAND /Users/<USER>/Documents/PoMVG/po_core/src/proto/relative_poses.proto /opt/homebrew/bin/protoc
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto && /opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -I /Users/<USER>/Documents/PoMVG/po_core/src/proto /Users/<USER>/Documents/PoMVG/po_core/src/proto/relative_poses.proto
  DESC = Running cpp protocol buffer compiler on /Users/<USER>/Documents/PoMVG/po_core/src/proto/relative_poses.proto
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target pomvg_internal


#############################################
# Order-only phony target for pomvg_internal

build cmake_object_order_depends_target_pomvg_internal: phony || .

build src/internal/CMakeFiles/pomvg_internal.dir/types.cpp.o: CXX_COMPILER__pomvg_internal_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/types.cpp || cmake_object_order_depends_target_pomvg_internal
  DEFINES = -DAPPLE_M1 -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DUSE_OPENMP -D_DEBUG -Dpomvg_internal_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_internal.dir/types.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_internal.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_internal.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target pomvg_internal


#############################################
# Link the shared library po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib

build po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib: CXX_SHARED_LIBRARY_LINKER__pomvg_internal_Debug src/internal/CMakeFiles/pomvg_internal.dir/types.cpp.o | /opt/homebrew/lib/libceres.2.2.0.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp   -current_version 0.11.4
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/lib  /opt/homebrew/lib/libceres.2.2.0.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_internal.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libpomvg_internal.0.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib
  TARGET_PDB = pomvg_internal.dylib.dbg


#############################################
# Create library symlink po_core_lib/lib/libpomvg_internal.dylib

build po_core_lib/lib/libpomvg_internal.0.dylib po_core_lib/lib/libpomvg_internal.dylib: CMAKE_SYMLINK_LIBRARY po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib
  POST_BUILD = :

# =============================================================================
# Object build statements for SHARED_LIBRARY target pomvg_factory


#############################################
# Order-only phony target for pomvg_factory

build cmake_object_order_depends_target_pomvg_factory: phony || cmake_object_order_depends_target_pomvg_file_io cmake_object_order_depends_target_pomvg_internal cmake_object_order_depends_target_pomvg_proto

build src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/Rt_check.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/relative_process/Rt_check.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/Rt_check.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process

build src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_pose.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_pose.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_pose.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process

build src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_residuals.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/relative_process/relative_residuals.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_residuals.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_features.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_features.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_features.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_poses.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_poses.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_poses.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_rotations.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_rotations.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_rotations.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_translations.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_global_translations.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_translations.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_images.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_images.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_images.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_matches.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_matches.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_matches.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_points_3d.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_points_3d.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_points_3d.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_relative_rotations.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_relative_rotations.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_relative_rotations.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/data/data_tracks.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/data/data_tracks.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/data/data_tracks.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/data

build src/internal/CMakeFiles/pomvg_factory.dir/evaluator.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/evaluator.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/evaluator.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir

build src/internal/CMakeFiles/pomvg_factory.dir/executable_path.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/executable_path.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/executable_path.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir

build src/internal/CMakeFiles/pomvg_factory.dir/factory/factory.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/factory/factory.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/factory/factory.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/factory

build src/internal/CMakeFiles/pomvg_factory.dir/interfaces_preset.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/interfaces_preset.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/interfaces_preset.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir

build src/internal/CMakeFiles/pomvg_factory.dir/methods/LiGT/LiGT.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/LiGT/LiGT.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/LiGT/LiGT.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/LiGT

build src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/experiment_helpers.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/experiment_helpers.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/experiment_helpers.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV

build src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/random_generators.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/random_generators.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/random_generators.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV

build src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/time_measurement.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/OpenGV/time_measurement.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/time_measurement.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV

build src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/CeresOptimizer.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/CeresOptimizer.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/CeresOptimizer.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer

build src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/EigenLMOptimizer.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/EigenLMOptimizer.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer

build src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer

build src/internal/CMakeFiles/pomvg_factory.dir/methods/analytical_reconstruction.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/analytical_reconstruction.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/analytical_reconstruction.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiGT.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiGT.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiGT.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRP.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRP.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRP.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRPFast.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_LiRPFast.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRPFast.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_PA.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_PA.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_PA.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_global_outlier_removal.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_global_outlier_removal.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_global_outlier_removal.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_matches2tracks.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_matches2tracks.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_matches2tracks.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_povgSixPoint.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_povgSixPoint.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_povgSixPoint.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_relative_cost.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_relative_cost.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_relative_cost.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/method_robustLiRP.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/method_robustLiRP.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/method_robustLiRP.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/opengv_simulator.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/opengv_simulator.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/opengv_simulator.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/methods/visual_simulator.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/visual_simulator.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/methods/visual_simulator.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir/methods

build src/internal/CMakeFiles/pomvg_factory.dir/pomvg_plugin.cpp.o: CXX_COMPILER__pomvg_factory_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/internal/pomvg_plugin.cpp || cmake_object_order_depends_target_pomvg_factory
  DEFINES = -DAPPLE_M1 -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPOMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/configs/methods\" -DPOMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\" -DUSE_OPENMP -D_DEBUG -Dpomvg_factory_EXPORTS
  DEP_FILE = src/internal/CMakeFiles/pomvg_factory.dir/pomvg_plugin.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/internal -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory -I/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process -I/Users/<USER>/Documents/spectra-master -I/Users/<USER>/Documents/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  OBJECT_FILE_DIR = src/internal/CMakeFiles/pomvg_factory.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target pomvg_factory


#############################################
# Link the shared library po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib

build po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib: CXX_SHARED_LIBRARY_LINKER__pomvg_factory_Debug src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/Rt_check.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_pose.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/__/relative_process/relative_residuals.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_features.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_poses.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_rotations.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_global_translations.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_images.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_matches.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_points_3d.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_relative_rotations.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/data/data_tracks.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/evaluator.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/executable_path.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/factory/factory.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/interfaces_preset.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/LiGT/LiGT.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/experiment_helpers.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/random_generators.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/OpenGV/time_measurement.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/CeresOptimizer.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/EigenLMOptimizer.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/analytical_reconstruction.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiGT.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRP.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_LiRPFast.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_PA.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_global_outlier_removal.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_matches2tracks.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_povgSixPoint.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_relative_cost.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/method_robustLiRP.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/opengv_simulator.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/methods/visual_simulator.cpp.o src/internal/CMakeFiles/pomvg_factory.dir/pomvg_plugin.cpp.o | po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib /opt/homebrew/lib/libboost_filesystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/libcurl.tbd /opt/homebrew/lib/libgflags.dylib /opt/homebrew/lib/libprotobuf.dylib /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_city.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_base.2407.0.0.dylib /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib /opt/homebrew/lib/libceres.2.2.0.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib /opt/homebrew/lib/libboost_system.dylib /opt/homebrew/lib/libboost_atomic.dylib || po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp   -current_version 0.11.4
  LINK_LIBRARIES = -Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib  po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib  po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib  po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib  /opt/homebrew/lib/libboost_filesystem.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/libcurl.tbd  /opt/homebrew/lib/libgflags.dylib  /opt/homebrew/lib/libprotobuf.dylib  /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib  /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib  /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_city.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib  /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib  /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib  -Wl,-framework,CoreFoundation  /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib  /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib  /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib  /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib  /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_base.2407.0.0.dylib  /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib  /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib  /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib  /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib  /opt/homebrew/lib/libceres.2.2.0.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib  /opt/homebrew/lib/libboost_system.dylib  /opt/homebrew/lib/libboost_atomic.dylib
  OBJECT_DIR = src/internal/CMakeFiles/pomvg_factory.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libpomvg_factory.0.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib
  TARGET_PDB = pomvg_factory.dylib.dbg


#############################################
# Create library symlink po_core_lib/lib/libpomvg_factory.dylib

build po_core_lib/lib/libpomvg_factory.0.dylib po_core_lib/lib/libpomvg_factory.dylib: CMAKE_SYMLINK_LIBRARY po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib
  POST_BUILD = :


#############################################
# Utility command for test

build src/internal/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/internal && /opt/homebrew/Cellar/cmake/3.31.5/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/internal/test: phony src/internal/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/internal/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/internal && /opt/homebrew/Cellar/cmake/3.31.5/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/internal/edit_cache: phony src/internal/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/internal/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/internal && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/internal/rebuild_cache: phony src/internal/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target pomvg_file_io


#############################################
# Order-only phony target for pomvg_file_io

build cmake_object_order_depends_target_pomvg_file_io: phony || cmake_object_order_depends_target_pomvg_internal

build src/fileIO/CMakeFiles/pomvg_file_io.dir/file_io.cpp.o: CXX_COMPILER__pomvg_file_io_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/fileIO/file_io.cpp || cmake_object_order_depends_target_pomvg_file_io
  DEFINES = -DAPPLE_M1 -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DUSE_OPENMP -D_DEBUG -Dpomvg_file_io_EXPORTS
  DEP_FILE = src/fileIO/CMakeFiles/pomvg_file_io.dir/file_io.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/../internal -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = src/fileIO/CMakeFiles/pomvg_file_io.dir
  OBJECT_FILE_DIR = src/fileIO/CMakeFiles/pomvg_file_io.dir

build src/fileIO/CMakeFiles/pomvg_file_io.dir/g2o_io.cpp.o: CXX_COMPILER__pomvg_file_io_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/src/fileIO/g2o_io.cpp || cmake_object_order_depends_target_pomvg_file_io
  DEFINES = -DAPPLE_M1 -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DUSE_OPENMP -D_DEBUG -Dpomvg_file_io_EXPORTS
  DEP_FILE = src/fileIO/CMakeFiles/pomvg_file_io.dir/g2o_io.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO -I/Users/<USER>/Documents/PoMVG/po_core/src/fileIO/../internal -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = src/fileIO/CMakeFiles/pomvg_file_io.dir
  OBJECT_FILE_DIR = src/fileIO/CMakeFiles/pomvg_file_io.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target pomvg_file_io


#############################################
# Link the shared library po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib

build po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib: CXX_SHARED_LIBRARY_LINKER__pomvg_file_io_Debug src/fileIO/CMakeFiles/pomvg_file_io.dir/file_io.cpp.o src/fileIO/CMakeFiles/pomvg_file_io.dir/g2o_io.cpp.o | po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib /opt/homebrew/lib/libceres.2.2.0.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib || po_core_lib/lib/libpomvg_internal.dylib po_core_lib/lib/libpomvg_internal.dylib
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp   -current_version 0.11.4
  LINK_LIBRARIES = -Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib  po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib  /opt/homebrew/lib/libceres.2.2.0.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib
  OBJECT_DIR = src/fileIO/CMakeFiles/pomvg_file_io.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libpomvg_file_io.0.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib
  TARGET_PDB = pomvg_file_io.dylib.dbg


#############################################
# Create library symlink po_core_lib/lib/libpomvg_file_io.dylib

build po_core_lib/lib/libpomvg_file_io.0.dylib po_core_lib/lib/libpomvg_file_io.dylib: CMAKE_SYMLINK_LIBRARY po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib
  POST_BUILD = :


#############################################
# Utility command for test

build src/fileIO/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/fileIO && /opt/homebrew/Cellar/cmake/3.31.5/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/fileIO/test: phony src/fileIO/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/fileIO/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/fileIO && /opt/homebrew/Cellar/cmake/3.31.5/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/fileIO/edit_cache: phony src/fileIO/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/fileIO/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/fileIO && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/fileIO/rebuild_cache: phony src/fileIO/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target test_all


#############################################
# Order-only phony target for test_all

build cmake_object_order_depends_target_test_all: phony || cmake_object_order_depends_target_po_core cmake_object_order_depends_target_pomvg_factory cmake_object_order_depends_target_pomvg_file_io cmake_object_order_depends_target_pomvg_internal cmake_object_order_depends_target_pomvg_proto

build tests/CMakeFiles/test_all.dir/test_rtcheck_types_performance.cpp.o: CXX_COMPILER__test_all_unscanned_Debug /Users/<USER>/Documents/PoMVG/po_core/tests/test_rtcheck_types_performance.cpp || cmake_object_order_depends_target_test_all
  DEFINES = -DAPPLE_M1 -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DPROJECT_SOURCE_DIR=\"/Users/<USER>/Documents/PoMVG/po_core\" -DUSE_OPENMP -D_DEBUG
  DEP_FILE = tests/CMakeFiles/test_all.dir/test_rtcheck_types_performance.cpp.o.d
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIE -fcolor-diagnostics -Wall -Wextra -Wpedantic -Xpreprocessor -fopenmp
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include -I/Users/<USER>/Documents/PoMVG/po_core/src -I/Users/<USER>/Documents/PoMVG/po_core/tests/../src/internal -I/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/proto -isystem /opt/homebrew/include -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/opt/libomp/include
  OBJECT_DIR = tests/CMakeFiles/test_all.dir
  OBJECT_FILE_DIR = tests/CMakeFiles/test_all.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_all


#############################################
# Link the executable po_core_lib/bin/test_all

build po_core_lib/bin/test_all: CXX_EXECUTABLE_LINKER__test_all_Debug tests/CMakeFiles/test_all.dir/test_rtcheck_types_performance.cpp.o | po_core_lib/lib/libpo_core.0.11.4.0.dylib /opt/homebrew/lib/libprotobuf.dylib po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib /opt/homebrew/lib/libprotobuf.dylib /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_city.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_base.2407.0.0.dylib /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib /opt/homebrew/lib/libceres.2.2.0.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib /opt/homebrew/lib/libgtest_main.a /opt/homebrew/lib/libgtest.a /opt/homebrew/opt/libomp/lib/libomp.dylib || po_core_lib/lib/libpo_core.dylib po_core_lib/lib/libpomvg_factory.dylib po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpo_core.dylib po_core_lib/lib/libpomvg_factory.dylib po_core_lib/lib/libpomvg_proto.dylib po_core_lib/lib/libpomvg_file_io.dylib po_core_lib/lib/libpomvg_internal.dylib
  FLAGS = -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  LINK_FLAGS = -L/opt/homebrew/opt/libomp/lib -lomp
  LINK_LIBRARIES = -Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib  po_core_lib/lib/libpo_core.0.11.4.0.dylib  /opt/homebrew/lib/libprotobuf.dylib  po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib  po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib  /opt/homebrew/lib/libprotobuf.dylib  /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib  /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib  /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib  /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_city.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib  /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib  /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib  /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib  /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib  -Wl,-framework,CoreFoundation  /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib  /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib  /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib  /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib  /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib  /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_base.2407.0.0.dylib  /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib  /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib  /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib  /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib  /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib  po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib  po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib  /opt/homebrew/lib/libceres.2.2.0.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib  /opt/homebrew/lib/libgtest_main.a  /opt/homebrew/lib/libgtest.a  /opt/homebrew/opt/libomp/lib/libomp.dylib
  OBJECT_DIR = tests/CMakeFiles/test_all.dir
  POST_BUILD = :
  PRE_LINK = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/tests && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -E echo Ensuring\ po_core.hpp\ exists... && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -E make_directory /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -E copy /Users/<USER>/Documents/PoMVG/po_core/po_core.hpp.in /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/po_core_lib/include/po_core.hpp && cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  TARGET_FILE = po_core_lib/bin/test_all
  TARGET_PDB = test_all.dbg


#############################################
# Utility command for test

build tests/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/tests && /opt/homebrew/Cellar/cmake/3.31.5/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/test: phony tests/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/tests && /opt/homebrew/Cellar/cmake/3.31.5/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/edit_cache: phony tests/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/tests && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core -B/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/rebuild_cache: phony tests/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libpo_core.dylib: phony po_core_lib/lib/libpo_core.dylib

build libpomvg_factory.dylib: phony po_core_lib/lib/libpomvg_factory.dylib

build libpomvg_file_io.dylib: phony po_core_lib/lib/libpomvg_file_io.dylib

build libpomvg_internal.dylib: phony po_core_lib/lib/libpomvg_internal.dylib

build libpomvg_proto.dylib: phony po_core_lib/lib/libpomvg_proto.dylib

build po_core: phony po_core_lib/lib/libpo_core.dylib

build pomvg_factory: phony po_core_lib/lib/libpomvg_factory.dylib

build pomvg_file_io: phony po_core_lib/lib/libpomvg_file_io.dylib

build pomvg_internal: phony po_core_lib/lib/libpomvg_internal.dylib

build pomvg_proto: phony po_core_lib/lib/libpomvg_proto.dylib

build test_all: phony po_core_lib/bin/test_all

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug

build all: phony po_core_lib/lib/libpo_core.dylib src/proto/all src/internal/all src/fileIO/all tests/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/fileIO

build src/fileIO/all: phony po_core_lib/lib/libpomvg_file_io.dylib

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/internal

build src/internal/all: phony po_core_lib/lib/libpomvg_internal.dylib po_core_lib/lib/libpomvg_factory.dylib

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/src/proto

build src/proto/all: phony po_core_lib/lib/libpomvg_proto.dylib

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/tests

build tests/all: phony po_core_lib/bin/test_all

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/cmake/FindOpenGV.cmake /Users/<USER>/Documents/PoMVG/po_core/cmake/po_core-config.cmake.in /Users/<USER>/Documents/PoMVG/po_core/cmake/po_core-targets.cmake.in /Users/<USER>/Documents/PoMVG/po_core/po_core.hpp.in /Users/<USER>/Documents/PoMVG/po_core/src/fileIO/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/src/internal/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/TwoViewOptimizer.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/analytical_reconstruction.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/gnc_irls_estimator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_GNCInfo.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiGT.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiRP.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiRPFast.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_global_outlier_removal.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_matches2tracks.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_povgSixPoint.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_relative_cost.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_robustLiRP.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/opengv_simulator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/ransac_estimator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/visual_simulator.ini /Users/<USER>/Documents/PoMVG/po_core/src/proto/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/tests/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/version.hpp.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindDependencyMacro.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakePackageConfigHelpers.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseArguments.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakePushCheckState.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckFunctionExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckIncludeFileCXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckSymbolExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindBLAS.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindCURL.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindGTest.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindLAPACK.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPkgConfig.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindProtobuf.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindThreads.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/GoogleTest.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/SelectLibraryConfigurations.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/WriteBasicConfigVersionFile.cmake /opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfig.cmake /opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfigVersion.cmake /opt/homebrew/lib/cmake/BoostDetectToolset-1.87.0.cmake /opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake /opt/homebrew/lib/cmake/Ceres/CeresConfigVersion.cmake /opt/homebrew/lib/cmake/Ceres/CeresTargets-release.cmake /opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake /opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake /opt/homebrew/lib/cmake/Ceres/FindSuiteSparse.cmake /opt/homebrew/lib/cmake/GTest/GTestConfig.cmake /opt/homebrew/lib/cmake/GTest/GTestConfigVersion.cmake /opt/homebrew/lib/cmake/GTest/GTestTargets-release.cmake /opt/homebrew/lib/cmake/GTest/GTestTargets.cmake /opt/homebrew/lib/cmake/TBB/TBBConfig.cmake /opt/homebrew/lib/cmake/TBB/TBBConfigVersion.cmake /opt/homebrew/lib/cmake/TBB/TBBTargets-release.cmake /opt/homebrew/lib/cmake/TBB/TBBTargets.cmake /opt/homebrew/lib/cmake/absl/abslConfig.cmake /opt/homebrew/lib/cmake/absl/abslConfigVersion.cmake /opt/homebrew/lib/cmake/absl/abslTargets-release.cmake /opt/homebrew/lib/cmake/absl/abslTargets.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config-version.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-shared.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-static.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config-version.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-shared.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-static.cmake /opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake /opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config-version.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-shared.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-static.cmake /opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake /opt/homebrew/lib/cmake/gflags/gflags-config.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake /opt/homebrew/lib/cmake/glog/glog-config-version.cmake /opt/homebrew/lib/cmake/glog/glog-config.cmake /opt/homebrew/lib/cmake/glog/glog-modules.cmake /opt/homebrew/lib/cmake/glog/glog-targets-release.cmake /opt/homebrew/lib/cmake/glog/glog-targets.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake /opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake /Users/<USER>/Documents/PoMVG/po_core/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/cmake/FindOpenGV.cmake /Users/<USER>/Documents/PoMVG/po_core/cmake/po_core-config.cmake.in /Users/<USER>/Documents/PoMVG/po_core/cmake/po_core-targets.cmake.in /Users/<USER>/Documents/PoMVG/po_core/po_core.hpp.in /Users/<USER>/Documents/PoMVG/po_core/src/fileIO/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/src/internal/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/TwoViewOptimizer.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/analytical_reconstruction.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/gnc_irls_estimator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_GNCInfo.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiGT.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiRP.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_LiRPFast.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_global_outlier_removal.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_matches2tracks.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_povgSixPoint.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_relative_cost.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/method_robustLiRP.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/opengv_simulator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/ransac_estimator.ini /Users/<USER>/Documents/PoMVG/po_core/src/internal/methods/configs/visual_simulator.ini /Users/<USER>/Documents/PoMVG/po_core/src/proto/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/tests/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/version.hpp.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindDependencyMacro.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakePackageConfigHelpers.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseArguments.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakePushCheckState.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckFunctionExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckIncludeFileCXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckSymbolExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindBLAS.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindCURL.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindGTest.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindLAPACK.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPkgConfig.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindProtobuf.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindThreads.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/GoogleTest.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/SelectLibraryConfigurations.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/WriteBasicConfigVersionFile.cmake /opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfig.cmake /opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfigVersion.cmake /opt/homebrew/lib/cmake/BoostDetectToolset-1.87.0.cmake /opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake /opt/homebrew/lib/cmake/Ceres/CeresConfigVersion.cmake /opt/homebrew/lib/cmake/Ceres/CeresTargets-release.cmake /opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake /opt/homebrew/lib/cmake/Ceres/FindMETIS.cmake /opt/homebrew/lib/cmake/Ceres/FindSuiteSparse.cmake /opt/homebrew/lib/cmake/GTest/GTestConfig.cmake /opt/homebrew/lib/cmake/GTest/GTestConfigVersion.cmake /opt/homebrew/lib/cmake/GTest/GTestTargets-release.cmake /opt/homebrew/lib/cmake/GTest/GTestTargets.cmake /opt/homebrew/lib/cmake/TBB/TBBConfig.cmake /opt/homebrew/lib/cmake/TBB/TBBConfigVersion.cmake /opt/homebrew/lib/cmake/TBB/TBBTargets-release.cmake /opt/homebrew/lib/cmake/TBB/TBBTargets.cmake /opt/homebrew/lib/cmake/absl/abslConfig.cmake /opt/homebrew/lib/cmake/absl/abslConfigVersion.cmake /opt/homebrew/lib/cmake/absl/abslTargets-release.cmake /opt/homebrew/lib/cmake/absl/abslTargets.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config-version.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/boost_atomic-config.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-shared.cmake /opt/homebrew/lib/cmake/boost_atomic-1.87.0/libboost_atomic-variant-static.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config-version.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-shared.cmake /opt/homebrew/lib/cmake/boost_filesystem-1.87.0/libboost_filesystem-variant-static.cmake /opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake /opt/homebrew/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config-version.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/boost_system-config.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-shared.cmake /opt/homebrew/lib/cmake/boost_system-1.87.0/libboost_system-variant-static.cmake /opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake /opt/homebrew/lib/cmake/gflags/gflags-config.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake /opt/homebrew/lib/cmake/glog/glog-config-version.cmake /opt/homebrew/lib/cmake/glog/glog-config.cmake /opt/homebrew/lib/cmake/glog/glog-modules.cmake /opt/homebrew/lib/cmake/glog/glog-targets-release.cmake /opt/homebrew/lib/cmake/glog/glog-targets.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake /opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
