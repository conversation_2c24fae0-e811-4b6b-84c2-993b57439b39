{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4], "jsonFile": "directory-.-Release-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "src/proto", "jsonFile": "directory-src.proto-Release-91884764699e0504eaed.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "src/proto", "targetIndexes": [4]}, {"build": "src/internal", "jsonFile": "directory-src.internal-Release-eb1c9d5b2b236d2c0621.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "src/internal", "targetIndexes": [1, 3]}, {"build": "src/fileIO", "jsonFile": "directory-src.fileIO-Release-57bca8182c318ca7fde5.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "src/fileIO", "targetIndexes": [2]}, {"build": "tests", "jsonFile": "directory-tests-Release-b01f6abb0ab61427ef3b.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [5]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4], "name": "po_core_lib", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "po_core::@6890427a1f51a3e7e1df", "jsonFile": "target-po_core-Release-dd7faa6ec94ca6751f5e.json", "name": "po_core", "projectIndex": 0}, {"directoryIndex": 2, "id": "pomvg_factory::@6c4b52c4923443589346", "jsonFile": "target-pomvg_factory-Release-8cff1e493155377651dc.json", "name": "pomvg_factory", "projectIndex": 0}, {"directoryIndex": 3, "id": "pomvg_file_io::@f8615aabce025a27f52c", "jsonFile": "target-pomvg_file_io-Release-33b282a5a0217ea0cda0.json", "name": "pomvg_file_io", "projectIndex": 0}, {"directoryIndex": 2, "id": "pomvg_internal::@6c4b52c4923443589346", "jsonFile": "target-pomvg_internal-Release-c97ee6a60f7242b4ea0a.json", "name": "pomvg_internal", "projectIndex": 0}, {"directoryIndex": 1, "id": "pomvg_proto::@4466fd9208ca80570770", "jsonFile": "target-pomvg_proto-Release-32cac0344adce9c1d6b4.json", "name": "pomvg_proto", "projectIndex": 0}, {"directoryIndex": 4, "id": "test_all::@a44f0ac069e85531cdee", "jsonFile": "target-test_all-Release-40992ae52b0b99ca6098.json", "name": "test_all", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release", "source": "/Users/<USER>/Documents/PoMVG/po_core"}, "version": {"major": 2, "minor": 7}}