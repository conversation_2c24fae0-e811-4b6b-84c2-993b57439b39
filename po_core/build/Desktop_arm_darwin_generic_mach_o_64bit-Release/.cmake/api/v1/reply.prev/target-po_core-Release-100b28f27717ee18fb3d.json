{"artifacts": [{"path": "po_core_lib/lib/libpo_core.dylib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "set_target_properties", "include", "find_package", "add_compile_options", "add_compile_definitions", "add_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "src/proto/CMakeLists.txt", "/opt/homebrew/lib/cmake/absl/abslTargets.cmake", "/opt/homebrew/lib/cmake/absl/abslConfig.cmake", "src/fileIO/CMakeLists.txt", "src/internal/CMakeLists.txt", "/opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake", "/opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 363, "parent": 0}, {"command": 1, "file": 0, "line": 382, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 75, "parent": 3}, {"command": 4, "file": 0, "line": 309, "parent": 0}, {"file": 3, "parent": 5}, {"command": 3, "file": 3, "line": 32, "parent": 6}, {"file": 2, "parent": 7}, {"command": 2, "file": 2, "line": 905, "parent": 8}, {"command": 2, "file": 2, "line": 851, "parent": 8}, {"command": 2, "file": 2, "line": 923, "parent": 8}, {"command": 2, "file": 2, "line": 1563, "parent": 8}, {"command": 2, "file": 2, "line": 1031, "parent": 8}, {"command": 2, "file": 2, "line": 797, "parent": 8}, {"command": 2, "file": 2, "line": 1787, "parent": 8}, {"command": 2, "file": 2, "line": 1760, "parent": 8}, {"command": 2, "file": 2, "line": 1121, "parent": 8}, {"command": 2, "file": 2, "line": 1689, "parent": 8}, {"command": 2, "file": 2, "line": 1698, "parent": 8}, {"command": 2, "file": 2, "line": 1715, "parent": 8}, {"command": 2, "file": 2, "line": 1527, "parent": 8}, {"command": 2, "file": 2, "line": 168, "parent": 8}, {"command": 2, "file": 2, "line": 536, "parent": 8}, {"command": 2, "file": 2, "line": 545, "parent": 8}, {"command": 2, "file": 2, "line": 581, "parent": 8}, {"command": 2, "file": 2, "line": 608, "parent": 8}, {"command": 2, "file": 2, "line": 599, "parent": 8}, {"file": 4}, {"command": 1, "file": 4, "line": 45, "parent": 28}, {"file": 5}, {"command": 1, "file": 5, "line": 67, "parent": 30}, {"command": 4, "file": 5, "line": 5, "parent": 30}, {"file": 7, "parent": 32}, {"command": 3, "file": 7, "line": 272, "parent": 33}, {"file": 6, "parent": 34}, {"command": 2, "file": 6, "line": 61, "parent": 35}, {"command": 5, "file": 0, "line": 181, "parent": 0}, {"command": 5, "file": 0, "line": 196, "parent": 0}, {"command": 6, "file": 0, "line": 33, "parent": 0}, {"command": 7, "file": 0, "line": 129, "parent": 0}, {"command": 8, "file": 0, "line": 395, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics"}, {"backtrace": 37, "fragment": "-Wall"}, {"backtrace": 37, "fragment": "-Wextra"}, {"backtrace": 37, "fragment": "-Wpedantic"}, {"backtrace": 38, "fragment": "-O3"}, {"backtrace": 38, "fragment": "-march=native"}], "defines": [{"backtrace": 39, "define": "APPLE_M1"}, {"backtrace": 2, "define": "GFLAGS_IS_A_DLL=0"}, {"backtrace": 2, "define": "GLOG_CUSTOM_PREFIX_SUPPORT"}, {"backtrace": 40, "define": "USE_OPENMP"}, {"define": "po_core_EXPORTS"}], "includes": [{"backtrace": 41, "path": "/Users/<USER>/Documents/PoMVG/po_core/src/internal"}, {"backtrace": 41, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include/proto"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include/eigen3"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "pomvg_factory::@6c4b52c4923443589346"}, {"backtrace": 2, "id": "pomvg_file_io::@f8615aabce025a27f52c"}, {"backtrace": 2, "id": "pomvg_proto::@4466fd9208ca80570770"}, {"backtrace": 2, "id": "pomvg_internal::@6c4b52c4923443589346"}], "id": "po_core::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-L/opt/homebrew/opt/libomp/lib -lomp", "role": "flags"}, {"fragment": "-Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_factory.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libprotobuf.dylib", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/libabsl_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/homebrew/lib/libabsl_city.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "-Wl,-framework,CoreFoundation", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_strings.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/homebrew/lib/libabsl_int128.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_base.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 29, "fragment": "po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/homebrew/lib/libceres.2.2.0.dylib", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/homebrew/lib/libglog.0.6.0.dylib", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/homebrew/lib/libgflags.2.2.2.dylib", "role": "libraries"}], "language": "CXX"}, "name": "po_core", "nameOnDisk": "libpo_core.dylib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "po_core.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}