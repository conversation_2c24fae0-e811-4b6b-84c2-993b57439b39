{"artifacts": [{"path": "po_core_lib/lib/libpomvg_factory.dylib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "set_target_properties", "include", "find_package", "set_property", "boost_find_component", "add_compile_options", "add_compile_definitions", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["src/internal/CMakeLists.txt", "src/proto/CMakeLists.txt", "/opt/homebrew/lib/cmake/absl/abslTargets.cmake", "/opt/homebrew/lib/cmake/absl/abslConfig.cmake", "CMakeLists.txt", "/opt/homebrew/lib/cmake/Ceres/CeresTargets.cmake", "/opt/homebrew/lib/cmake/Ceres/CeresConfig.cmake", "/opt/homebrew/lib/cmake/boost_filesystem-1.87.0/boost_filesystem-config.cmake", "/opt/homebrew/lib/cmake/Boost-1.87.0/BoostConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 94, "parent": 0}, {"command": 1, "file": 0, "line": 141, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 75, "parent": 3}, {"file": 4}, {"command": 4, "file": 4, "line": 309, "parent": 5}, {"file": 3, "parent": 6}, {"command": 3, "file": 3, "line": 32, "parent": 7}, {"file": 2, "parent": 8}, {"command": 2, "file": 2, "line": 905, "parent": 9}, {"command": 2, "file": 2, "line": 851, "parent": 9}, {"command": 2, "file": 2, "line": 923, "parent": 9}, {"command": 2, "file": 2, "line": 1563, "parent": 9}, {"command": 2, "file": 2, "line": 1031, "parent": 9}, {"command": 2, "file": 2, "line": 797, "parent": 9}, {"command": 2, "file": 2, "line": 1787, "parent": 9}, {"command": 2, "file": 2, "line": 1760, "parent": 9}, {"command": 2, "file": 2, "line": 1121, "parent": 9}, {"command": 2, "file": 2, "line": 1689, "parent": 9}, {"command": 2, "file": 2, "line": 1698, "parent": 9}, {"command": 2, "file": 2, "line": 1715, "parent": 9}, {"command": 2, "file": 2, "line": 1527, "parent": 9}, {"command": 2, "file": 2, "line": 168, "parent": 9}, {"command": 2, "file": 2, "line": 536, "parent": 9}, {"command": 2, "file": 2, "line": 545, "parent": 9}, {"command": 2, "file": 2, "line": 581, "parent": 9}, {"command": 2, "file": 2, "line": 608, "parent": 9}, {"command": 2, "file": 2, "line": 599, "parent": 9}, {"command": 1, "file": 0, "line": 67, "parent": 0}, {"command": 4, "file": 0, "line": 5, "parent": 0}, {"file": 6, "parent": 30}, {"command": 3, "file": 6, "line": 272, "parent": 31}, {"file": 5, "parent": 32}, {"command": 2, "file": 5, "line": 61, "parent": 33}, {"command": 4, "file": 4, "line": 258, "parent": 5}, {"file": 8, "parent": 35}, {"command": 6, "file": 8, "line": 262, "parent": 36}, {"command": 4, "file": 8, "line": 141, "parent": 37}, {"file": 7, "parent": 38}, {"command": 5, "file": 7, "line": 103, "parent": 39}, {"command": 7, "file": 4, "line": 181, "parent": 5}, {"command": 7, "file": 4, "line": 196, "parent": 5}, {"command": 8, "file": 4, "line": 33, "parent": 5}, {"command": 9, "file": 0, "line": 175, "parent": 0}, {"command": 10, "file": 4, "line": 129, "parent": 5}, {"command": 11, "file": 0, "line": 114, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Xpreprocessor -fopenmp -I/opt/homebrew/opt/libomp/include -O3 -DNDEBUG -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics"}, {"backtrace": 41, "fragment": "-Wall"}, {"backtrace": 41, "fragment": "-Wextra"}, {"backtrace": 41, "fragment": "-Wpedantic"}, {"backtrace": 42, "fragment": "-O3"}, {"backtrace": 42, "fragment": "-march=native"}], "defines": [{"backtrace": 43, "define": "APPLE_M1"}, {"backtrace": 2, "define": "BOOST_ATOMIC_DYN_LINK"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 2, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 2, "define": "GFLAGS_IS_A_DLL=0"}, {"backtrace": 2, "define": "GLOG_CUSTOM_PREFIX_SUPPORT"}, {"backtrace": 44, "define": "POMVG_BUILD_CONFIG_DIR=\"/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods\""}, {"backtrace": 44, "define": "POMVG_INSTALL_CONFIG_DIR=\"/pomvg/methods/configs\""}, {"backtrace": 45, "define": "USE_OPENMP"}, {"define": "pomvg_factory_EXPORTS"}], "includes": [{"backtrace": 46, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/PoMVG/po_core/src/internal"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/../fileIO"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/factory"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/PoMVG/po_core/src/internal/../relative_process"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/spectra-master"}, {"backtrace": 46, "path": "/Users/<USER>/Documents/spectra-master/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/include/proto"}, {"backtrace": 46, "isSystem": true, "path": "/opt/homebrew/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include/eigen3"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}], "dependencies": [{"backtrace": 2, "id": "pomvg_proto::@4466fd9208ca80570770"}, {"backtrace": 2, "id": "pomvg_internal::@6c4b52c4923443589346"}, {"backtrace": 2, "id": "pomvg_file_io::@f8615aabce025a27f52c"}], "id": "pomvg_factory::@6c4b52c4923443589346", "link": {"commandFragments": [{"fragment": "-L/opt/homebrew/opt/libomp/lib -lomp", "role": "flags"}, {"fragment": "-Wl,-rpath,/Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/lib -Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_proto.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_file_io.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "po_core_lib/lib/libpomvg_internal.0.11.4.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libboost_filesystem.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/libcurl.tbd", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libgflags.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libprotobuf.dylib", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/homebrew/lib/libabsl_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/homebrew/lib/libabsl_city.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 21, "fragment": "-Wl,-framework,CoreFoundation", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_strings.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libabsl_int128.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/homebrew/lib/libabsl_base.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/homebrew/lib/libceres.2.2.0.dylib", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/homebrew/lib/libglog.0.6.0.dylib", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/homebrew/lib/libgflags.2.2.2.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libboost_system.dylib", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/homebrew/lib/libboost_atomic.dylib", "role": "libraries"}], "language": "CXX"}, "name": "pomvg_factory", "nameOnDisk": "libpomvg_factory.dylib", "paths": {"build": "src/internal", "source": "src/internal"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/relative_process/Rt_check.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/relative_process/relative_pose.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/relative_process/relative_residuals.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_features.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_global_poses.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_global_rotations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_global_translations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_images.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_matches.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_points_3d.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_relative_rotations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/data/data_tracks.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/evaluator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/executable_path.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/factory/factory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/interfaces_preset.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/LiGT/LiGT.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/OpenGV/experiment_helpers.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/OpenGV/random_generators.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/OpenGV/time_measurement.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/TwoViewOptimizer/CeresOptimizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/analytical_reconstruction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_LiGT.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_LiRP.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_LiRPFast.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_PA.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_global_outlier_removal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_matches2tracks.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_povgSixPoint.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_relative_cost.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/method_robustLiRP.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/opengv_simulator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/methods/visual_simulator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/internal/pomvg_plugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/relative_process/Rt_check.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/relative_process/compute_coefficients.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/relative_process/costor_info.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/relative_process/relative_pose.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/relative_process/relative_residuals.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_camera_models.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_features.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_global_poses.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_global_rotations.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_global_translations.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_images.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_matches.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_points_3d.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_relative_poses.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_relative_rotations.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/data/data_tracks.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/evaluator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/executable_path.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/factory/factory.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/factory/factory_lookup.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/factory/factory_template_impl.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/factory/factory_utils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/gnc_irls.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/inifile.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/interfaces.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/interfaces_preset.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/interfaces_preset_profiler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/interfaces_robust_estimator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/LiGT/LiGT.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/OpenGV/experiment_helpers.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/OpenGV/random_generators.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/OpenGV/time_measurement.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/PA/pa_reprojection_error_calibrated.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/TwoViewOptimizer/CeresOptimizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/TwoViewOptimizer/EigenLMOptimizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/TwoViewOptimizer/TwoViewOptimizerBase.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/TwoViewOptimizer/method_TwoViewOptimizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/analytical_reconstruction.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_LiGT.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_LiRP.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_LiRPFast.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_PA.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_global_outlier_removal.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_matches2tracks.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_povgSixPoint.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_relative_cost.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/method_robustLiRP.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/opengv_simulator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/methods/visual_simulator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/pb_dataio.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/pomvg_plugin.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/pomvg_plugin_register.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/ransac_estimator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/types.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/internal/union_find.hpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}