# Find package module for GOPT library.
#
# The following variables are set by this module:
#
#   GOPT_FOUND: TRUE if GOPT is found.
#   GOPT_VERSION: GOPT version.
#   GOPT_INCLUDE_DIRS: Include directories for GOPT.
#   GOPT_LINK_DIRS: Link directories for GOPT.
#   GOPT_LIBRARIES: Libraries required to link GOPT.
#   GOPT_CUDA_ENABLED: Whether GOPT was compiled with CUDA support.

get_filename_component(GOPT_INSTALL_PREFIX ${CMAKE_CURRENT_LIST_FILE} PATH)
set(GOPT_INSTALL_PREFIX "${GOPT_INSTALL_PREFIX}/../..")

set(GOPT_FOUND FALSE)

# Set hints for finding dependency packages.

set(EIGEN3_INCLUDE_DIR_HINTS @EIGEN3_INCLUDE_DIR_HINTS@)

set(GLOG_INCLUDE_DIR_HINTS @GLOG_INCLUDE_DIR_HINTS@)
set(G<PERSON><PERSON><PERSON>_LIBRARY_DIR_HINTS @GLOG_LIBRARY_DIR_HINTS@)

# Find dependency packages.

set(TEMP_CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH})
set(CMAKE_MODULE_PATH ${GOPT_INSTALL_PREFIX}/share/gopt/cmake)

if(GOPT_FIND_QUIETLY)
    find_package(Ceres QUIET)

    find_package(Eigen3 QUIET)

    find_package(Glog QUIET)
    find_package(GTest QUIET)
    find_package(GFlags QUIET)
    
    find_package(SuiteSparse QUIET)
else()
    find_package(Ceres REQUIRED)

    find_package(Eigen3 REQUIRED)

    find_package(Glog REQUIRED)
    find_package(GTest REQUIRED)
    find_package(GFlags REQUIRED)
    
    find_package(SuiteSparse REQUIRED)

endif()

# Set the exported variables.

set(GOPT_FOUND TRUE)

# set(GOPT_VERSION @GOPT_VERSION@)

set(GOPT_OPENMP_ENABLED @OPENMP_ENABLED@)

# set(GOPT_CUDA_ENABLED @CUDA_ENABLED@)
# set(GOPT_CUDA_MIN_VERSION @CUDA_MIN_VERSION@)


set(GOPT_INCLUDE_DIRS
    ${GOPT_INSTALL_PREFIX}/include/
    ${GOPT_INSTALL_PREFIX}/include/gopt
    ${GOPT_INSTALL_PREFIX}/include/gopt/lib
    ${EIGEN3_INCLUDE_DIRS}
    ${GLOG_INCLUDE_DIRS}
    ${CERES_INCLUDE_DIRS}
    ${SUITESPARSE_INCLUDE_DIRS}
    ${GTEST_INCLUDE_DIRS}
)

set(GOPT_LINK_DIRS
    ${GOPT_INSTALL_PREFIX}/lib/gopt
)

set(GOPT_INTERNAL_LIBRARIES
    graclus
)

set(GOPT_EXTERNAL_LIBRARIES
    ${CMAKE_DL_LIBS}
    ${GLOG_LIBRARIES}
    ${GTEST_LIBRARIES}
    ${GFLAGS_LIBRARIES}
    ${CERES_LIBRARIES}
)

if(UNIX)
    list(APPEND GOPT_EXTERNAL_LIBRARIES
        pthread)
endif()

if(GOPT_OPENMP_ENABLED)
    find_package(OpenMP QUIET)
    add_definitions("-DOPENMP_ENABLED")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
endif()

# if(GOPT_CUDA_ENABLED)
#     find_package(CUDA ${GOPT_CUDA_MIN_VERSION} QUIET)
#     list(APPEND GOPT_EXTERNAL_LIBRARIES ${CUDA_LIBRARIES})
#     list(APPEND GOPT_INTERNAL_LIBRARIES gopt_cuda)
# endif()

set(GOPT_LIBRARIES
    gopt
    ${GOPT_INTERNAL_LIBRARIES}
    ${GOPT_EXTERNAL_LIBRARIES}
)

# Cleanup of configuration variables.

set(CMAKE_MODULE_PATH ${TEMP_CMAKE_MODULE_PATH})

unset(GOPT_INSTALL_PREFIX)
unset(EIGEN3_INCLUDE_DIR_HINTS)
unset(GLOG_INCLUDE_DIR_HINTS)
unset(GLOG_LIBRARY_DIR_HINTS)
unset(GFLAGS_INCLUDE_DIR_HINTS)
unset(GFLAGS_LIBRARY_DIR_HINTS)
unset(GFLAGS_LIBRARY_DIR_HINTS)
