// Copyright (C) 2014 The Regents of the University of California (Regents).
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//
//     * Neither the name of The Regents or University of California nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
//
// Please contact the author of this library if you have any questions.
// Author: Chris Sweeney (<EMAIL>)

// BSD 3-Clause License

// Copyright (c) 2021, Chenyu
// All rights reserved.

// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:

// 1. Redistributions of source code must retain the above copyright notice, this
//    list of conditions and the following disclaimer.

// 2. Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.

// 3. Neither the name of the copyright holder nor the names of its
//    contributors may be used to endorse or promote products derived from
//    this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
// SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
// CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
// OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef TRANSLATION_AVERAGING_POSITION_ESTIMATOR_H_
#define TRANSLATION_AVERAGING_POSITION_ESTIMATOR_H_

#include <unordered_map>

#include <Eigen/Core>
#include <glog/logging.h>

#include "geometry/track_builder.h"
#include "utils/types.h"
#include "utils/util.h"
#include "utils/hash.h"

namespace gopt {

enum class PositionEstimatorType : int {
  LUD = 0,
  BATA = 1,
  LIGT = 2
};

typedef Eigen::Matrix<double, Eigen::Dynamic, 3> KeypointsMat;

struct PositionEstimatorOptions {
  PositionEstimatorType estimator_type = PositionEstimatorType::LUD;

  bool verbose = true;

  // Options for ADMM QP solver.
  int max_num_iterations = 400;

  // Maximum number of reweighted iterations.
  int max_num_reweighted_iterations = 10;

  // A measurement for convergence criterion.
  double convergence_criterion = 1e-4;

  // Tracks and normalized_keypoints only take effect for LIGT.
  std::vector<TrackElements> tracks;
  std::unordered_map<image_t, KeypointsMat> normalized_keypoints;

  bool Check() const {
    CHECK_GT(max_num_iterations, 0);

    if (estimator_type == PositionEstimatorType::LIGT) {
      CHECK_GT(tracks.size(), 0);
      CHECK_GT(normalized_keypoints.size(), 0);
    }
    return true;
  }
};

// A generic class defining the interface for global position estimation
// methods. These methods take in as input the (global/absolute) orientation
// estimated for each camera and pairwise translation directions between pairs
// of cameras. Additional information such as track/correspondences can also be
// passed in as needed, but those will be specific to the subclass
// implementation.
class PositionEstimator {
 public:
  PositionEstimator() {}
  virtual ~PositionEstimator() {}

  // Input the view pairs containing relative poses between matched
  // geometrically verified views, as well as the global (absolute) orientations
  // of the camera that were previously estimated.
  //
  // Returns true if the position estimation was a success, false if there was a
  // failure. If false is returned, the contents of positions are undefined.
  virtual bool EstimatePositions(
      const std::unordered_map<ImagePair, TwoViewGeometry>& view_pairs,
      const std::unordered_map<image_t, Eigen::Vector3d>& orientation,
      std::unordered_map<image_t, Eigen::Vector3d>* positions) = 0;

 private:
  DISALLOW_COPY_AND_ASSIGN(PositionEstimator);
};

}  // namespace gopt

#endif  // TRANSLATION_AVERAGING_POSITION_ESTIMATOR_H_
