#include "utils/random.h"

#include <algorithm>
#include <random> // Required for std::mt19937 and std::shuffle
#include <vector> // Ensure vector is included for std::vector

namespace gopt
{

  template <typename T>
  void RandomNumberGenerator::RandShuffle(std::vector<T> *vec)
  {
    // std::random_shuffle((*vec).begin(), (*vec).end()); // Deprecated and removed in C++17
    std::random_device rd;
    std::mt19937 g(rd());
    std::shuffle((*vec).begin(), (*vec).end(), g);
  }

} // namespace gopt
