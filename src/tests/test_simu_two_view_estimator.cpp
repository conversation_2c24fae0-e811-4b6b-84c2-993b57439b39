/**
 * @file test_simu_two_view_estimator.cpp
 * @brief 双视图位姿估计器仿真测试
 */

#include <gtest/gtest.h>
#include <po_core.hpp>

#include <filesystem>
#include <opengv/relative_pose/methods.hpp>
#include "random_generators.hpp"
#include "experiment_helpers.hpp"
#include <common/converter/converter_opengv.hpp>

namespace
{
    using namespace PoSDK;
    using namespace opengv;

    class TestSimuTwoViewEstimator : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称，用作ProfileCommit
            double max_parallax = 2.0;     // 平移幅度
            double max_rotation = 0.5;     // 旋转幅度(弧度)
            size_t num_points = 300;       // 每对视图的特征点数量 - 统一改为300
            size_t num_pairs = 1;          // 视图对数量
            double noise = 0.0;            // 噪声水平
            double outlier_fraction = 0.0; // 外点比例
        };

        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 设置conda环境用于Python绘图（根据您的环境调整）
            // 如果conda不可用，系统会自动回退到系统Python
            Interface::EvaluatorManager::SetCondaEnv("pymagsac_py312");

            // 初始化方法
            method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("two_view_estimator"));
            ASSERT_TRUE(method_ != nullptr);

            // 初始化相机参数
            InitializeCamera();
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 初始化相机参数
        void InitializeCamera()
        {
            auto camera_model_data = FactoryData::Create("data_camera_models");
            ASSERT_TRUE(camera_model_data != nullptr);

            auto camera_models = GetDataPtr<types::CameraModels>(camera_model_data);
            ASSERT_TRUE(camera_models != nullptr);

            // 添加相机模型
            types::CameraModel camera;
            camera.intrinsics.fx = 2759.48;
            camera.intrinsics.fy = 2764.16;
            camera.intrinsics.cx = 1520.69;
            camera.intrinsics.cy = 1006.81;
            camera.intrinsics.width = 3040;
            camera.intrinsics.height = 2014;
            camera_models->push_back(camera);

            camera_model_data_ = camera_model_data;
        }

        // 生成多视图对场景数据
        void GenerateSceneMultiPairs(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 1. 准备相机模型数据
            auto camera_model_data = FactoryData::Create("data_camera_models");
            ASSERT_TRUE(camera_model_data != nullptr);
            auto camera_models = GetDataPtr<types::CameraModels>(camera_model_data);
            ASSERT_TRUE(camera_models != nullptr);

            // 添加相机模型
            types::CameraModel camera;
            camera.intrinsics.fx = 2759.48;
            camera.intrinsics.fy = 2764.16;
            camera.intrinsics.cx = 1520.69;
            camera.intrinsics.cy = 1006.81;
            camera.intrinsics.width = 3040;
            camera.intrinsics.height = 2014;
            camera_models->push_back(camera);

            // 2. 准备特征点数据
            auto features_data = FactoryData::Create("data_features");
            ASSERT_TRUE(features_data != nullptr);
            auto features_info = GetDataPtr<types::FeaturesInfo>(features_data);
            ASSERT_TRUE(features_info != nullptr);
            features_info->resize(config.num_pairs + 1); // N对视图需要N+1个视图

            auto matches_data = FactoryData::Create("data_matches");
            ASSERT_TRUE(matches_data != nullptr);
            auto matches = GetDataPtr<Matches>(matches_data);
            ASSERT_TRUE(matches != nullptr);

            ground_truth_Rs_.resize(config.num_pairs);
            ground_truth_ts_.resize(config.num_pairs);

            // 记录每个视图中已有的特征点数量
            std::vector<size_t> features_count(config.num_pairs + 1, 0);

            // 为每对视图生成数据
            for (size_t pair_idx = 0; pair_idx < config.num_pairs; ++pair_idx)
            {
                // 获取当前视图对的ID
                IndexT view_i = pair_idx;
                IndexT view_j = pair_idx + 1;

                // 生成相对位姿
                translation_t position1 = Eigen::Vector3d::Zero();
                rotation_t rotation1 = Eigen::Matrix3d::Identity();
                translation_t position2 = generateRandomTranslation(config.max_parallax);
                rotation_t rotation2 = generateRandomRotation(config.max_rotation);

                // 提取真值相对位姿 - 使用PoSDK标准约定
                extractPoSDKRelativePose(
                    position1, position2,
                    rotation1, rotation2,
                    ground_truth_ts_[pair_idx],
                    ground_truth_Rs_[pair_idx],
                    true);

                // 生成特征点
                translations_t camOffsets;
                rotations_t camRotations;
                generateCentralCameraSystem(camOffsets, camRotations);

                bearingVectors_t bearingVectors1, bearingVectors2;
                std::vector<int> camCorrespondences1, camCorrespondences2;
                Eigen::MatrixXd gt(3, config.num_points);

                // generateRandom2D2DCorrespondences(
                //     position1, rotation1,
                //     position2, rotation2,
                //     camOffsets, camRotations,
                //     config.num_points,
                //     config.noise,
                //     config.outlier_fraction,
                //     bearingVectors1, bearingVectors2,
                //     camCorrespondences1, camCorrespondences2,
                //     gt);
                generateRandom2D2DPlanarCorrespondences(
                    position1, rotation1,
                    position2, rotation2,
                    camOffsets, camRotations,
                    config.num_points,
                    config.noise,
                    config.outlier_fraction,
                    20.0,  // planeDistance
                    100.0, // X_length
                    100.0, // Y_length
                    bearingVectors1, bearingVectors2,
                    camCorrespondences1, camCorrespondences2,
                    gt);
                // 创建视图对的匹配
                ViewPair view_pair(view_i, view_j);
                IdMatches id_matches;
                id_matches.reserve(config.num_points);

                // 为当前视图对生成特征点和匹配
                for (size_t pt_idx = 0; pt_idx < bearingVectors1.size(); ++pt_idx)
                {
                    // 生成第一个视图的特征点
                    Eigen::Vector2d norm_point1(bearingVectors1[pt_idx].x() / bearingVectors1[pt_idx].z(),
                                                bearingVectors1[pt_idx].y() / bearingVectors1[pt_idx].z());
                    types::Feature feat1;
                    feat1.x() = camera.intrinsics.fx * norm_point1.x() + camera.intrinsics.cx;
                    feat1.y() = camera.intrinsics.fy * norm_point1.y() + camera.intrinsics.cy;
                    // 添加到特征数据中
                    alignas(16) types::FeaturePoint fp1(feat1);
                    (*features_info)[view_i].features.push_back(fp1);

                    // 生成第二个视图的特征点
                    Eigen::Vector2d norm_point2(bearingVectors2[pt_idx].x() / bearingVectors2[pt_idx].z(),
                                                bearingVectors2[pt_idx].y() / bearingVectors2[pt_idx].z());
                    types::Feature feat2;
                    feat2.x() = camera.intrinsics.fx * norm_point2.x() + camera.intrinsics.cx;
                    feat2.y() = camera.intrinsics.fy * norm_point2.y() + camera.intrinsics.cy;
                    // 添加到特征数据中
                    alignas(16) types::FeaturePoint fp2(feat2);
                    (*features_info)[view_j].features.push_back(fp2);

                    // 添加匹配，考虑已有的特征点数量
                    IdMatch match;
                    match.i = features_count[view_i] + pt_idx; // 在视图i中的累积索引
                    match.j = features_count[view_j] + pt_idx; // 在视图j中的累积索引
                    id_matches.push_back(match);
                }

                // 更新特征点计数
                features_count[view_i] += bearingVectors1.size();
                features_count[view_j] += bearingVectors2.size();

                // 将匹配添加到matches中
                (*matches)[view_pair] = id_matches;
            }

            // 设置数据
            method_->SetRequiredData(camera_model_data);
            method_->SetRequiredData(features_data);
            method_->SetRequiredData(matches_data);
        }

        // 生成场景数据
        void GenerateScene(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 生成相对位姿
            translation_t position1 = Eigen::Vector3d::Zero();
            rotation_t rotation1 = Eigen::Matrix3d::Identity();
            translation_t position2 = generateRandomTranslation(config.max_parallax);
            rotation_t rotation2 = generateRandomRotation(config.max_rotation);

            // 提取真值相对位姿 - 使用PoSDK标准约定
            extractPoSDKRelativePose(
                position1, position2,
                rotation1, rotation2,
                ground_truth_t_, ground_truth_R_,
                true); // normalize translation

            // 1. 准备相机模型数据
            camera_model_data_ = FactoryData::Create("data_camera_models");
            ASSERT_TRUE(camera_model_data_ != nullptr);
            auto camera_models = GetDataPtr<types::CameraModels>(camera_model_data_);
            ASSERT_TRUE(camera_models != nullptr);

            // 添加相机模型
            types::CameraModel camera;
            camera.intrinsics.fx = 2759.48;
            camera.intrinsics.fy = 2764.16;
            camera.intrinsics.cx = 1520.69;
            camera.intrinsics.cy = 1006.81;
            camera.intrinsics.width = 3040;
            camera.intrinsics.height = 2014;
            camera_models->push_back(camera);

            // 2. 准备特征点数据
            features_data_ = FactoryData::Create("data_features");
            ASSERT_TRUE(features_data_ != nullptr);
            auto features_info = GetDataPtr<types::FeaturesInfo>(features_data_);
            ASSERT_TRUE(features_info != nullptr);
            features_info->resize(2); // 两个视图

            // 3. 准备匹配数据
            matches_data_ = FactoryData::Create("data_matches");
            ASSERT_TRUE(matches_data_ != nullptr);
            auto matches = GetDataPtr<Matches>(matches_data_);
            ASSERT_TRUE(matches != nullptr);

            // 生成特征点和匹配
            translations_t camOffsets;
            rotations_t camRotations;
            generateCentralCameraSystem(camOffsets, camRotations);

            bearingVectors_t bearingVectors1, bearingVectors2;
            std::vector<int> camCorrespondences1, camCorrespondences2;
            Eigen::MatrixXd gt(3, config.num_points);

            generateRandom2D2DCorrespondences(
                position1, rotation1,
                position2, rotation2,
                camOffsets, camRotations,
                config.num_points,
                config.noise,
                config.outlier_fraction,
                bearingVectors1, bearingVectors2,
                camCorrespondences1, camCorrespondences2,
                gt);

            // generateRandom2D2DPlanarCorrespondences(
            //     position1, rotation1,
            //     position2, rotation2,
            //     camOffsets, camRotations,
            //     config.num_points,
            //     config.noise,
            //     config.outlier_fraction,
            //     50.0,  // planeDistance
            //     100.0, // X_length
            //     100.0, // Y_length
            //     bearingVectors1, bearingVectors2,
            //     camCorrespondences1, camCorrespondences2,
            //     gt);
            // 创建视图对的匹配
            ViewPair view_pair(0, 1); // 第一个视图和第二个视图
            IdMatches id_matches;     // 该视图对的匹配
            id_matches.reserve(bearingVectors1.size());

            // 将bearing vectors转换为图像特征点并生成匹配
            for (size_t i = 0; i < bearingVectors1.size(); ++i)
            {
                // 归一化坐标
                Eigen::Vector2d norm_point1(bearingVectors1[i].x() / bearingVectors1[i].z(),
                                            bearingVectors1[i].y() / bearingVectors1[i].z());
                Eigen::Vector2d norm_point2(bearingVectors2[i].x() / bearingVectors2[i].z(),
                                            bearingVectors2[i].y() / bearingVectors2[i].z());

                // 投影到图像平面
                types::Feature feat1, feat2;
                feat1.x() = camera.intrinsics.fx * norm_point1.x() + camera.intrinsics.cx;
                feat1.y() = camera.intrinsics.fy * norm_point1.y() + camera.intrinsics.cy;
                feat2.x() = camera.intrinsics.fx * norm_point2.x() + camera.intrinsics.cx;
                feat2.y() = camera.intrinsics.fy * norm_point2.y() + camera.intrinsics.cy;

                // 添加到特征数据中
                alignas(16) types::FeaturePoint fp1(feat1);
                alignas(16) types::FeaturePoint fp2(feat2);
                (*features_info)[0].features.push_back(fp1);
                (*features_info)[1].features.push_back(fp2);

                // 添加匹配
                IdMatch match;
                match.i = i; // 第一个视图中的特征点索引
                match.j = i; // 第二个视图中的特征点索引
                id_matches.push_back(match);
            }

            // 将所有匹配添加到视图对中
            (*matches)[view_pair] = id_matches;

            // 设置所需数据
            method_->SetRequiredData(camera_model_data_);
            method_->SetRequiredData(features_data_);
            method_->SetRequiredData(matches_data_);
        }

        // 使用评估器系统运行单个估计器配置
        bool RunEstimatorWithEvaluator(const std::string &estimator_name,
                                       const std::string &algorithm,
                                       const SceneConfig &config,
                                       bool posdk_refine = false)
        {
            // 构建算法标识用于评估器
            std::string algorithm_name = estimator_name;
            if (!algorithm.empty())
            {
                algorithm_name += "_" + algorithm;
            }
            if (posdk_refine)
            {
                algorithm_name += "_posdk_refine";
            }

            // 设置方法选项
            MethodOptions options;
            options["estimator"] = estimator_name;
            options["algorithm"] = algorithm;
            options["posdk_refine"] = posdk_refine ? "true" : "false"; // 设置PoSDK精细优化选项

            options["enable_evaluator"] = "true";          // 启用评估器
            options["ProfileCommit"] = config.config_name; // 设置配置提交名称

            method_->SetMethodOptions(options);

            // 设置数据
            method_->SetRequiredData(camera_model_data_);
            method_->SetRequiredData(features_data_);
            method_->SetRequiredData(matches_data_);

            // 设置真值数据 - 修复：应该创建RelativePoses而不是单个RelativePose
            RelativePoses gt_poses;
            RelativePose gt_pose;
            gt_pose.i = 0;
            gt_pose.j = 1;
            gt_pose.Rij = ground_truth_R_;
            gt_pose.tij = ground_truth_t_;
            gt_pose.weight = 1.0;
            gt_poses.push_back(gt_pose);

            // 创建真值数据并转换为DataPtr - 修复：使用RelativePoses而不是RelativePose
            auto gt_data_map = std::make_shared<DataMap<RelativePoses>>(gt_poses);
            DataPtr gt_data = std::static_pointer_cast<DataIO>(gt_data_map);

            // 尝试转换为MethodPresetProfiler来设置真值数据
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(method_);
            if (profiler)
            {
                profiler->SetGTData(gt_data);
                // 重要：每次都重新设置算法名称，避免使用之前的缓存值
                // profiler->SetEvaluatorAlgorithm(algorithm_name);
            }
            else
            {
                // 如果不是MethodPresetProfiler，尝试直接设置GT数据
                method_->SetGTData(gt_data);
            }

            // 执行估计
            auto pose_data = method_->Build();

            return pose_data != nullptr;
        }

        // 显示评估结果和测试评估管理器功能
        void ShowEvaluationResults()
        {
            std::cout << "\n"
                      << std::string(60, '=') << std::endl;
            std::cout << "TWO VIEW ESTIMATOR EVALUATION RESULTS" << std::endl;
            std::cout << std::string(60, '=') << std::endl;

            // 1. 打印所有评估报告
            std::cout << "\n1. All Evaluation Reports:" << std::endl;
            Interface::EvaluatorManager::PrintAllEvaluationReports();

            // 2. 打印算法对比（如果有多个指标）
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
            for (const auto &eval_type : eval_types)
            {
                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                for (const auto &algorithm : algorithms)
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                        Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                    }
                }
            }

            // 3. 导出CSV文件测试
            std::cout << "\n3. Testing CSV Export Functions:" << std::endl;

            // 获取当前测试信息
            const ::testing::TestInfo *const test_info =
                ::testing::UnitTest::GetInstance()->current_test_info();
            std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                    std::string(test_info->name());

            // 添加时间戳确保唯一性
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);

            std::ostringstream timestamp;
            timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

            std::string unique_dir_name = test_name + "_" + timestamp.str();

            TestCSVExport(unique_dir_name);

            std::cout << std::string(60, '=') << std::endl;
        }

        // 测试CSV导出功能
        void TestCSVExport(const std::string &output_dir_name)
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 创建测试输出目录
                std::filesystem::path test_output_dir = output_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出功能（智能解析评估结果）===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 单统计类型导出
                        std::filesystem::path single_metric_path = test_output_dir / (eval_type + "_" + metric + "_comparison.csv");
                        bool single_success = Interface::EvaluatorManager::ExportAlgorithmComparisonToCSV(
                            eval_type, metric, single_metric_path, "mean");
                        std::cout << "Export single metric " << eval_type << "::" << metric << ": "
                                  << (single_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 所有统计类型导出
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的所有统计类型..." << std::endl;
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }

                // 导出原始评估值
                std::filesystem::path raw_values_dir = test_output_dir / "raw_values";
                bool raw_success = Interface::EvaluatorManager::ExportAllRawValuesToCSV(eval_type, raw_values_dir, "core_time");
                std::cout << "Export raw values for " << eval_type << ": "
                          << (raw_success ? "SUCCESS" : "FAILED") << std::endl;

                // ==================== 新增：测试时间统计导出功能 ====================
                std::cout << "\n=== 测试时间统计导出功能 ===" << std::endl;

                // 测试导出时间统计CSV和可视化
                std::filesystem::path time_stats_dir = test_output_dir / "time_statistics";
                std::vector<std::string> time_stat_types = {"Mean", "Median", "Min", "Max"};

                bool time_stats_with_viz_success = Interface::EvaluatorManager::ExportTimeStatisticsWithVisualization(
                    eval_type, time_stats_dir, time_stat_types);
                std::cout << "Export time statistics with visualization for " << eval_type << ": "
                          << (time_stats_with_viz_success ? "SUCCESS" : "FAILED") << std::endl;

                // 测试单独导出时间统计CSV
                std::filesystem::path time_stats_path = test_output_dir / (eval_type + "_time_statistics.csv");
                bool time_stats_success = Interface::EvaluatorManager::ExportTimeStatisticsToCSV(eval_type, time_stats_path);
                std::cout << "Export time statistics CSV for " << eval_type << ": "
                          << (time_stats_success ? "SUCCESS" : "FAILED") << std::endl;

                // 输出时间统计信息到控制台
                if (time_stats_success && std::filesystem::exists(time_stats_path))
                {
                    std::cout << "\n=== 算法运行时间统计 ===" << std::endl;
                    std::cout << std::string(80, '-') << std::endl;

                    // 简化显示：只显示前几行作为示例
                    std::ifstream time_file(time_stats_path);
                    if (time_file.is_open())
                    {
                        std::string line;
                        int line_count = 0;
                        while (std::getline(time_file, line) && line_count < 10)
                        {
                            std::cout << line << std::endl;
                            line_count++;
                        }

                        if (line_count >= 10)
                        {
                            std::cout << "... (省略其余内容，完整时间统计见CSV文件)" << std::endl;
                        }

                        time_file.close();
                    }
                    std::cout << std::string(80, '-') << std::endl;
                }

                // 显示时间统计图表路径
                if (time_stats_with_viz_success)
                {
                    std::cout << "\n时间统计图表保存在: " << time_stats_dir / "plots" << std::endl;
                }

                // ==================== 新增：测试可视化绘图功能 ====================
                std::cout << "\n=== 测试可视化绘图功能 ===" << std::endl;

                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[可视化] 为指标 " << metric << " 生成图表..." << std::endl;

                        // 方法1：直接从现有CSV生成图表
                        std::filesystem::path csv_file = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        if (std::filesystem::exists(csv_file))
                        {
                            std::filesystem::path plot_dir = test_output_dir / "plots";
                            std::vector<std::string> stat_types = {"Mean", "Median"};

                            bool plot_success = Interface::EvaluatorManager::GenerateMetricVisualization(
                                eval_type, metric, csv_file, plot_dir, stat_types);

                            std::cout << "Generate plots from existing CSV for " << eval_type << "::" << metric << ": "
                                      << (plot_success ? "SUCCESS" : "FAILED") << std::endl;
                        }

                        // 方法2：一键导出CSV并生成图表
                        std::filesystem::path combined_dir = test_output_dir / "combined_output";
                        std::vector<std::string> combined_stat_types = {"Mean", "Median", "Min", "Max"};

                        bool combined_success = Interface::EvaluatorManager::ExportMetricWithVisualization(
                            eval_type, metric, combined_dir, combined_stat_types);

                        std::cout << "Export CSV with visualization for " << eval_type << "::" << metric << ": "
                                  << (combined_success ? "SUCCESS" : "FAILED") << std::endl;
                    }

                    // 方法3：批量生成所有指标的图表
                    std::cout << "\n[可视化] 批量生成所有指标图表..." << std::endl;
                    std::filesystem::path batch_plot_dir = test_output_dir / "batch_plots";
                    std::vector<std::string> batch_stat_types = {"Mean", "Median"};

                    bool batch_success = Interface::EvaluatorManager::GenerateAllMetricsVisualization(
                        eval_type, test_output_dir, batch_plot_dir, batch_stat_types);

                    std::cout << "Batch generate all metrics visualization for " << eval_type << ": "
                              << (batch_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                std::cout << "\n=== 可视化绘图功能测试完成 ===" << std::endl;
                std::cout << "图表文件保存在以下目录:" << std::endl;
                std::cout << "  - " << test_output_dir / "plots" << std::endl;
                std::cout << "  - " << test_output_dir / "combined_output" / "plots" << std::endl;
                std::cout << "  - " << test_output_dir / "batch_plots" << std::endl;
            }
        }

        Interface::MethodPresetPtr method_;
        Interface::DataPtr camera_model_data_;
        Interface::DataPtr features_data_;
        Interface::DataPtr matches_data_;

        rotation_t ground_truth_R_;
        translation_t ground_truth_t_;

        std::vector<rotation_t> ground_truth_Rs_;
        std::vector<translation_t> ground_truth_ts_;
    };

    // // 外点鲁棒性测试 [使用评估器系统]
    // TEST_F(TestSimuTwoViewEstimator, OutlierTest)
    // {
    //     const int NUM_REPEATS = 20; // 重复测试次数，增加统计数据

    //     // 定义不同外点比例的测试场景
    //     std::vector<SceneConfig> test_configs = {
    //         {"outlier_10", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.1}, // 10%外点
    //         {"outlier_20", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.2}, // 20%外点
    //         {"outlier_30", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.3}, // 30%外点
    //         {"outlier_40", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.4}, // 40%外点
    //         {"outlier_50", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.5}, // 50%外点
    //         {"outlier_60", 2.0, 0.3, 300, static_cast<size_t>(1), 0.1, 0.6}  // 60%外点
    //     };

    //     // 定义要测试的估计器配置
    //     std::vector<std::pair<std::string, std::string>> estimator_configs = {
    //         {"method_LiRP", ""},               // LiRP方法不需要指定算法
    //         {"method_robustLiRP", "ransac"},   // 鲁棒LiRP方法
    //         {"method_robustLiRP", "gnc_irls"}, // 鲁棒LiRP方法
    //         {"method_povgSixPoint", ""},       // PoVG六点算法方法

    //         // OpenGV算法
    //         {"opengv_model_estimator", "fivept_stewenius"},
    //         {"opengv_model_estimator", "fivept_nister"},
    //         {"opengv_model_estimator", "sevenpt"},
    //         {"opengv_model_estimator", "eightpt"},

    //         // PoseLib算法 - RANSAC方法（适合外点测试）
    //         {"poselib_model_estimator", "relpose_5pt_ransac"},
    //         {"poselib_model_estimator", "relpose_7pt_ransac"},
    //         {"poselib_model_estimator", "relpose_8pt_ransac"},
    //         {"poselib_model_estimator", "relpose_upright_3pt_ransac"},

    //         // Barath双视图估计器 - MAGSAC/MAGSAC++算法
    //         {"barath_two_view_estimator", "MAGSAC++"},
    //     };

    //     std::cout << "\n=== 开始双视图估计器外点鲁棒性测试 ===" << std::endl;
    //     std::cout << "测试配置: " << test_configs.size() << " 个外点级别, "
    //               << estimator_configs.size() << " 个算法, "
    //               << NUM_REPEATS << " 次重复" << std::endl;

    //     // 对每个场景配置进行测试
    //     for (const auto &config : test_configs)
    //     {
    //         std::cout << "\n--- 测试外点比例: " << (config.outlier_fraction * 100) << "% (commit: " << config.config_name << ") ---" << std::endl;

    //         for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
    //         {
    //             std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

    //             // 生成场景数据
    //             GenerateScene(config);

    //             // 测试每个估计器配置
    //             for (const auto &[estimator, algorithm] : estimator_configs)
    //             {
    //                 std::string algorithm_display = algorithm.empty() ? "(default)" : algorithm;
    //                 std::cout << "  测试算法: " << estimator << " + " << algorithm_display;

    //                 bool success = RunEstimatorWithEvaluator(estimator, algorithm, config);

    //                 std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

    //                 if (!success)
    //                 {
    //                     std::cerr << "    警告: 算法 " << estimator << " + " << algorithm_display
    //                               << " 在配置 " << config.config_name << " 中失败" << std::endl;
    //                 }
    //             }
    //         }
    //     }

    //     std::cout << "\n=== 测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    // }

    // 噪声鲁棒性测试 [使用评估器系统]
    TEST_F(TestSimuTwoViewEstimator, NoiseTest)
    {
        const int NUM_REPEATS = 50; // 重复测试次数，增加统计数据
        const int num_pts = 10;
        // 定义不同噪声水平的测试场景
        // std::vector<SceneConfig> test_configs = {
        //     {"noise_0", 10.0, 0.2, num_pts, static_cast<size_t>(1), 0, 0.0},     // 无噪声
        //     {"noise_0.5", 10.0, 0.2, num_pts, static_cast<size_t>(1), 0.5, 0.0}, // 0.5px噪声
        //     {"noise_1", 10.0, 0.2, num_pts, static_cast<size_t>(1), 1.0, 0.0},   // 1px噪声
        //     {"noise_2", 10.0, 0.2, num_pts, static_cast<size_t>(1), 2.0, 0.0},   // 2px噪声
        //     {"noise_3", 10.0, 0.2, num_pts, static_cast<size_t>(1), 3.0, 0.0},   // 3px噪声
        //     {"noise_5", 10.0, 0.3, num_pts, static_cast<size_t>(1), 5.0, 0.0}    // 5px噪声
        // };

        std::vector<SceneConfig> test_configs = {
            {"noise_0", 2, 0.2, num_pts, static_cast<size_t>(1), 0, 0.0},     // 无噪声
            {"noise_0.5", 2, 0.2, num_pts, static_cast<size_t>(1), 0.5, 0.0}, // 0.5px噪声
            {"noise_1", 2, 0.2, num_pts, static_cast<size_t>(1), 1.0, 0.0},   // 1px噪声
            {"noise_2", 2, 0.2, num_pts, static_cast<size_t>(1), 2.0, 0.0},   // 2px噪声
            {"noise_3", 2, 0.2, num_pts, static_cast<size_t>(1), 3.0, 0.0},   // 3px噪声
            {"noise_5", 2, 0.3, num_pts, static_cast<size_t>(1), 5.0, 0.0}    // 5px噪声
        };

        // 定义估计器配置结构体
        struct EstimatorConfig
        {
            std::string estimator;
            std::string algorithm;
            bool posdk_refine;

            EstimatorConfig(const std::string &est, const std::string &alg, bool refine = false)
                : estimator(est), algorithm(alg), posdk_refine(refine) {}
        };

        // 定义要测试的估计器配置
        std::vector<EstimatorConfig> estimator_configs = {
            {"method_LiRP", "", false},                         // LiRP方法不需要指定算法
            {"method_LiRPFast", "", false},                     // LiRPFast极速版本方法
            {"method_LiRP", "", true},                          // LiRP方法+统一精细优化
            {"method_LiRPFast", "", true},                      // LiRPFast极速版本方法+统一精细优化
            {"method_robustLiRP", "", true},                    // robustLiRP方法+统一精细优化
            {"poselib_model_estimator", "", true},              // PoseLib估计器+统一精细优化
            {"method_povgSixPoint", "eigen_solve_main", false}, // PoVG六点算法方法
            {"method_povgSixPoint", "eigen_solve_main", true},  // PoVG六点算法方法+统一精细优化(应被忽略)
            // // OpenGV算法
            // {"opengv_model_estimator", "fivept_stewenius", false},
            // {"opengv_model_estimator", "fivept_nister", false},
            // {"opengv_model_estimator", "sevenpt", false},
            // {"opengv_model_estimator", "eightpt", false},

            // // PoseLib算法 - 直接方法（对噪声敏感）
            {"poselib_model_estimator", "relpose_5pt", false},
            {"poselib_model_estimator", "relpose_7pt", false},
            {"poselib_model_estimator", "relpose_8pt", false},
        };

        std::cout << "\n=== 开始双视图估计器噪声鲁棒性测试 ===" << std::endl;
        std::cout << "测试配置: " << test_configs.size() << " 个噪声级别, "
                  << estimator_configs.size() << " 个算法配置(包含PoSDK精细优化对比), "
                  << NUM_REPEATS << " 次重复" << std::endl;

        // 显示配置详情
        std::cout << "算法配置详情:" << std::endl;
        for (const auto &est_config : estimator_configs)
        {
            std::string algorithm_display = est_config.algorithm.empty() ? "(default)" : est_config.algorithm;
            std::string posdk_display = est_config.posdk_refine ? " + PoSDK精细优化" : "";
            std::cout << "  - " << est_config.estimator << " + " << algorithm_display << posdk_display << std::endl;
        }

        // 对每个场景配置进行测试
        for (const auto &config : test_configs)
        {
            std::cout << "\n--- 测试噪声级别: " << config.noise << " px (commit: " << config.config_name << ") ---" << std::endl;

            for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
            {
                std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

                // 生成场景数据
                GenerateScene(config);

                // 测试每个估计器配置
                for (const auto &est_config : estimator_configs)
                {
                    std::string algorithm_display = est_config.algorithm.empty() ? "(default)" : est_config.algorithm;
                    std::string posdk_display = est_config.posdk_refine ? "+posdk" : "";
                    std::cout << "  测试算法: " << est_config.estimator << " + " << algorithm_display << posdk_display;

                    bool success = RunEstimatorWithEvaluator(est_config.estimator, est_config.algorithm, config, est_config.posdk_refine);

                    std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

                    if (!success)
                    {
                        std::cerr << "    警告: 算法 " << est_config.estimator << " + " << algorithm_display << posdk_display
                                  << " 在配置 " << config.config_name << " 中失败" << std::endl;
                    }
                }
            }
        }

        std::cout << "\n=== 测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    }

    // // 大量视图对测试
    // TEST_F(TestSimuTwoViewEstimator, LargeMultiPairsTest)
    // {
    //     // 定义多视图对测试场景，10个视图对
    //     SceneConfig config{
    //         1.0,                     // max_parallax
    //         0.3,                     // max_rotation
    //         300,                     // num_points - 改为300
    //         static_cast<size_t>(10), // num_pairs (10对视图)
    //         0.0,                     // noise
    //         0.0                      // outlier_fraction
    //     };

    //     std::cout << "测试处理10个视图对的情况（每对300个特征点）..." << std::endl;

    //     // 生成场景数据
    //     GenerateSceneMultiPairs(config);

    //     // 设置方法选项 - 确保使用小写的残差类型
    //     MethodOptions options;
    //     options["estimator"] = "method_LiRP";
    //     options["debug_output"] = "true";
    //     options["enable_evaluator"] = "false"; // 测试环境下关闭评估器
    //     method_->SetMethodOptions(options);

    //     // 运行算法并计时
    //     auto start_time = std::chrono::high_resolution_clock::now();
    //     auto result = method_->Build();
    //     auto end_time = std::chrono::high_resolution_clock::now();
    //     auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
    //                         end_time - start_time)
    //                         .count() /
    //                     1000.0;

    //     // 验证结果
    //     ASSERT_TRUE(result != nullptr) << "处理10个视图对时方法未能生成结果";

    //     // 从DataPackage中获取相对位姿
    //     auto poses = GetDataPtr<RelativePoses>(result, "data_relative_poses");
    //     ASSERT_TRUE(poses != nullptr) << "未能从结果中获取相对位姿";
    //     ASSERT_FALSE(poses->empty()) << "没有估计出任何相对位姿";

    //     std::cout << "估计出的位姿数量: " << poses->size() << "，预期数量: " << config.num_pairs << std::endl;
    //     ASSERT_EQ(poses->size(), config.num_pairs)
    //         << "估计出的位姿数量与视图对数量不匹配";

    //     // 统计误差数据
    //     double total_R_error = 0.0;
    //     double total_t_error = 0.0;
    //     double max_R_error = 0.0;
    //     double max_t_error = 0.0;
    //     size_t pair_count = 0;

    //     // 验证每个相对位姿
    //     for (const auto &pose : *poses)
    //     {
    //         std::cout << "\n检查视图对 ("
    //                   << pose.i << ", " << pose.j << ") 的位姿:\n";

    //         // 获取对应的真值位姿
    //         size_t pair_idx = pose.i; // 假设视图对是按顺序的
    //         double R_error = ComputeRotationError(pose.Rij, ground_truth_Rs_[pair_idx]);
    //         double t_error = ComputeTranslationError(pose.tij, ground_truth_ts_[pair_idx]);

    //         // 更新统计数据
    //         total_R_error += R_error;
    //         total_t_error += t_error;
    //         max_R_error = std::max(max_R_error, R_error);
    //         max_t_error = std::max(max_t_error, t_error);
    //         pair_count++;

    //         // 输出结果
    //         std::cout << std::string(50, '-') << "\n"
    //                   << "旋转误差: " << R_error << "\n"
    //                   << "平移误差: " << t_error << "\n"
    //                   << "权重: " << pose.weight << "\n"
    //                   << std::string(50, '-') << std::endl;

    //         // 验证结果
    //         EXPECT_LT(R_error, 0.1) << "视图对 ("
    //                                 << pose.i << ", " << pose.j << ") 的旋转误差过大";
    //         EXPECT_LT(t_error, 0.1) << "视图对 ("
    //                                 << pose.i << ", " << pose.j << ") 的平移误差过大";
    //     }

    //     // 输出总体统计结果
    //     std::cout << "\n10个视图对测试总结 (每对300个特征点):\n"
    //               << std::string(50, '-') << "\n"
    //               << "平均旋转误差: " << (total_R_error / pair_count) << "\n"
    //               << "平均平移误差: " << (total_t_error / pair_count) << "\n"
    //               << "最大旋转误差: " << max_R_error << "\n"
    //               << "最大平移误差: " << max_t_error << "\n"
    //               << "总运行时间: " << duration << " ms\n"
    //               << "每对视图平均耗时: " << (duration / pair_count) << " ms\n"
    //               << std::string(50, '-') << std::endl;
    // }

    // // 测试鲁棒LiRP方法在TwoViewEstimator中的应用
    // TEST_F(TestSimuTwoViewEstimator, RobustLiRPTest)
    // {
    //     // 定义测试场景 - 中等难度，有噪声和外点
    //     SceneConfig config{
    //         1.0,                    // max_parallax
    //         0.3,                    // max_rotation
    //         300,                    // num_points - 改为300
    //         static_cast<size_t>(1), // num_pairs
    //         0.5,                    // noise (0.5像素噪声)
    //         0.2                     // outlier_fraction (20%外点)
    //     };

    //     std::cout << "\n测试鲁棒LiRP方法在TwoViewEstimator中的应用（300个特征点）..." << std::endl;
    //     std::cout << "场景配置: 噪声=" << config.noise << "像素, 外点比例="
    //               << (config.outlier_fraction * 100) << "%" << std::endl;

    //     // 生成场景数据
    //     GenerateScene(config);

    //     // 测试结果存储
    //     struct RobustMethodResult
    //     {
    //         std::string method_name;
    //         std::string robust_type;
    //         double rotation_error;
    //         double translation_error;
    //         double runtime_ms;
    //         bool success;
    //     };
    //     std::vector<RobustMethodResult> results;

    //     // 测试不同配置
    //     std::vector<std::pair<std::string, std::string>> test_configs = {
    //         {"method_LiRP", ""},              // 原始LiRP (无鲁棒估计)
    //         {"method_robustLiRP", "ransac"},  // 鲁棒LiRP + RANSAC
    //         {"method_robustLiRP", "gnc_irls"} // 鲁棒LiRP + GNC-IRLS
    //     };

    //     for (const auto &[estimator, robust_type] : test_configs)
    //     {
    //         // 设置方法选项
    //         MethodOptions options;
    //         options["estimator"] = estimator;
    //         options["debug_output"] = "true";
    //         options["log_level"] = "0";
    //         options["enable_profiling"] = "false";
    //         options["enable_evaluator"] = "false"; // 测试环境下关闭评估器

    //         // 设置鲁棒估计器类型
    //         if (!robust_type.empty())
    //         {
    //             options["robust_type"] = robust_type;
    //         }

    //         method_->SetMethodOptions(options);

    //         // 运行算法并计时
    //         auto start_time = std::chrono::high_resolution_clock::now();
    //         auto result = method_->Build();
    //         auto end_time = std::chrono::high_resolution_clock::now();
    //         auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
    //                             end_time - start_time)
    //                             .count() /
    //                         1000.0;

    //         // 记录结果
    //         RobustMethodResult method_result;
    //         method_result.method_name = estimator;
    //         method_result.robust_type = robust_type;
    //         method_result.runtime_ms = duration;
    //         method_result.success = false;

    //         // 验证结果
    //         if (result)
    //         {
    //             // 从DataPackage中获取相对位姿
    //             auto poses = GetDataPtr<RelativePoses>(result, "data_relative_poses");
    //             if (poses && !poses->empty())
    //             {
    //                 const auto &pose = poses->front();
    //                 method_result.rotation_error = ComputeRotationError(pose.Rij, ground_truth_R_);
    //                 method_result.translation_error = ComputeTranslationError(pose.tij, ground_truth_t_);
    //                 method_result.success = true;
    //             }
    //         }

    //         results.push_back(method_result);
    //     }

    //     // 输出比较结果
    //     std::cout << "\n鲁棒LiRP测试结果比较 (300个特征点):\n";
    //     std::cout << std::string(80, '-') << std::endl;
    //     std::cout << std::left
    //               << std::setw(25) << "方法"
    //               << std::setw(25) << "鲁棒类型"
    //               << std::right
    //               << std::setw(25) << "旋转误差"
    //               << std::setw(25) << "平移误差"
    //               << std::setw(20) << "耗时(ms)"
    //               << std::endl;
    //     std::cout << std::string(80, '-') << std::endl;

    //     for (const auto &res : results)
    //     {
    //         if (res.success)
    //         {
    //             std::cout << std::left
    //                       << std::setw(25) << res.method_name
    //                       << std::setw(15) << res.robust_type
    //                       << std::right
    //                       << std::fixed << std::setprecision(6)
    //                       << std::setw(15) << res.rotation_error
    //                       << std::setw(15) << res.translation_error
    //                       << std::setw(10) << std::setprecision(2) << res.runtime_ms
    //                       << std::endl;
    //         }
    //         else
    //         {
    //             std::cout << std::left
    //                       << std::setw(25) << res.method_name
    //                       << std::setw(15) << res.robust_type
    //                       << std::right
    //                       << std::setw(15) << "失败"
    //                       << std::setw(15) << "失败"
    //                       << std::setw(10) << std::setprecision(2) << res.runtime_ms
    //                       << std::endl;
    //         }
    //     }
    //     std::cout << std::string(80, '-') << std::endl;

    //     // 验证鲁棒方法比标准方法有更好的结果
    //     if (results.size() >= 3 && results[0].success && results[1].success && results[2].success)
    //     {
    //         // 比较旋转误差
    //         EXPECT_LE(results[1].rotation_error, results[0].rotation_error * 1.2)
    //             << "RANSAC鲁棒LiRP的旋转误差应该不明显大于标准LiRP";
    //         EXPECT_LE(results[2].rotation_error, results[0].rotation_error * 1.2)
    //             << "GNC-IRLS鲁棒LiRP的旋转误差应该不明显大于标准LiRP";

    //         // 比较平移误差
    //         EXPECT_LE(results[1].translation_error, results[0].translation_error * 1.2)
    //             << "RANSAC鲁棒LiRP的平移误差应该不明显大于标准LiRP";
    //         EXPECT_LE(results[2].translation_error, results[0].translation_error * 1.2)
    //             << "GNC-IRLS鲁棒LiRP的平移误差应该不明显大于标准LiRP";
    //     }
    // }

    // // 测试内点信息处理的正确性
    // TEST_F(TestSimuTwoViewEstimator, InlierFlagsUpdateTest)
    // {
    //     // 定义测试场景 - 有噪声和外点
    //     SceneConfig config{
    //         1.0,                    // max_parallax
    //         0.3,                    // max_rotation
    //         300,                    // num_points - 改为300
    //         static_cast<size_t>(1), // num_pairs
    //         0.5,                    // noise (0.5像素噪声)
    //         0.3                     // outlier_fraction (30%外点)
    //     };

    //     std::cout << "\n测试内点信息处理的正确性（300个特征点）..." << std::endl;
    //     std::cout << "场景配置: 噪声=" << config.noise << "像素, 外点比例="
    //               << (config.outlier_fraction * 100) << "%" << std::endl;

    //     // 生成场景数据
    //     GenerateScene(config);

    //     // 获取原始匹配数据以便后续验证
    //     auto matches_data = GetDataPtr<Matches>(matches_data_);
    //     ASSERT_TRUE(matches_data != nullptr);
    //     ASSERT_FALSE(matches_data->empty());

    //     // 获取第一个视图对的匹配
    //     auto &first_pair_matches = matches_data->begin()->second;
    //     size_t total_matches = first_pair_matches.size();

    //     // 记录处理前的内点状态（应该全部为true，因为是从生成的数据）
    //     size_t initial_inliers = 0;
    //     for (const auto &match : first_pair_matches)
    //     {
    //         if (match.is_inlier)
    //             initial_inliers++;
    //     }

    //     std::cout << "处理前内点数量: " << initial_inliers << "/" << total_matches << std::endl;

    //     // 测试不同的鲁棒方法
    //     std::vector<std::pair<std::string, std::string>> test_configs = {
    //         {"method_robustLiRP", "ransac"},  // 鲁棒LiRP + RANSAC
    //         {"method_robustLiRP", "gnc_irls"} // 鲁棒LiRP + GNC-IRLS
    //     };

    //     for (const auto &[estimator, robust_type] : test_configs)
    //     {
    //         std::cout << "\n测试 " << estimator << " + " << robust_type << "..." << std::endl;

    //         // 重置匹配数据的内点标志为全部true（模拟初始状态）
    //         for (auto &match : first_pair_matches)
    //         {
    //             match.is_inlier = true;
    //         }

    //         // 设置方法选项
    //         MethodOptions options;
    //         options["estimator"] = estimator;
    //         options["robust_type"] = robust_type;
    //         options["debug_output"] = "true";
    //         options["log_level"] = "1";
    //         options["enable_evaluator"] = "false"; // 测试环境下关闭评估器

    //         // 设置鲁棒估计器参数
    //         if (robust_type == "ransac")
    //         {
    //             options["ransac_max_iterations"] = "500";
    //             options["ransac_inlier_threshold"] = "1e-3";
    //         }
    //         else if (robust_type == "gnc_irls")
    //         {
    //             options["gnc_max_iterations"] = "15";
    //             options["gnc_noise_scale"] = "2.5";
    //         }

    //         method_->SetMethodOptions(options);

    //         // 运行算法
    //         auto result = method_->Build();
    //         ASSERT_TRUE(result != nullptr) << "算法执行失败: " << estimator << " + " << robust_type;

    //         // 验证位姿结果
    //         auto poses = GetDataPtr<RelativePoses>(result, "data_relative_poses");
    //         ASSERT_TRUE(poses != nullptr && !poses->empty())
    //             << "未能获取位姿结果: " << estimator << " + " << robust_type;

    //         // 从输出DataPackage中获取更新后的匹配数据
    //         auto updated_matches = GetDataPtr<Matches>(result, "data_matches");
    //         ASSERT_TRUE(updated_matches != nullptr) << "未能从输出中获取更新的匹配数据";

    //         // 验证输出的匹配数据与输入的指针一致（应该是同一个对象）
    //         EXPECT_EQ(updated_matches.get(), matches_data.get())
    //             << "输出的匹配数据应该是输入数据的同一个对象";

    //         // 验证内点信息是否被正确更新
    //         size_t final_inliers = 0;
    //         for (const auto &match : first_pair_matches)
    //         {
    //             if (match.is_inlier)
    //                 final_inliers++;
    //         }

    //         std::cout << "处理后内点数量: " << final_inliers << "/" << total_matches << std::endl;

    //         // 验证内点数量的合理性
    //         EXPECT_GT(final_inliers, 0) << "没有检测到任何内点";
    //         EXPECT_LT(final_inliers, total_matches) << "所有点都被标记为内点，可能没有正确处理外点";

    //         // 对于有30%外点的场景，期望内点比例在40%-80%之间
    //         double inlier_ratio = static_cast<double>(final_inliers) / total_matches;
    //         EXPECT_GE(inlier_ratio, 0.4) << "内点比例过低: " << (inlier_ratio * 100) << "%";
    //         EXPECT_LE(inlier_ratio, 0.8) << "内点比例过高: " << (inlier_ratio * 100) << "%";

    //         std::cout << "内点比例: " << std::fixed << std::setprecision(1)
    //                   << (inlier_ratio * 100) << "%" << std::endl;

    //         // 验证位姿精度
    //         const auto &pose = poses->front();
    //         double R_error = ComputeRotationError(pose.Rij, ground_truth_R_);
    //         double t_error = ComputeTranslationError(pose.tij, ground_truth_t_);

    //         std::cout << "位姿误差 - 旋转: " << R_error << ", 平移: " << t_error << std::endl;

    //         // 在有噪声和外点的情况下，允许较大的误差
    //         EXPECT_LT(R_error, 0.5) << "旋转误差过大";
    //         EXPECT_LT(t_error, 0.5) << "平移误差过大";
    //     }

    //     std::cout << "\n内点信息处理测试完成（300个特征点）" << std::endl;
    // }

    // // 测试OpenCV双视图估计器在TwoViewEstimator中的应用
    // TEST_F(TestSimuTwoViewEstimator, OpenCVEstimatorTest)
    // {
    //     // 定义测试场景 - 中等难度，有噪声和外点
    //     SceneConfig config{
    //         1.0,                    // max_parallax
    //         0.3,                    // max_rotation
    //         300,                    // num_points
    //         static_cast<size_t>(1), // num_pairs
    //         0.5,                    // noise (0.5像素噪声)
    //         0.1                     // outlier_fraction (10%外点)
    //     };

    //     std::cout << "\n测试OpenCV双视图估计器在TwoViewEstimator中的应用（300个特征点）..." << std::endl;
    //     std::cout << "场景配置: 噪声=" << config.noise << "像素, 外点比例="
    //               << (config.outlier_fraction * 100) << "%" << std::endl;

    //     // 生成场景数据
    //     GenerateScene(config);

    //     // 测试结果存储
    //     struct OpenCVMethodResult
    //     {
    //         std::string algorithm;
    //         double rotation_error;
    //         double translation_error;
    //         double runtime_ms;
    //         bool success;
    //     };
    //     std::vector<OpenCVMethodResult> results;

    //     // 测试不同的OpenCV算法
    //     std::vector<std::string> opencv_algorithms = {
    //         "ESSENTIAL_RANSAC",
    //         "ESSENTIAL_LMEDS",
    //         "ESSENTIAL_USAC_DEFAULT",
    //         "ESSENTIAL_USAC_MAGSAC",
    //         "FUNDAMENTAL_RANSAC",
    //         "FUNDAMENTAL_8POINT",
    //         "HOMOGRAPHY_RANSAC"};

    //     for (const std::string &algorithm : opencv_algorithms)
    //     {
    //         // 设置方法选项
    //         MethodOptions options;
    //         options["estimator"] = "opencv_two_view_estimator";
    //         options["algorithm"] = algorithm;
    //         options["debug_output"] = "true";
    //         options["log_level"] = "0";
    //         options["enable_profiling"] = "false";
    //         options["enable_evaluator"] = "false"; // 测试环境下关闭评估器

    //         // 设置OpenCV特定参数
    //         options["ransac_threshold"] = "1.0";
    //         options["confidence"] = "0.999";
    //         options["max_iterations"] = "2000";

    //         method_->SetMethodOptions(options);

    //         // 运行算法并计时
    //         auto start_time = std::chrono::high_resolution_clock::now();
    //         auto result = method_->Build();
    //         auto end_time = std::chrono::high_resolution_clock::now();
    //         auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
    //                             end_time - start_time)
    //                             .count() /
    //                         1000.0;

    //         // 记录结果
    //         OpenCVMethodResult method_result;
    //         method_result.algorithm = algorithm;
    //         method_result.runtime_ms = duration;
    //         method_result.success = false;

    //         // 验证结果
    //         if (result)
    //         {
    //             // 从DataPackage中获取相对位姿
    //             auto poses = GetDataPtr<RelativePoses>(result, "data_relative_poses");
    //             if (poses && !poses->empty())
    //             {
    //                 const auto &pose = poses->front();
    //                 method_result.rotation_error = ComputeRotationError(pose.Rij, ground_truth_R_);
    //                 method_result.translation_error = ComputeTranslationError(pose.tij, ground_truth_t_);
    //                 method_result.success = true;
    //             }
    //         }

    //         results.push_back(method_result);
    //     }

    //     // 输出比较结果
    //     std::cout << "\nOpenCV双视图估计器测试结果 (300个特征点):\n";
    //     std::cout << std::string(80, '-') << std::endl;
    //     std::cout << std::left
    //               << std::setw(25) << "算法"
    //               << std::right
    //               << std::setw(15) << "旋转误差"
    //               << std::setw(15) << "平移误差"
    //               << std::setw(15) << "耗时(ms)"
    //               << std::setw(10) << "状态"
    //               << std::endl;
    //     std::cout << std::string(80, '-') << std::endl;

    //     size_t successful_count = 0;
    //     for (const auto &res : results)
    //     {
    //         if (res.success)
    //         {
    //             std::cout << std::left
    //                       << std::setw(25) << res.algorithm
    //                       << std::right
    //                       << std::fixed << std::setprecision(6)
    //                       << std::setw(15) << res.rotation_error
    //                       << std::setw(15) << res.translation_error
    //                       << std::setw(15) << std::setprecision(2) << res.runtime_ms
    //                       << std::setw(10) << "成功"
    //                       << std::endl;
    //             successful_count++;
    //         }
    //         else
    //         {
    //             std::cout << std::left
    //                       << std::setw(25) << res.algorithm
    //                       << std::right
    //                       << std::setw(15) << "失败"
    //                       << std::setw(15) << "失败"
    //                       << std::setw(15) << std::setprecision(2) << res.runtime_ms
    //                       << std::setw(10) << "失败"
    //                       << std::endl;
    //         }
    //     }
    //     std::cout << std::string(80, '-') << std::endl;

    //     std::cout << "成功算法数量: " << successful_count << "/" << results.size() << std::endl;

    //     // 验证至少有一些算法成功
    //     EXPECT_GT(successful_count, 0) << "至少应有一个OpenCV算法成功";

    //     // 对于成功的算法，验证误差在合理范围内
    //     for (const auto &res : results)
    //     {
    //         if (res.success)
    //         {
    //             // 在有噪声和外点的情况下，允许较大的误差
    //             EXPECT_LT(res.rotation_error, 1.0) << "算法 " << res.algorithm << " 的旋转误差过大";
    //             EXPECT_LT(res.translation_error, 1.0) << "算法 " << res.algorithm << " 的平移误差过大";
    //         }
    //     }

    //     std::cout << "\nOpenCV双视图估计器测试完成（300个特征点）" << std::endl;
    // }

    // // 测试TwoViewEstimator的DataPackage输出格式
    // TEST_F(TestSimuTwoViewEstimator, DataPackageOutputTest)
    // {
    //     // 定义简单的测试场景
    //     SceneConfig config{
    //         1.0,                    // max_parallax
    //         0.3,                    // max_rotation
    //         300,                    // num_points
    //         static_cast<size_t>(1), // num_pairs
    //         0.0,                    // noise
    //         0.0                     // outlier_fraction
    //     };

    //     std::cout << "\n测试TwoViewEstimator的DataPackage输出格式..." << std::endl;

    //     // 生成场景数据
    //     GenerateScene(config);

    //     // 设置方法选项
    //     MethodOptions options;
    //     options["estimator"] = "method_LiRP";
    //     options["debug_output"] = "true";
    //     options["enable_evaluator"] = "false"; // 测试环境下关闭评估器
    //     method_->SetMethodOptions(options);

    //     // 记录输入数据的指针，用于后续验证
    //     auto input_matches = GetDataPtr<Matches>(matches_data_);
    //     auto input_features = GetDataPtr<FeaturesInfo>(features_data_);
    //     auto input_cameras = GetDataPtr<CameraModels>(camera_model_data_);

    //     // 运行算法
    //     auto result = method_->Build();
    //     ASSERT_TRUE(result != nullptr) << "TwoViewEstimator执行失败";

    //     // 验证输出是DataPackage类型
    //     EXPECT_EQ(result->GetType(), "data_package") << "输出应该是DataPackage类型";

    //     auto package = std::dynamic_pointer_cast<DataPackage>(result);
    //     ASSERT_TRUE(package != nullptr) << "无法转换为DataPackage";

    //     // 获取DataPackage中的内容
    //     const auto &package_contents = package->GetPackage();

    //     // 验证包含正确的数据类型
    //     EXPECT_TRUE(package_contents.find("data_relative_poses") != package_contents.end())
    //         << "DataPackage应该包含data_relative_poses";
    //     EXPECT_TRUE(package_contents.find("data_matches") != package_contents.end())
    //         << "DataPackage应该包含data_matches";

    //     std::cout << "DataPackage包含的数据类型:" << std::endl;
    //     for (const auto &[key, value] : package_contents)
    //     {
    //         std::cout << "  - " << key << " (type: " << value->GetType() << ")" << std::endl;
    //     }

    //     // 验证相对位姿数据
    //     auto output_poses = GetDataPtr<RelativePoses>(result, "data_relative_poses");
    //     ASSERT_TRUE(output_poses != nullptr) << "无法获取输出的相对位姿";
    //     EXPECT_FALSE(output_poses->empty()) << "输出的相对位姿为空";
    //     std::cout << "输出相对位姿数量: " << output_poses->size() << std::endl;

    //     // 验证匹配数据（应该是同一个对象）
    //     auto output_matches = GetDataPtr<Matches>(result, "data_matches");
    //     ASSERT_TRUE(output_matches != nullptr) << "无法获取输出的匹配数据";
    //     EXPECT_EQ(output_matches.get(), input_matches.get())
    //         << "输出的匹配数据应该与输入数据是同一个对象";
    //     std::cout << "输出匹配数据验证: 与输入数据是同一对象 ✓" << std::endl;

    //     // 验证位姿精度
    //     const auto &pose = output_poses->front();
    //     double R_error = ComputeRotationError(pose.Rij, ground_truth_R_);
    //     double t_error = ComputeTranslationError(pose.tij, ground_truth_t_);

    //     std::cout << "位姿精度 - 旋转误差: " << R_error << ", 平移误差: " << t_error << std::endl;
    //     EXPECT_LT(R_error, 0.1) << "旋转误差过大";
    //     EXPECT_LT(t_error, 0.1) << "平移误差过大";

    //     std::cout << "DataPackage输出格式测试完成 ✓" << std::endl;
    // }

    // // 基于评估器系统的测试
    // TEST_F(TestSimuTwoViewEstimator, EvaluatorBasedTest)
    // {
    //     const int NUM_REPEATS = 1; // 重复测试次数，增加统计显著性

    //     // 清空之前的评估数据
    //     Interface::EvaluatorManager::ClearEvaluator("RelativePoses");

    //     // 定义不同噪声水平的测试场景
    //     std::vector<SceneConfig> test_configs = {
    //         // max_parallax | max_rotation | num_points | num_pairs | noise      | outlier
    //         {2.0, 0.3, 300, static_cast<size_t>(1), 0.0, 0.0}, // 无噪声
    //         {2.0, 0.3, 300, static_cast<size_t>(1), 0.5, 0.0}, // 中等噪声(0.5px)
    //         {2.0, 0.3, 300, static_cast<size_t>(1), 1.0, 0.0}, // 较大噪声(1.0px)
    //         {2.0, 0.3, 300, static_cast<size_t>(1), 2.0, 0.0}  // 极端噪声(2.0px)
    //     };

    //     // 定义要测试的估计器配置
    //     std::vector<std::pair<std::string, std::string>> estimator_configs = {
    //         {"method_LiRP", ""}, // LiRP方法不需要指定算法
    //         {"opengv_model_estimator", "fivept_stewenius"},
    //         {"opengv_model_estimator", "fivept_nister"},
    //         {"opengv_model_estimator", "sevenpt"},
    //         {"opengv_model_estimator", "eightpt"},

    //         // OpenCV基础矩阵算法
    //         {"opencv_two_view_estimator", "FUNDAMENTAL_7POINT"},
    //         {"opencv_two_view_estimator", "FUNDAMENTAL_8POINT"},
    //         {"opencv_two_view_estimator", "FUNDAMENTAL_RANSAC"},
    //         {"opencv_two_view_estimator", "FUNDAMENTAL_LMEDS"},
    //         {"opencv_two_view_estimator", "FUNDAMENTAL_RHO"},

    //         // OpenCV本质矩阵算法
    //         {"opencv_two_view_estimator", "ESSENTIAL_5POINT"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_RANSAC"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_LMEDS"},

    //         // OpenCV USAC高级算法
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_DEFAULT"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_PARALLEL"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_FM_8PTS"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_FAST"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_ACCURATE"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_PROSAC"},
    //         {"opencv_two_view_estimator", "ESSENTIAL_USAC_MAGSAC"}};

    //     std::cout << "\n=== 开始基于评估器的双视图估计器测试 ===" << std::endl;
    //     std::cout << "测试配置: " << test_configs.size() << " 个噪声级别, "
    //               << estimator_configs.size() << " 个算法, "
    //               << NUM_REPEATS << " 次重复" << std::endl;

    //     // 准备真值数据
    //     auto gt_relative_poses = std::make_shared<RelativePoses>();
    //     RelativePose gt_pose;
    //     gt_pose.i = 0;
    //     gt_pose.j = 1;
    //     gt_pose.Rij = ground_truth_R_;
    //     gt_pose.tij = ground_truth_t_;
    //     gt_relative_poses->push_back(gt_pose);

    //     auto gt_relative_poses_datamap = std::make_shared<DataMap<RelativePoses>>(*gt_relative_poses);
    //     DataPtr gt_relative_poses_data = std::static_pointer_cast<DataIO>(gt_relative_poses_datamap);

    //     // 对每个场景配置进行测试
    //     for (const auto &config : test_configs)
    //     {
    //         std::string noise_commit = "noise_" + std::to_string(config.noise);
    //         std::cout << "\n--- 测试噪声级别: " << config.noise << " px ---" << std::endl;

    //         for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
    //         {
    //             std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

    //             // 生成场景数据
    //             GenerateScene(config);

    //             // 测试每个估计器配置
    //             for (const auto &[estimator, algorithm] : estimator_configs)
    //             {
    //                 std::string algorithm_name = estimator;
    //                 if (!algorithm.empty())
    //                 {
    //                     algorithm_name += "_" + algorithm;
    //                 }

    //                 std::cout << "  测试算法: " << algorithm_name << std::endl;

    //                 // 设置方法选项
    //                 MethodOptions options;
    //                 options["estimator"] = estimator;
    //                 if (!algorithm.empty())
    //                 {
    //                     options["algorithm"] = algorithm;
    //                 }

    //                 // 启用评估器并设置ProfileCommit
    //                 options["enable_evaluator"] = "true";
    //                 options["ProfileCommit"] = noise_commit;
    //                 options["debug_output"] = "false"; // 减少输出

    //                 // 为OpenCV算法设置特定参数
    //                 if (estimator == "opencv_two_view_estimator")
    //                 {
    //                     options["ransac_threshold"] = "1.0";
    //                     options["confidence"] = "0.999";
    //                     options["max_iterations"] = "2000";
    //                 }

    //                 method_->SetMethodOptions(options);
    //                 method_->SetGTData(gt_relative_poses_data);

    //                 // 运行算法
    //                 auto start_time = std::chrono::high_resolution_clock::now();
    //                 auto result = method_->Build();
    //                 auto end_time = std::chrono::high_resolution_clock::now();
    //                 auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
    //                                     end_time - start_time)
    //                                     .count() /
    //                                 1000.0;

    //                 if (result)
    //                 {
    //                     std::cout << "    成功，耗时: " << std::fixed << std::setprecision(2)
    //                               << duration << " ms" << std::endl;
    //                 }
    //                 else
    //                 {
    //                     std::cout << "    失败" << std::endl;
    //                 }
    //             }
    //         }
    //     }

    //     std::cout << "\n=== 导出评估结果到CSV文件 ===" << std::endl;

    //     // 创建输出目录
    //     std::filesystem::create_directories("test_results");

    //     // 导出所有指标的详细统计
    //     Interface::EvaluatorManager::ExportAllMetricsToCSV("RelativePoses", "test_results");

    //     // 导出原始评估值（包含备注信息）
    //     Interface::EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", "test_results", "view_pairs");

    //     // 为每个指标导出统一的所有统计类型表格
    //     auto metrics = {"rotation_error", "translation_error", "runtime_ms"};
    //     for (const auto &metric : metrics)
    //     {
    //         std::string output_file = "test_results/RelativePoses_" + std::string(metric) + "_all_stats.csv";
    //         Interface::EvaluatorManager::ExportMetricAllStatsToCSV("RelativePoses", metric, output_file);
    //     }

    //     // 打印控制台报告
    //     std::cout << "\n=== 控制台评估报告 ===" << std::endl;
    //     Interface::EvaluatorManager::PrintEvaluationReport("RelativePoses");

    //     // 打印算法对比（以旋转误差为例）
    //     std::cout << "\n=== 旋转误差算法对比 ===" << std::endl;
    //     Interface::EvaluatorManager::PrintAlgorithmComparison("RelativePoses", "rotation_error");

    //     std::cout << "\n=== 测试完成 ===" << std::endl;
    //     std::cout << "详细结果已导出到 test_results/ 目录" << std::endl;
    // }

} // namespace
