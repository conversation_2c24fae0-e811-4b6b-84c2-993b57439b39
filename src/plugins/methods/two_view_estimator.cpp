/**
 * @file two_view_estimator.cpp
 * @brief 双视图位姿估计器实现
 */

#include "two_view_estimator.hpp"
#include <boost/algorithm/string.hpp>
#include <po_core.hpp>

namespace PluginMethods
{
    using namespace PoSDK;
    using namespace Interface;
    using namespace types;

    TwoViewEstimator::TwoViewEstimator()
    {
        // 注册所需数据类型
        required_package_["data_matches"] = nullptr;
        required_package_["data_features"] = nullptr;
        required_package_["data_camera_models"] = nullptr;

        // 初始化默认配置
        InitializeDefaultConfigPath();
    }

    DataPtr TwoViewEstimator::Run()
    {
        // 1. 获取输入数据
        auto matches_ptr = GetDataPtr<Matches>(required_package_["data_matches"]);
        auto features_ptr = GetDataPtr<FeaturesInfo>(required_package_["data_features"]);
        auto cameras_ptr = GetDataPtr<CameraModels>(required_package_["data_camera_models"]);

        if (!matches_ptr || !features_ptr || !cameras_ptr)
        {
            PO_LOG_ERR << "Invalid input data" << std::endl;
            return nullptr;
        }

        // 2. 获取算法类型
        std::string estimator = GetOptionAsString("estimator", "method_LiRP");

        // 3. 创建结果容器
        auto data_relative_poses = FactoryData::Create("data_relative_poses");
        auto poses = GetDataPtr<RelativePoses>(data_relative_poses);
        if (!poses)
        {
            PO_LOG_ERR << "Failed to create relative poses container" << std::endl;
            return nullptr;
        }

        // 4. 创建方法
        auto method = std::dynamic_pointer_cast<Interface::MethodPreset>(FactoryMethod::Create(estimator.c_str()));
        if (!method)
        {
            PO_LOG_ERR << "Failed to create method: " << estimator << std::endl;
            return nullptr;
        }
        current_method_ = method; // 保存当前方法实例
        PO_LOG(PO_LOG_NORMAL) << "Method created successfully: " << estimator << std::endl;

        // 5. 为支持评估器的方法设置算法名称
        std::string algorithm = GetOptionAsString("algorithm", "");
        bool enable_refine = GetOptionAsBool("enable_refine", false);

        // 构造完整的算法名称：estimator + algorithm + enable_refine
        std::string full_algorithm_name = estimator;
        if (!algorithm.empty())
        {
            full_algorithm_name += "_" + algorithm;
        }
        if (enable_refine)
        {
            full_algorithm_name += "_refine";
        }

        SetEvaluatorAlgorithm(full_algorithm_name);
        method->SetEvaluatorAlgorithm(full_algorithm_name);
        PO_LOG(PO_LOG_VERBOSE) << "Set evaluator algorithm name: " << full_algorithm_name << std::endl;

        // 统计变量
        size_t total_view_pairs = matches_ptr->size();
        size_t processed_pairs = 0;
        size_t successful_pairs = 0;
        size_t empty_matches = 0;
        size_t invalid_view_ids = 0;
        size_t insufficient_inliers = 0;
        size_t insufficient_pairs = 0; // 新增：匹配对数量不足的统计
        size_t conversion_failures = 0;
        size_t method_failures = 0;
        size_t invalid_poses = 0;

        // 获取最小匹配对数量要求
        int min_num_required_pairs = GetOptionAsIndexT("min_num_required_pairs", 50);
        PO_LOG(PO_LOG_NORMAL) << "[TwoViewEstimator] Minimum required pairs: " << min_num_required_pairs << std::endl;

        // 5. 处理每个视图对
        for (auto &[view_pair, matches] : *matches_ptr)
        {
            processed_pairs++;

            if (log_level_ >= PO_LOG_NONE) // 使用更详细的日志级别
            {
                PO_LOG(PO_LOG_VERBOSE) << "Processing view pair (" << view_pair.first << "," << view_pair.second << "): " << processed_pairs << "/" << total_view_pairs << std::endl;
            }

            // 预先验证视图对和匹配数据
            if (matches.empty())
            {
                PO_LOG(PO_LOG_VERBOSE) << "Warning: Empty matches for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                empty_matches++;
                continue;
            }

            // 检查匹配对数量是否满足最小要求
            if (static_cast<int>(matches.size()) < min_num_required_pairs)
            {
                PO_LOG(PO_LOG_VERBOSE) << "Warning: Insufficient match pairs (" << matches.size()
                                       << " < " << min_num_required_pairs << ") for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;

                // 将所有匹配对的is_inlier标志设置为false
                for (auto &match : matches)
                {
                    match.is_inlier = false;
                }

                insufficient_pairs++;
                continue;
            }

            // 验证view_id是否在有效范围内
            if (view_pair.first >= features_ptr->size() || view_pair.second >= features_ptr->size())
            {
                PO_LOG_ERR << "Invalid view_pair (" << view_pair.first << "," << view_pair.second
                           << ") - exceeds features size " << features_ptr->size() << std::endl;
                invalid_view_ids++;
                continue;
            }

            // 转换为射线向量
            BearingPairs bearing_pairs;
            if (!MatchesToBearingPairs(matches, *features_ptr, *cameras_ptr, view_pair, bearing_pairs))
            {
                PO_LOG(PO_LOG_VERBOSE) << "Failed to convert matches to bearing pairs for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                conversion_failures++;
                continue;
            }

            // 根据算法类型准备数据
            if (boost::iequals(estimator, "method_LiRP"))
            {
                // 准备BearingPairs数据
                auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(sample_data);
            }
            else if (boost::iequals(estimator, "method_LiRPFast"))
            {
                // 准备BearingPairs数据 - 与LiRP相同的数据格式
                auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(sample_data);
            }
            else if (boost::iequals(estimator, "method_robustLiRP"))
            {
                // 准备BearingPairs数据
                auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

                // 设置方法选项
                MethodOptions options;

                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(sample_data);
            }
            else if (boost::iequals(estimator, "method_povgSixPoint"))
            {
                // 准备BearingPairs数据
                auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(sample_data);
            }
            else if (boost::iequals(estimator, "opengv_model_estimator"))
            {
                // 🔧 修复：创建指向原始matches的shared_ptr，避免数据拷贝
                // 使用shared_ptr的aliasing constructor，与matches_ptr共享所有权
                auto matches_shared = std::shared_ptr<IdMatches>(matches_ptr, &matches);
                auto matches_data = std::make_shared<DataSample<IdMatches>>(matches_shared);

                // 设置方法选项
                MethodOptions options;
                if (view_pair.first > 0)
                {
                    int a = 0;
                    std::cout << "view_pair.first: " << view_pair.first << std::endl;
                }
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                // 重要：从 two_view_estimator 的选项中获取算法参数并传递
                std::string algorithm = GetOptionAsString("algorithm", "");
                if (!algorithm.empty())
                {
                    options["algorithm"] = algorithm;
                }

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(matches_data);
                method->SetRequiredData(required_package_["data_features"]);
                method->SetRequiredData(required_package_["data_camera_models"]);
            }
            else if (boost::iequals(estimator, "opencv_two_view_estimator"))
            {
                // OpenCV双视图估计器 - 使用与opengv相同的数据格式
                auto matches_shared = std::shared_ptr<IdMatches>(matches_ptr, &matches);
                auto matches_data = std::make_shared<DataSample<IdMatches>>(matches_shared);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                // 传递算法参数
                std::string algorithm = GetOptionAsString("algorithm", "");
                if (!algorithm.empty())
                {
                    options["algorithm"] = algorithm;
                }

                // 传递其他OpenCV相关参数

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(matches_data);
                method->SetRequiredData(required_package_["data_features"]);
                method->SetRequiredData(required_package_["data_camera_models"]);
            }
            else if (boost::iequals(estimator, "barath_two_view_estimator"))
            {
                // Barath双视图估计器 - 使用与opengv/opencv相同的数据格式
                auto matches_shared = std::shared_ptr<IdMatches>(matches_ptr, &matches);
                auto matches_data = std::make_shared<DataSample<IdMatches>>(matches_shared);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                // 传递算法参数
                std::string algorithm = GetOptionAsString("algorithm", "");
                if (!algorithm.empty())
                {
                    options["algorithm"] = algorithm;
                }

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(matches_data);
                method->SetRequiredData(required_package_["data_features"]);
                method->SetRequiredData(required_package_["data_camera_models"]);
            }
            else if (boost::iequals(estimator, "poselib_model_estimator"))
            {
                // PoseLib双视图估计器 - 使用与opengv相同的数据格式
                auto matches_shared = std::shared_ptr<IdMatches>(matches_ptr, &matches);
                auto matches_data = std::make_shared<DataSample<IdMatches>>(matches_shared);

                // 设置方法选项
                MethodOptions options;
                options["view_i"] = std::to_string(view_pair.first);
                options["view_j"] = std::to_string(view_pair.second);

                // 传递算法参数
                std::string algorithm = GetOptionAsString("algorithm", "");
                if (!algorithm.empty())
                {
                    options["algorithm"] = algorithm;
                }

                // 传递统一精细优化参数到PoseLib
                bool enable_refine = GetOptionAsBool("enable_refine", false);
                if (enable_refine)
                {
                    options["refine_model"] = "nonlinear";
                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "Enabling PoseLib internal refinement (refine_model=nonlinear) for view pair ("
                                               << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    }
                }

                method->SetMethodOptions(options);

                // 设置输入数据
                method->SetRequiredData(matches_data);
                method->SetRequiredData(required_package_["data_features"]);
                method->SetRequiredData(required_package_["data_camera_models"]);
            }
            else
            {
                PO_LOG_ERR << "Unknown estimator: " << estimator << std::endl;
                continue;
            }

            // 设置GTdata(如果先验数据有的话)
            SetCurrentViewPairGTData(view_pair);

            // 执行位姿估计

            auto result = method->Build();
            if (!result)
            {
                // 算法执行失败：清除所有内点标志
                for (auto &match : matches)
                {
                    match.is_inlier = false;
                }

                PO_LOG(PO_LOG_VERBOSE) << "Method Build() failed for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                method_failures++;
                continue;
            }

            // 获取核心计算时间并设置到当前TwoViewEstimator
            double core_time = method->GetCoreTime();
            SetCoreTime(core_time);

            if (log_level_ >= PO_LOG_VERBOSE)
            {
                PO_LOG(PO_LOG_VERBOSE) << "Retrieved core time from " << estimator << ": " << core_time << " ms" << std::endl;
            }

            // 处理内点信息并进行统一的质量验证
            if (boost::iequals(estimator, "method_LiRP") || boost::iequals(estimator, "method_LiRPFast") || boost::iequals(estimator, "method_robustLiRP") || boost::iequals(estimator, "method_povgSixPoint"))
            {
                UpdateInlierFlags(matches, estimator);
            }
            // OpenGV、OpenCV和Barath估计器会自动更新匹配数据中的内点标志，无需调用UpdateInlierFlags

            // 统计内点数量（对所有算法统一处理）
            size_t final_inlier_count = 0;
            for (const auto &match : matches)
            {
                if (match.is_inlier)
                    final_inlier_count++;
            }

            // 统一的质量验证处理（对所有算法统一管控）
            bool quality_validation_passed = false;
            bool enable_quality_validation = GetOptionAsBool("enable_quality_validation", true);

            if (enable_quality_validation)
            {
                // 使用统一的质量验证函数
                quality_validation_passed = ValidateEstimationQuality(final_inlier_count, matches.size(), estimator);

                if (!quality_validation_passed)
                {
                    // 质量验证失败：清除所有内点标志
                    for (auto &match : matches)
                    {
                        match.is_inlier = false;
                    }

                    PO_LOG(PO_LOG_VERBOSE) << "Quality validation failed for view pair ("
                                           << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    insufficient_inliers++;
                    continue; // 跳过该视图对，不保存pose结果
                }
            }
            else
            {
                // 如果不启用质量验证，仅进行基本的内点数量检查
                if (final_inlier_count < 6) // 双视图估计至少需要8个内点
                {
                    // 基本验证失败：清除所有内点标志
                    for (auto &match : matches)
                    {
                        match.is_inlier = false;
                    }

                    PO_LOG(PO_LOG_VERBOSE) << "Warning: Insufficient final inliers (" << final_inlier_count
                                           << ") for view pair (" << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    insufficient_inliers++;
                    continue; // 跳过该视图对，不保存pose结果
                }
                quality_validation_passed = true; // 基本检查通过
            }

            PO_LOG(PO_LOG_VERBOSE) << "Final inlier count: " << final_inlier_count
                                   << "/" << matches.size() << " for view pair ("
                                   << view_pair.first << "," << view_pair.second << ")" << std::endl;

            // 获取估计结果并验证有效性
            auto pose_result = GetDataPtr<RelativePose>(result);
            if (!pose_result)
            {
                // 结果提取失败：清除所有内点标志
                for (auto &match : matches)
                {
                    match.is_inlier = false;
                }

                PO_LOG(PO_LOG_VERBOSE) << "Failed to extract RelativePose from result for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                method_failures++;
                continue;
            }

            // 统一精细优化：根据估计器类型选择合适的精细优化方法
            bool enable_refine = GetOptionAsBool("enable_refine", false);
            if (enable_refine)
            {
                // 检查是否为支持PoSDK精细优化的方法（仅LiRP系列方法）
                bool supports_posdk_refine = (boost::iequals(estimator, "method_LiRP") ||
                                              boost::iequals(estimator, "method_LiRPFast") ||
                                              boost::iequals(estimator, "method_robustLiRP"));

                if (supports_posdk_refine)
                {
                    auto optimized_pose_result = ApplyPoSDKRefinement(*pose_result, bearing_pairs, view_pair);
                    if (optimized_pose_result)
                    {
                        // 使用优化后的位姿替换原始估计
                        pose_result = optimized_pose_result;

                        if (log_level_ >= PO_LOG_VERBOSE)
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "PoSDK refinement applied successfully for " << estimator
                                                   << " view pair (" << view_pair.first << "," << view_pair.second << ")" << std::endl;
                        }
                    }
                    else
                    {
                        if (log_level_ >= PO_LOG_VERBOSE)
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "PoSDK refinement failed for " << estimator
                                                   << ", using original estimate for view pair ("
                                                   << view_pair.first << "," << view_pair.second << ")" << std::endl;
                        }
                    }
                }
                else if (boost::iequals(estimator, "poselib_model_estimator"))
                {
                    // poselib_model_estimator的精细优化已在上面通过refine_model参数处理
                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "PoseLib internal refinement enabled for view pair ("
                                               << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    }
                }
                else
                {
                    // 其他估计器不支持精细优化，记录警告信息
                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "Refinement not supported for estimator " << estimator
                                               << ", ignoring enable_refine=true for view pair ("
                                               << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    }
                }
            }

            // 验证位姿数据的有效性
            bool pose_valid = true;

            // 检查旋转矩阵是否有效
            double det = pose_result->Rij.determinant();
            if (std::abs(det - 1.0) > 0.1) // 旋转矩阵行列式应该接近1
            {
                PO_LOG(PO_LOG_VERBOSE) << "Warning: Invalid rotation matrix determinant " << det
                                       << " for view pair (" << view_pair.first << "," << view_pair.second << ")" << std::endl;
                pose_valid = false;
            }

            // 检查旋转矩阵和平移向量是否包含NaN或Inf
            if (!pose_result->Rij.allFinite() || !pose_result->tij.allFinite())
            {
                PO_LOG(PO_LOG_VERBOSE) << "Warning: Non-finite values in pose for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                pose_valid = false;
            }

            // 检查平移向量是否为零向量（可能的估计失败）
            if (pose_result->tij.norm() < 1e-12)
            {
                PO_LOG(PO_LOG_VERBOSE) << "Warning: Zero translation vector for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                // 零平移可能是有效的（纯旋转），所以只警告不拒绝
            }

            if (pose_valid)
            {
                // 将算法内部格式转换为PoSDK标准格式
                RelativePose converted_pose = ToPoSDKRelativePoseFormat(*pose_result);
                poses->push_back(converted_pose);
                successful_pairs++;

                // 打印估计结果（显示转换后的PoSDK标准格式, 10位小数）
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "Successfully estimated relative pose: ("
                                           << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    PO_LOG(PO_LOG_VERBOSE) << "PoSDK format - Rotation: " << std::endl
                                           << std::fixed << std::setprecision(10) << converted_pose.Rij << std::endl;

                    PO_LOG(PO_LOG_VERBOSE) << "PoSDK format - Translation: " << std::endl
                                           << std::fixed << std::setprecision(10) << converted_pose.tij.transpose() << std::endl;

                    // 可选：同时显示原始算法输出（调试用）
                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "Original algorithm format - Rotation: " << std::endl
                                               << pose_result->Rij << std::endl;
                        PO_LOG(PO_LOG_VERBOSE) << "Original algorithm format - Translation: " << std::endl
                                               << pose_result->tij.transpose() << std::endl;
                    }
                }

                // 显示最新的评估结果（如果启用了评估器）
                if (GetOptionAsBool("enable_evaluator"))
                {
                    // 构造完整的算法名称，与上面SetEvaluatorAlgorithm中的逻辑一致
                    std::string display_algorithm = estimator;
                    if (!algorithm.empty())
                    {
                        display_algorithm += "_" + algorithm;
                    }
                    bool enable_refine = GetOptionAsBool("enable_refine", false);
                    if (enable_refine)
                    {
                        display_algorithm += "_refine";
                    }

                    // 显示最新评估结果，包含view_pairs和匹配数量信息
                    std::string view_pair_info = "(" + std::to_string(view_pair.first) + "," + std::to_string(view_pair.second) + ")";
                    EvaluatorManager::PrintLatestEvaluationResults("RelativePose", display_algorithm, "view_pairs|match_num");
                }
            }
            else
            {
                // 位姿验证失败：清除所有内点标志
                for (auto &match : matches)
                {
                    match.is_inlier = false;
                }

                PO_LOG(PO_LOG_NORMAL) << "Rejected invalid pose for view pair ("
                                      << view_pair.first << "," << view_pair.second << ")" << std::endl;
                invalid_poses++;
            }
        }

        // 输出详细的处理统计信息
        PO_LOG(PO_LOG_NORMAL) << "[TwoViewEstimator] Processing summary:" << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  Total view pairs: " << total_view_pairs << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  Processed pairs: " << processed_pairs << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  Successful estimations: " << successful_pairs << std::endl;

        if (total_view_pairs > 0)
        {
            double success_rate = (double)successful_pairs / total_view_pairs * 100.0;
            PO_LOG(PO_LOG_NORMAL) << "  Success rate: " << std::fixed << std::setprecision(1) << success_rate << "%" << std::endl;
        }

        // 详细的失败原因统计
        if (successful_pairs < total_view_pairs)
        {
            PO_LOG(PO_LOG_NORMAL) << "[TwoViewEstimator] Failure breakdown:" << std::endl;
            if (empty_matches > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Empty matches: " << empty_matches << std::endl;
            if (invalid_view_ids > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Invalid view IDs: " << invalid_view_ids << std::endl;
            if (insufficient_inliers > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Insufficient inliers: " << insufficient_inliers << std::endl;
            if (insufficient_pairs > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Insufficient pairs: " << insufficient_pairs << std::endl;
            if (conversion_failures > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Conversion failures: " << conversion_failures << std::endl;
            if (method_failures > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Method execution failures: " << method_failures << std::endl;
            if (invalid_poses > 0)
                PO_LOG(PO_LOG_NORMAL) << "  Invalid poses rejected: " << invalid_poses << std::endl;
        }

        // 5.5. 添加成功率评估结果到EvaluatorManager
        if (total_view_pairs > 0)
        {
            double success_ratio = static_cast<double>(successful_pairs) / static_cast<double>(total_view_pairs);

            // 获取评估提交信息（如果有的话）
            std::string eval_commit = GetOptionAsString("ProfileCommit", "default");

            // 添加成功率评估结果（使用统一的算法名称）
            bool add_result_success = Interface::EvaluatorManager::AddEvaluationResult(
                "RelativePoses",                  // 评估类型
                full_algorithm_name,              // 算法名称（与SetEvaluatorAlgorithm一致）
                eval_commit,                      // 评估提交信息
                "SuccessfulRatio",                // 指标名称
                success_ratio,                    // 成功率值（0.0-1.0）
                "Success rate of pose estimation" // 备注信息
            );

            if (add_result_success && log_level_ >= PO_LOG_VERBOSE)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] Added SuccessfulRatio evaluation result: "
                                       << std::fixed << std::setprecision(3) << success_ratio
                                       << " for algorithm " << full_algorithm_name << std::endl;
            }
            else if (!add_result_success)
            {
                PO_LOG_WARNING << "[TwoViewEstimator] Failed to add SuccessfulRatio evaluation result" << std::endl;
            }
        }

        if (poses->empty())
        {
            PO_LOG_WARNING << "[TwoViewEstimator] Critical Error: Failed to estimate any valid relative poses!" << std::endl;
            PO_LOG_WARNING << "Possible causes:" << std::endl;
            if (total_view_pairs == 0)
                PO_LOG_WARNING << "  - No view pairs in input matches" << std::endl;
            else if (insufficient_inliers + invalid_poses > total_view_pairs * 0.5)
                PO_LOG_WARNING << "  - Poor data quality: too many outliers or invalid matches" << std::endl;
            else if (insufficient_pairs > total_view_pairs * 0.5)
                PO_LOG_WARNING << "  - Insufficient match pairs: most pairs have < " << min_num_required_pairs << " matches" << std::endl;
            else if (method_failures > total_view_pairs * 0.5)
                PO_LOG_WARNING << "  - Algorithm failures: check " << estimator << " configuration" << std::endl;
            else if (invalid_view_ids > 0)
                PO_LOG_WARNING << "  - Data inconsistency: view IDs don't match features data" << std::endl;
            else
                PO_LOG_WARNING << "  - Mixed failures: check input data quality and algorithm parameters" << std::endl;

            return nullptr;
        }

        PO_LOG(PO_LOG_NORMAL) << "[TwoViewEstimator] Successfully estimated " << poses->size()
                              << " relative poses using " << estimator << std::endl;

        // 6. 创建输出数据包，包含相对位姿和修改后的匹配数据
        DataPackagePtr data_package_ptr = std::make_shared<DataPackage>();

        // 添加相对位姿数据
        data_package_ptr->AddData("data_relative_poses", data_relative_poses);

        // 添加修改后的匹配数据（包含更新的is_inlier信息）
        data_package_ptr->AddData("data_matches", required_package_["data_matches"]);

        PO_LOG(PO_LOG_NORMAL) << "[TwoViewEstimator] Output package contains:" << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  - data_relative_poses: " << poses->size() << " poses" << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  - data_matches: " << matches_ptr->size() << " view pairs (with updated inlier flags)" << std::endl;

        return data_package_ptr;
    }

    bool TwoViewEstimator::MatchesToBearingPairs(
        const IdMatches &matches,
        const FeaturesInfo &features_info,
        const CameraModels &camera_models,
        const ViewPair &view_pair,
        BearingPairs &bearing_pairs)
    {
        try
        {
            // 1. 清空输出向量
            bearing_pairs.clear();

            // 2. 如果是空匹配，直接返回成功
            if (matches.empty())
            {
                return true;
            }

            // 3. 获取对应的相机模型
            const CameraModel *camera1 = GetCameraModel(camera_models, view_pair.first);
            const CameraModel *camera2 = GetCameraModel(camera_models, view_pair.second);

            if (!camera1 || !camera2)
            {
                PO_LOG_ERR << "Failed to get camera models for views" << std::endl;
                return false;
            }

            // 4. 预分配空间
            bearing_pairs.reserve(matches.size());

            // 5. 转换每个匹配点
            for (const auto &match : matches)
            {
                // 获取特征点坐标
                const Vector2d &pt1 = features_info[view_pair.first].features[match.i].coord;
                const Vector2d &pt2 = features_info[view_pair.second].features[match.j].coord;

                // 转换为单位向量
                BearingVector bearing1 = PixelToBearingVector(pt1, *camera1);
                BearingVector bearing2 = PixelToBearingVector(pt2, *camera2);

                // 构造6x1的向量对 （更新了，统一opengv的顺序）
                // 以前论文是：xj = Rij * xi + tij
                // 现在是：xi = Rji * xj + tji
                // 其中： (bearing2, bearing1) >> Rij, tij
                // 否则： (bearing1, bearing2) >> Rji, tji

                Eigen::Matrix<double, 6, 1> bearing_pair;
                bearing_pair.head<3>() = bearing1;
                bearing_pair.tail<3>() = bearing2;

                bearing_pairs.push_back(bearing_pair);
            }

            return true;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in MatchesToBearingPairs: " << e.what() << std::endl;
            return false;
        }
    }

    void TwoViewEstimator::UpdateInlierFlags(IdMatches &matches, const std::string &estimator)
    {
        try
        {
            // 获取当前方法使用的DataSample
            DataPtr sample_data = nullptr;

            // 根据估计器类型获取对应的DataSample
            if (boost::iequals(estimator, "method_LiRP") || boost::iequals(estimator, "method_robustLiRP") || boost::iequals(estimator, "method_povgSixPoint"))
            {
                // 从方法的required_package中获取data_sample
                auto method_cast = std::dynamic_pointer_cast<MethodPreset>(current_method_);
                if (method_cast)
                {
                    const auto &required_package = method_cast->GetRequiredPackage();
                    auto it = required_package.find("data_sample");
                    if (it != required_package.end())
                    {
                        sample_data = it->second;
                    }
                }
            }

            if (!sample_data)
            {
                PO_LOG(PO_LOG_VERBOSE) << "No sample data available for inlier update" << std::endl;
                return;
            }

            // 尝试转换为DataSample<BearingPairs>
            auto bearing_sample = std::dynamic_pointer_cast<DataSample<BearingPairs>>(sample_data);
            if (!bearing_sample)
            {
                PO_LOG(PO_LOG_VERBOSE) << "Cannot cast to DataSample<BearingPairs>" << std::endl;
                return;
            }

            // 检查是否有内点信息
            if (!bearing_sample->HasBestInliers())
            {
                PO_LOG(PO_LOG_VERBOSE) << "No inlier information available in DataSample" << std::endl;
                return;
            }

            // 获取内点索引
            auto best_inliers = bearing_sample->GetBestInliers();
            if (!best_inliers || best_inliers->empty())
            {
                PO_LOG(PO_LOG_VERBOSE) << "Empty inlier indices" << std::endl;
                return;
            }

            // 首先将所有匹配标记为外点
            for (auto &match : matches)
            {
                match.is_inlier = false;
            }

            // 根据内点索引更新is_inlier标志
            for (size_t inlier_idx : *best_inliers)
            {
                if (inlier_idx < matches.size())
                {
                    matches[inlier_idx].is_inlier = true;
                }
            }

            // 统计内点数量并输出日志
            size_t inlier_count = best_inliers->size();
            size_t total_matches = matches.size();
            double inlier_ratio = static_cast<double>(inlier_count) / total_matches;

            PO_LOG(PO_LOG_VERBOSE) << "Updated inlier flags: " << inlier_count
                                   << "/" << total_matches << " ("
                                   << std::fixed << std::setprecision(1) << (inlier_ratio * 100)
                                   << "%) inliers" << std::endl;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error updating inlier flags: " << e.what() << std::endl;
        }
    }

    RelativePose TwoViewEstimator::ToPoSDKRelativePoseFormat(const RelativePose &pose_result)
    {
        // 创建转换后的相对位姿
        RelativePose converted_pose = pose_result;

        // 应用坐标转换：从OpenGV内部约定转换为PoSDK标准约定
        // OpenGV内部约定: xi = R * xj + t
        // PoSDK标准约定: xj = R * xi + t
        // 转换公式: R_posdk = R_opengv^T, t_posdk = -R_opengv^T * t_opengv

        Matrix3d R_opengv = pose_result.Rij;
        Vector3d t_opengv = pose_result.tij;

        // 执行坐标转换
        converted_pose.Rij = R_opengv.transpose();             // R^T
        converted_pose.tij = -R_opengv.transpose() * t_opengv; // -R^T * t

        // 保持其他属性不变
        converted_pose.i = pose_result.i;
        converted_pose.j = pose_result.j;
        converted_pose.weight = pose_result.weight;

        return converted_pose;
    }

    bool TwoViewEstimator::ValidateEstimationQuality(size_t inlier_count, size_t total_matches, const std::string &estimator_name) const
    {
        // 获取质量控制参数
        size_t min_geometric_inliers = GetOptionAsIndexT("min_geometric_inliers", 50);
        double min_inlier_ratio = GetOptionAsFloat("min_inlier_ratio", 0.25);

        // 1. 检查几何内点数量（参考OpenMVG的50个内点要求）
        if (inlier_count < min_geometric_inliers)
        {
            PO_LOG(PO_LOG_VERBOSE) << "[" << estimator_name << "] Quality validation failed: insufficient geometric inliers ("
                                   << inlier_count << " < " << min_geometric_inliers << ")" << std::endl;
            return false;
        }

        // 2. 检查内点比例
        double inlier_ratio = static_cast<double>(inlier_count) / static_cast<double>(total_matches);
        if (inlier_ratio < min_inlier_ratio)
        {
            PO_LOG(PO_LOG_VERBOSE) << "[" << estimator_name << "] Quality validation failed: low inlier ratio ("
                                   << std::fixed << std::setprecision(3) << inlier_ratio
                                   << " < " << min_inlier_ratio << ")" << std::endl;
            return false;
        }

        // 3. 基本的内点数量检查（双视图估计至少需要8个内点）
        if (inlier_count < 6)
        {
            PO_LOG(PO_LOG_VERBOSE) << "[" << estimator_name << "] Quality validation failed: insufficient inliers for pose estimation ("
                                   << inlier_count << " < 8)" << std::endl;
            return false;
        }

        // 4. 所有检查通过
        PO_LOG(PO_LOG_VERBOSE) << "[" << estimator_name << "] Quality validation passed: "
                               << inlier_count << " inliers ("
                               << std::fixed << std::setprecision(1) << (inlier_ratio * 100.0) << "%)" << std::endl;

        return true;
    }

    void TwoViewEstimator::SetCurrentViewPairGTData(const ViewPair &view_pair)
    {
        try
        {
            // 检查是否有GT数据
            auto gt_data_it = prior_info_.find("gt_data");
            if (gt_data_it == prior_info_.end() || !gt_data_it->second)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] No GT data found in prior_info_" << std::endl;
                return;
            }

            DataPtr gt_data = gt_data_it->second;

            // 尝试转换为RelativePoses数据
            auto gt_poses_ptr = GetDataPtr<RelativePoses>(gt_data);
            if (!gt_poses_ptr)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] GT data is not RelativePoses type: " << gt_data->GetType() << std::endl;
                return;
            }

            // 使用新实现的GetRelativePose函数获取当前view_pair的GT位姿
            Matrix3d R_gt;
            Vector3d t_gt;
            if (gt_poses_ptr->GetRelativePose(view_pair, R_gt, t_gt))
            {
                // 创建单个RelativePose作为GT数据
                RelativePose current_gt_pose(view_pair.first, view_pair.second, R_gt.transpose(), -R_gt.transpose() * t_gt);

                // 包装为DataMap
                auto current_gt_pose_datamap = std::make_shared<DataMap<RelativePose>>(current_gt_pose);
                DataPtr current_gt_pose_data = std::static_pointer_cast<DataIO>(current_gt_pose_datamap);

                // 为method设置GT数据
                if (current_method_)
                {
                    auto method_cast = std::dynamic_pointer_cast<MethodPresetProfiler>(current_method_);
                    if (method_cast)
                    {
                        method_cast->SetGTData(current_gt_pose_data);
                        PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] Set GT pose for view pair ("
                                               << view_pair.first << "," << view_pair.second << ")" << std::endl;
                    }
                    else
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] Method does not support GT data setting" << std::endl;
                    }
                }
                else
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] current_method_ is null" << std::endl;
                }
            }
            else
            {
                PO_LOG(PO_LOG_VERBOSE) << "[TwoViewEstimator] No GT pose found for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "[TwoViewEstimator] Error setting GT data for view pair ("
                      << view_pair.first << "," << view_pair.second << "): " << e.what() << std::endl;
        }
    }

    std::shared_ptr<RelativePose> TwoViewEstimator::ApplyPoSDKRefinement(
        const RelativePose &initial_pose,
        const BearingPairs &bearing_pairs,
        const ViewPair &view_pair)
    {
        try
        {
            // 1. 创建MethodTwoViewOptimizer实例
            auto optimizer_method = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_TwoViewOptimizer"));

            if (!optimizer_method)
            {
                PO_LOG_ERR << "[PoSDK Refinement] Failed to create method_TwoViewOptimizer" << std::endl;
                return nullptr;
            }

            // 2. 准备输入数据
            // 创建bearing pairs数据
            auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

            // 创建初始位姿数据
            auto initial_pose_data = std::make_shared<DataMap<RelativePose>>(initial_pose);

            // 创建输入数据包
            auto optimizer_package = std::make_shared<DataPackage>();
            (*optimizer_package)["data_sample"] = sample_data;
            (*optimizer_package)["data_map"] = initial_pose_data;

            // 3. 设置优化器参数（使用推荐配置）
            MethodOptions optimizer_options;

            // 基本配置
            optimizer_options["view_i"] = std::to_string(view_pair.first);
            optimizer_options["view_j"] = std::to_string(view_pair.second);

            // 优化器配置（使用推荐的ppo_opengv残差函数）
            optimizer_options["optimizer_type"] = "eigen_lm";
            optimizer_options["residual_type"] = "ppo_opengv";
            optimizer_options["loss_type"] = "cauchy";

            // 损失函数阈值（使用推荐配置）
            optimizer_options["huber_threshold_explicit"] = "0.0016"; // Huber阈值：0.0016
            optimizer_options["cauchy_threshold_explicit"] = "0.01";  // Cauchy阈值：0.008

            optimizer_method->SetMethodOptions(optimizer_options);
            optimizer_method->SetRequiredData(optimizer_package);

            if (log_level_ >= PO_LOG_VERBOSE)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Starting optimization for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Using " << optimizer_options["residual_type"] << " + " << optimizer_options["loss_type"] << " loss with thresholds [" << optimizer_options["huber_threshold_explicit"] << ", " << optimizer_options["cauchy_threshold_explicit"] << "]" << std::endl;
            }

            // 4. 执行优化
            auto result = optimizer_method->Build();
            if (!result)
            {
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Optimization failed for view pair ("
                                           << view_pair.first << "," << view_pair.second << ")" << std::endl;
                }
                return nullptr;
            }

            // 5. 提取优化结果
            auto optimized_pose_ptr = GetDataPtr<RelativePose>(result);
            if (!optimized_pose_ptr)
            {
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Failed to extract optimized pose for view pair ("
                                           << view_pair.first << "," << view_pair.second << ")" << std::endl;
                }
                return nullptr;
            }

            // 6. 验证优化结果的有效性
            if (!optimized_pose_ptr->Rij.allFinite() || !optimized_pose_ptr->tij.allFinite())
            {
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Optimized pose contains non-finite values for view pair ("
                                           << view_pair.first << "," << view_pair.second << ")" << std::endl;
                }
                return nullptr;
            }

            // 检查旋转矩阵的有效性
            double det = optimized_pose_ptr->Rij.determinant();
            if (std::abs(det - 1.0) > 0.1)
            {
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Invalid rotation matrix determinant " << det
                                           << " for view pair (" << view_pair.first << "," << view_pair.second << ")" << std::endl;
                }
                return nullptr;
            }

            if (log_level_ >= PO_LOG_VERBOSE)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Optimization completed successfully for view pair ("
                                       << view_pair.first << "," << view_pair.second << ")" << std::endl;

                // 计算优化前后的位姿差异（可选的调试信息）
                Matrix3d R_diff = initial_pose.Rij.transpose() * optimized_pose_ptr->Rij;
                Vector3d t_diff = optimized_pose_ptr->tij - initial_pose.tij;
                double rotation_diff_deg = std::acos(std::min(1.0, std::max(-1.0, (R_diff.trace() - 1.0) / 2.0))) * 180.0 / M_PI;
                double translation_diff = t_diff.norm();

                PO_LOG(PO_LOG_VERBOSE) << "[PoSDK Refinement] Refinement impact: rotation_diff="
                                       << std::fixed << std::setprecision(6) << rotation_diff_deg
                                       << "°, translation_diff=" << translation_diff << std::endl;
            }

            // 返回优化后的位姿（创建shared_ptr）
            return std::make_shared<RelativePose>(*optimized_pose_ptr);
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "[PoSDK Refinement] Exception during optimization: " << e.what() << std::endl;
            return nullptr;
        }
    }

} // namespace PluginMethods

REGISTRATION_PLUGIN(PluginMethods::TwoViewEstimator, "two_view_estimator")
