/**
 * @file two_view_estimator.hpp
 * @brief 双视图位姿估计器
 * @copyright Copyright (c) 2024 XXX公司
 */

#pragma once

#include <po_core.hpp>
#include <common/converter/converter_opengv.hpp>
#include <opengv/relative_pose/methods.hpp>

namespace PluginMethods
{
    using namespace PoSDK;
    using namespace Interface;
    using namespace types;
    class TwoViewEstimator : public Interface::MethodPresetProfiler
    {
    public:
        TwoViewEstimator();
        ~TwoViewEstimator() = default;

        DataPtr Run() override;

        const std::string &GetType() const override
        {
            static const std::string type = "two_view_estimator";
            return type;
        }

        // 实现 Call 接口
    private:
        /**
         * @brief 将匹配点转换为单位向量
         * @param matches 匹配点
         * @param features_info 特征信息
         * @param camera_models 相机模型
         * @param view_pair 视图对
         * @param bearing_pairs 单位向量
         */
        bool MatchesToBearingPairs(
            const IdMatches &matches,
            const FeaturesInfo &features_info,
            const CameraModels &camera_models,
            const ViewPair &view_pair,
            BearingPairs &bearing_pairs);

        /**
         * @brief 更新匹配点的内点标志
         * @param matches 匹配点引用，将被修改
         * @param estimator 估计器类型名称
         */
        void UpdateInlierFlags(IdMatches &matches, const std::string &estimator);

        /**
         * @brief 将算法内部的相对位姿转换为PoSDK标准格式
         * @details 算法内部使用OpenGV约定: xi = R * xj + t
         *          转换为PoSDK标准约定: xj = R * xi + t
         * @param pose_result 输入的相对位姿（OpenGV内部格式）
         * @return 转换后的相对位姿（PoSDK标准格式）
         * @note 转换公式: R_posdk = R_opengv^T, t_posdk = -R_opengv^T * t_opengv
         */
        RelativePose ToPoSDKRelativePoseFormat(const RelativePose &pose_result);

        /**
         * @brief 统一的质量验证函数
         * @param inlier_count 内点数量
         * @param total_matches 总匹配数量
         * @param estimator_name 估计器名称（用于日志输出）
         * @return 是否通过质量验证
         */
        bool ValidateEstimationQuality(size_t inlier_count, size_t total_matches, const std::string &estimator_name) const;

        /**
         * @brief 为当前method设置对应view_pair的GT相对位姿数据
         * @param view_pair 当前处理的视图对
         * @details 从prior_info_中获取GT相对位姿数据，提取当前view_pair对应的位姿，并设置给method
         */
        void SetCurrentViewPairGTData(const ViewPair &view_pair);

        /**
         * @brief 使用PoSDK TwoViewOptimizer对位姿进行精细优化
         * @param initial_pose 初始位姿估计
         * @param bearing_pairs 对应的bearing pairs数据
         * @param view_pair 视图对信息
         * @return 优化后的位姿，如果优化失败则返回nullptr
         */
        std::shared_ptr<RelativePose> ApplyPoSDKRefinement(
            const RelativePose &initial_pose,
            const BearingPairs &bearing_pairs,
            const ViewPair &view_pair);

        Interface::MethodPresetPtr current_method_; ///< 当前使用的方法实例
    };

} // namespace PluginMethods
