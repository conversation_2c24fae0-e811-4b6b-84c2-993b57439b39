{"version": "2.0.0", "tasks": [{"label": "CMake: 配置", "type": "shell", "command": "cmake", "args": ["-B", "${workspaceFolder}/build/${input:buildType}", "-S", "${workspaceFolder}/src", "-DCMAKE_BUILD_TYPE=${input:buildType}", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"], "group": "build", "problemMatcher": []}, {"label": "CMake: 构建", "type": "shell", "command": "cmake", "args": ["--build", "${workspaceFolder}/build/${input:buildType}", "--config", "${input:buildType}", "-j", "${input:jobs}"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$gcc"}, {"label": "CMake: 清理", "type": "shell", "command": "cmake", "args": ["--build", "${workspaceFolder}/build/${input:buildType}", "--target", "clean"], "group": "build", "problemMatcher": []}, {"label": "CMake: 重新构建", "dependsOrder": "sequence", "dependsOn": ["CMake: 清理", "CMake: 构建"], "group": "build", "problemMatcher": []}], "inputs": [{"id": "buildType", "type": "pickString", "options": ["Debug", "Release"], "default": "Debug", "description": "选择构建类型"}, {"id": "jobs", "type": "promptString", "default": "8", "description": "并行构建的任务数"}]}