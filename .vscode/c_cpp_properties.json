{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/src/**", "${workspaceFolder}/build/Debug/output/include/**", "${workspaceFolder}/build/Release/output/include/**", "/usr/include/**", "/usr/local/include/**"], "defines": [], "compilerPath": "/usr/bin/g++", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64", "configurationProvider": "ms-vscode.cmake-tools", "compileCommands": "${workspaceFolder}/build/Debug/compile_commands.json", "browse": {"path": ["${workspaceFolder}/src", "${workspaceFolder}/build/Debug/output/include", "${workspaceFolder}/build/Release/output/include", "/usr/include", "/usr/local/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/.browse.vc.db"}}], "version": 4}