{"cmake.sourceDirectory": "${workspaceFolder}/src", "cmake.buildDirectory": "${workspaceFolder}/build/${variant:buildType}", "cmake.configureOnOpen": true, "cmake.configureArgs": ["-DCMAKE_BUILD_TYPE=${variant:buildType}"], "cmake.defaultVariants": {"buildType": {"default": "debug", "description": "构建类型", "choices": {"debug": {"short": "Debug", "long": "包含调试信息的非优化构建", "buildType": "Debug"}, "release": {"short": "Release", "long": "优化构建，无调试信息", "buildType": "Release"}}}}, "files.associations": {"*.tex": "latex", "*.py": "python", "*.hpp": "cpp", "*.cpp": "cpp", "*.h": "cpp", "*.c": "cpp", "*.inl": "cpp", "*.in": "cpp", "CMakeLists.txt": "cmake", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "source_location": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "variant": "cpp", "filesystem": "cpp", "expected": "cpp", "future": "cpp", "*.ipp": "cpp", "ranges": "cpp", "valarray": "cpp", "core": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__threading_support": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "csignal": "cpp", "execution": "cpp", "ios": "cpp", "locale": "cpp", "print": "cpp", "queue": "cpp", "stack": "cpp", "csetjmp": "cpp", "scoped_allocator": "cpp", "__config": "cpp", "*.inc": "cpp", "__string": "cpp", "kroneckerproduct": "cpp", "numericaldiff": "cpp", "specialfunctions": "cpp", "nonlinearoptimization": "cpp", "*.th": "cpp", "matrixfunctions": "cpp", "text_encoding": "cpp", "polynomials": "cpp", "__memory": "cpp", "__bits": "cpp", "__debug": "cpp", "__nullptr": "cpp", "__tuple": "cpp"}, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.formatOnSave": true, "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.includePath": ["${workspaceFolder}/src/**", "${workspaceFolder}/build/output/include/**"], "C_Cpp.default.defines": [], "C_Cpp.default.compileCommands": "${workspaceFolder}/build/${variant:buildType}/compile_commands.json", "python.defaultInterpreterPath": "/Users/<USER>/miniconda3/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.autoSearchPaths": true, "python.analysis.extraPaths": ["${workspaceFolder}/src/drawer", "${workspaceFolder}/src"], "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.flake8Args": ["--max-line-length=120", "--ignore=E501,W503"], "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=120"], "python.debugging.console": "integratedTerminal"}