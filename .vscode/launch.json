{"version": "0.2.0", "configurations": [{"name": "Python: plot_editor_gui.py 调试", "type": "python", "request": "launch", "program": "${workspaceFolder}/src/drawer/plot_editor_gui.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/src/drawer", "python": "/Users/<USER>/miniconda3/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}/src/drawer", "CONDA_DEFAULT_ENV": "base", "CONDA_PREFIX": "/Users/<USER>/miniconda3"}, "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "args": []}, {"name": "Python: 当前文件调试", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${fileDirname}", "python": "/Users/<USER>/miniconda3/bin/python", "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "Python: 模块调试", "type": "python", "request": "launch", "module": "plot_editor_gui", "console": "integratedTerminal", "cwd": "${workspaceFolder}/src/drawer", "python": "/Users/<USER>/miniconda3/bin/python", "justMyCode": false, "args": []}, {"name": "调试测试程序", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/Debug/output/bin/test_all", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/build/Debug/output/lib:${env:LD_LIBRARY_PATH}"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: 构建", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "自定义程序调试", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/Debug/output/bin/${input:programName}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/build/Debug/output/lib:${env:LD_LIBRARY_PATH}"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: 构建", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "附加到进程", "type": "cppdbg", "request": "attach", "processId": "${command:pickProcess}", "program": "${workspaceFolder}/build/Debug/output/bin/${input:programName}", "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}], "inputs": [{"id": "programName", "type": "promptString", "description": "输入要调试的程序名称", "default": "test_all"}]}