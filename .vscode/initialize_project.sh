#!/bin/bash

# 确保脚本在错误时停止
set -e

# 创建Debug构建目录
mkdir -p build/Debug
cd build/Debug

# 配置CMake项目
cmake -S ../../src -B . -DCMAKE_BUILD_TYPE=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

# 创建符号链接，确保VS Code能找到编译命令文件
if [ -f compile_commands.json ]; then
    echo "为VS Code创建compile_commands.json符号链接..."
    ln -sf $(pwd)/compile_commands.json ../../.vscode/
fi

# 回到项目根目录
cd ../..

echo "项目初始化完成。"
echo "请在VS Code中重新加载窗口以刷新IntelliSense索引。" 