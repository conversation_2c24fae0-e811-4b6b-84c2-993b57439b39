#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
matplotlib图表编辑器 - GUI版本

这个程序提供一个图形界面来编辑保存在.pkl文件中的matplotlib图表。
支持修改：
- 线条颜色和透明度
- 线条宽度和样式
- 标记类型和大小
- 图表标题和标签
- 图例设置
"""

import sys
import pickle
import numpy as np
import json
import os
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import matplotlib.colors as mcolors

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QFileDialog, QLabel, QComboBox, 
                                QSpinBox, QDoubleSpinBox, QColorDialog, QGroupBox,
                                QListWidget, QListWidgetItem, QLineEdit, QCheckBox,
                                QSlider, QGridLayout, QMessageBox, QSplitter,
                                QScrollArea, QFrame, QProgressDialog, QTextEdit,
                                QDialog, QDialogButtonBox, QTabWidget, QMenu)
    from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
    from PyQt5.QtGui import QColor, QFont
except ImportError:
    print("Error: PyQt5 is required for this GUI application.")
    print("Install it with: pip install PyQt5")

    sys.exit(1)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_file = os.path.expanduser("~/.plot_editor_config.json")
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            "last_directory": os.path.expanduser("~"),
            "algorithm_styles": {}
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_last_directory(self):
        """获取上次打开的目录"""
        return self.config.get("last_directory", os.path.expanduser("~"))
    
    def set_last_directory(self, directory):
        """设置上次打开的目录"""
        self.config["last_directory"] = directory
        self.save_config()
    
    def save_algorithm_style(self, algorithm_name, style_dict):
        """保存算法样式"""
        self.config["algorithm_styles"][algorithm_name] = style_dict
        self.save_config()
    
    def get_algorithm_style(self, algorithm_name):
        """获取算法样式"""
        return self.config["algorithm_styles"].get(algorithm_name, None)
    
    def get_all_algorithm_styles(self):
        """获取所有算法样式"""
        return self.config["algorithm_styles"]


class BatchProcessDialog(QDialog):
    """批处理对话框"""
    
    def __init__(self, algorithm_styles, parent=None):
        super().__init__(parent)
        self.algorithm_styles = algorithm_styles
        self.selected_files = []
        self.selected_directory = ""
        self.setupUI()
        self.setWindowTitle("批处理设置")
        self.resize(600, 400)
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 选择模式
        mode_group = QGroupBox("处理模式")
        mode_layout = QVBoxLayout()
        
        self.file_mode_btn = QPushButton("选择多个文件")
        self.file_mode_btn.clicked.connect(self.selectFiles)
        mode_layout.addWidget(self.file_mode_btn)
        
        self.dir_mode_btn = QPushButton("选择目录（处理所有.pkl文件）")
        self.dir_mode_btn.clicked.connect(self.selectDirectory)
        mode_layout.addWidget(self.dir_mode_btn)
        
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # 选择结果显示
        result_group = QGroupBox("选择结果")
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(100)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 样式预览
        style_group = QGroupBox("将要应用的样式")
        style_layout = QVBoxLayout()
        
        self.style_text = QTextEdit()
        self.style_text.setReadOnly(True)
        self.updateStylePreview()
        style_layout.addWidget(self.style_text)
        
        style_group.setLayout(style_layout)
        layout.addWidget(style_group)
        
        # 导出选项
        export_group = QGroupBox("导出选项")
        export_layout = QVBoxLayout()
        
        self.export_png_check = QCheckBox("同时导出PNG文件")
        self.export_png_check.setChecked(True)  # 默认勾选
        self.export_png_check.setToolTip("处理完PKL文件后，自动导出对应的PNG图片文件")
        export_layout.addWidget(self.export_png_check)
        
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def selectFiles(self):
        """选择多个文件"""
        # 尝试从父窗口获取配置管理器
        last_dir = os.path.expanduser("~")
        if hasattr(self.parent(), 'config_manager'):
            last_dir = self.parent().config_manager.get_last_directory()
        
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择.pkl文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
        
        if files:
            self.selected_files = files
            self.selected_directory = ""
            self.result_text.setText(f"选择了 {len(files)} 个文件:\n" + "\n".join(files))
    
    def selectDirectory(self):
        """选择目录"""
        # 尝试从父窗口获取配置管理器
        last_dir = os.path.expanduser("~")
        if hasattr(self.parent(), 'config_manager'):
            last_dir = self.parent().config_manager.get_last_directory()
        
        directory = QFileDialog.getExistingDirectory(self, "选择目录", last_dir)
        
        if directory:
            self.selected_directory = directory
            self.selected_files = []
            
            # 查找目录中的.pkl文件
            pkl_files = list(Path(directory).glob("*.pkl"))
            self.result_text.setText(f"目录: {directory}\n找到 {len(pkl_files)} 个.pkl文件:\n" + 
                                   "\n".join([f.name for f in pkl_files]))
    
    def updateStylePreview(self):
        """更新样式预览"""
        if not self.algorithm_styles:
            self.style_text.setText("没有保存的样式")
            return
        
        preview_text = "算法样式预览:\n\n"
        for algorithm, style in self.algorithm_styles.items():
            preview_text += f"算法: {algorithm}\n"
            for key, value in style.items():
                preview_text += f"  {key}: {value}\n"
            preview_text += "\n"
        
        self.style_text.setText(preview_text)
    
    def getSelectedFiles(self):
        """获取选择的文件列表"""
        if self.selected_files:
            return self.selected_files
        elif self.selected_directory:
            return [str(f) for f in Path(self.selected_directory).glob("*.pkl")]
        else:
            return []
    
    def shouldExportPNG(self):
        """是否应该导出PNG文件"""
        return self.export_png_check.isChecked()


class BatchProcessThread(QThread):
    """批处理线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished_signal = pyqtSignal(int, int)  # 成功数量, 总数量
    
    def __init__(self, files, algorithm_styles, export_png=True):
        super().__init__()
        self.files = files
        self.algorithm_styles = algorithm_styles
        self.export_png = export_png
    
    def run(self):
        """运行批处理"""
        success_count = 0
        total_count = len(self.files)
        
        for i, file_path in enumerate(self.files):
            try:
                self.status.emit(f"处理文件: {Path(file_path).name}")
                
                # 加载.pkl文件
                with open(file_path, 'rb') as f:
                    figure = pickle.load(f)
                
                # 应用样式
                styles_applied = False
                if len(figure.axes) > 0:
                    ax = figure.axes[0]
                    lines = ax.lines
                    
                    for line in lines:
                        # 尝试多种方式获取算法名称
                        algorithm_name = self._getAlgorithmNameForLine(line, ax, lines)
                        
                        if algorithm_name and algorithm_name in self.algorithm_styles:
                            style = self.algorithm_styles[algorithm_name]
                            
                            # 应用样式
                            if 'color' in style:
                                line.set_color(style['color'])
                            if 'linewidth' in style:
                                line.set_linewidth(style['linewidth'])
                            if 'linestyle' in style:
                                line.set_linestyle(style['linestyle'])
                            if 'marker' in style:
                                line.set_marker(style['marker'])
                            if 'markersize' in style:
                                line.set_markersize(style['markersize'])
                            if 'alpha' in style:
                                line.set_alpha(style['alpha'])
                            
                            styles_applied = True
                
                # 如果应用了样式，需要更新图例并重新绘制图表
                if styles_applied:
                    # 更新图例样式以匹配线条样式
                    self._updateLegendStyles(ax)
                    
                    # 创建一个临时的canvas来重新绘制
                    from matplotlib.backends.backend_agg import FigureCanvasAgg
                    canvas = FigureCanvasAgg(figure)
                    canvas.draw()
                
                # 保存PKL文件
                with open(file_path, 'wb') as f:
                    pickle.dump(figure, f)
                
                # 如果需要，导出PNG文件
                if self.export_png:
                    try:
                        png_path = Path(file_path).with_suffix('.png')
                        figure.savefig(png_path, dpi=300, bbox_inches='tight', 
                                     facecolor='white', edgecolor='none')
                        self.status.emit(f"已导出PNG: {png_path.name}")
                    except Exception as png_error:
                        self.status.emit(f"PNG导出失败 {Path(file_path).name}: {str(png_error)}")
                
                success_count += 1
                
            except Exception as e:
                self.status.emit(f"处理文件失败 {Path(file_path).name}: {str(e)}")
            
            # 更新进度
            progress = int((i + 1) / total_count * 100)
            self.progress.emit(progress)
        
        self.finished_signal.emit(success_count, total_count)
    
    def _getAlgorithmNameForLine(self, line, ax, lines):
        """为线条获取有效的算法名称"""
        # 首先尝试从线条标签获取
        label = line.get_label()
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 如果标签无效，尝试从图例获取
        try:
            legend = ax.get_legend()
            if legend:
                # 找到这条线在lines列表中的索引
                line_index = -1
                for i, l in enumerate(lines):
                    if l is line:
                        line_index = i
                        break
                
                # 获取对应的图例文本
                if line_index >= 0 and len(legend.get_texts()) > line_index:
                    legend_text = legend.get_texts()[line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        return legend_text.strip()
        except:
            pass
        
        return None
    
    def _updateLegendStyles(self, ax):
        """更新图例样式以匹配线条样式"""
        try:
            legend = ax.get_legend()
            if legend:
                # 获取图例中的线条对象
                legend_lines = legend.get_lines()
                plot_lines = ax.lines
                
                # 确保图例线条数量与绘图线条数量匹配
                for i, (legend_line, plot_line) in enumerate(zip(legend_lines, plot_lines)):
                    # 同步样式
                    legend_line.set_color(plot_line.get_color())
                    legend_line.set_linewidth(plot_line.get_linewidth())
                    legend_line.set_linestyle(plot_line.get_linestyle())
                    legend_line.set_marker(plot_line.get_marker())
                    legend_line.set_markersize(plot_line.get_markersize())
                    legend_line.set_alpha(plot_line.get_alpha())
        except Exception as e:
            # 如果图例更新失败，不影响主要功能
            pass


class ColorButton(QPushButton):
    """自定义颜色选择按钮"""
    colorChanged = pyqtSignal(str)
    
    def __init__(self, color='blue'):
        super().__init__()
        self.setFixedSize(40, 30)
        self.color = color
        self.updateColor()
        self.clicked.connect(self.chooseColor)
    
    def updateColor(self):
        """更新按钮颜色显示"""
        try:
            # 将matplotlib颜色转换为RGB
            rgb = mcolors.to_rgb(self.color)
            r, g, b = [int(c * 255) for c in rgb]
            self.setStyleSheet(f"background-color: rgb({r}, {g}, {b}); border: 1px solid black;")
        except:
            self.setStyleSheet("background-color: gray; border: 1px solid black;")
    
    def chooseColor(self):
        """打开颜色选择对话框"""
        color_dialog = QColorDialog()
        
        # 设置当前颜色
        try:
            current_qcolor = QColor(self.color)
            color_dialog.setCurrentColor(current_qcolor)
        except:
            pass
        
        # 连接颜色改变信号以实现实时预览
        color_dialog.currentColorChanged.connect(self.onColorPreview)
        
        if color_dialog.exec_():
            qcolor = color_dialog.currentColor()
            # 转换为matplotlib颜色格式
            self.color = f"#{qcolor.red():02x}{qcolor.green():02x}{qcolor.blue():02x}"
            self.updateColor()
            self.colorChanged.emit(self.color)
        else:
            # 如果取消，恢复原来的颜色
            self.colorChanged.emit(self.color)
    
    def onColorPreview(self, qcolor):
        """颜色预览"""
        preview_color = f"#{qcolor.red():02x}{qcolor.green():02x}{qcolor.blue():02x}"
        self.colorChanged.emit(preview_color)
    
    def setColor(self, color):
        """设置颜色"""
        self.color = color
        self.updateColor()


class LinePropertiesWidget(QWidget):
    """线条属性编辑控件"""
    propertiesChanged = pyqtSignal()
    styleChanged = pyqtSignal(str, dict)  # 算法名称, 样式字典
    
    def __init__(self, line, line_index, config_manager=None):
        super().__init__()
        self.line = line
        self.line_index = line_index
        self.config_manager = config_manager
        self.setupUI()
        self.loadLineProperties()
    
    def setupUI(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 算法标识 - 显示算法名称和颜色预览
        legend_name = self.line.get_label()
        
        # 尝试从图例中获取更准确的算法名称
        try:
            if hasattr(self.line, 'axes') and self.line.axes:
                ax = self.line.axes
                legend = ax.get_legend()
                if legend and len(legend.get_texts()) > self.line_index:
                    legend_text = legend.get_texts()[self.line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        legend_name = legend_text.strip()
        except:
            pass  # 如果获取失败，使用原来的名称
        
        # 如果仍然没有有效名称，使用默认名称
        if not legend_name or legend_name.startswith('_') or not legend_name.strip():
            legend_name = f"Line {self.line_index + 1}"
        
        # 创建标题容器，包含颜色预览 - 固定大小
        title_container = QWidget()
        title_container.setFixedHeight(50)  # 固定高度
        title_container.setStyleSheet("""
            QWidget {
                background-color: #e8f4fd;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin: 2px;
                padding: 8px;
            }
        """)
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(5, 5, 5, 5)
        
        # 颜色预览方块
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(24, 24)
        self.color_preview.setStyleSheet("QLabel { border: 2px solid black; border-radius: 4px; }")
        self.updateColorPreview()
        
        # 算法名称标签 - 支持文本省略
        self.label = QLabel(f"算法: {legend_name}")
        self.label.setFont(QFont("Arial", 11, QFont.Bold))
        self.label.setStyleSheet("QLabel { color: #2c3e50; background-color: transparent; }")
        # 设置文本省略模式，当文本过长时显示省略号
        self.label.setWordWrap(False)
        self.label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        # 设置工具提示显示完整名称
        self.label.setToolTip(f"算法: {legend_name}")
        
        # 算法信息按钮
        self.info_btn = QPushButton("ℹ")
        self.info_btn.setFixedSize(24, 24)
        self.info_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.info_btn.clicked.connect(self.showAlgorithmInfo)
        
        title_layout.addWidget(self.color_preview)
        title_layout.addWidget(self.label)
        title_layout.addStretch()
        title_layout.addWidget(self.info_btn)
        title_container.setLayout(title_layout)
        layout.addWidget(title_container)
        
        # 创建属性编辑区域 - 使用更紧凑的布局
        props_layout = QGridLayout()
        
        # 第一行：颜色和透明度
        props_layout.addWidget(QLabel("颜色:"), 0, 0)
        self.color_button = ColorButton()
        self.color_button.colorChanged.connect(self.onColorChanged)
        props_layout.addWidget(self.color_button, 0, 1)
        
        props_layout.addWidget(QLabel("透明度:"), 0, 2)
        alpha_container = QWidget()
        alpha_layout = QHBoxLayout()
        alpha_layout.setContentsMargins(0, 0, 0, 0)
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setRange(0, 100)
        self.alpha_slider.setValue(100)
        self.alpha_slider.valueChanged.connect(self.onAlphaChanged)
        self.alpha_label = QLabel("1.0")
        self.alpha_label.setMinimumWidth(30)
        alpha_layout.addWidget(self.alpha_slider)
        alpha_layout.addWidget(self.alpha_label)
        alpha_container.setLayout(alpha_layout)
        props_layout.addWidget(alpha_container, 0, 3, 1, 2)
        
        # 第二行：线宽和线型
        props_layout.addWidget(QLabel("线宽:"), 1, 0)
        self.linewidth_spin = QDoubleSpinBox()
        self.linewidth_spin.setRange(0.1, 10.0)
        self.linewidth_spin.setSingleStep(0.1)
        self.linewidth_spin.setValue(1.0)
        self.linewidth_spin.setMaximumWidth(80)
        self.linewidth_spin.valueChanged.connect(self.onLinewidthChanged)
        props_layout.addWidget(self.linewidth_spin, 1, 1)
        
        props_layout.addWidget(QLabel("线型:"), 1, 2)
        self.linestyle_combo = QComboBox()
        self.linestyle_combo.addItems(['-', '--', '-.', ':', 'None'])
        self.linestyle_combo.currentTextChanged.connect(self.onLinestyleChanged)
        props_layout.addWidget(self.linestyle_combo, 1, 3)
        
        # 第三行：标记类型和大小
        props_layout.addWidget(QLabel("标记:"), 2, 0)
        self.marker_combo = QComboBox()
        markers = ['None', 'o', 's', '^', 'v', '<', '>', 'D', 'p', '*', 'h', 'H', '+', 'x', '|', '_']
        self.marker_combo.addItems(markers)
        self.marker_combo.currentTextChanged.connect(self.onMarkerChanged)
        props_layout.addWidget(self.marker_combo, 2, 1)
        
        props_layout.addWidget(QLabel("标记大小:"), 2, 2)
        self.markersize_spin = QDoubleSpinBox()
        self.markersize_spin.setRange(1.0, 20.0)
        self.markersize_spin.setValue(6.0)
        self.markersize_spin.setMaximumWidth(80)
        self.markersize_spin.valueChanged.connect(self.onMarkersizeChanged)
        props_layout.addWidget(self.markersize_spin, 2, 3)
        
        # 第四行：可见性和保存按钮
        self.visible_check = QCheckBox("显示")
        self.visible_check.setChecked(True)
        self.visible_check.toggled.connect(self.onVisibilityChanged)
        props_layout.addWidget(self.visible_check, 3, 0)
        
        self.save_style_btn = QPushButton("保存样式")
        self.save_style_btn.clicked.connect(self.saveCurrentStyle)
        self.save_style_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        props_layout.addWidget(self.save_style_btn, 3, 1, 1, 2)
        
        layout.addLayout(props_layout)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("QFrame { color: #cccccc; }")
        layout.addWidget(line)
        
        # 设置整体样式
        self.setLayout(layout)
        self.setStyleSheet("""
            LinePropertiesWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 3px;
                padding: 8px;
            }
            QLabel {
                background-color: transparent;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
            QComboBox, QDoubleSpinBox {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
            QComboBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
            QCheckBox {
                background-color: transparent;
                font-weight: bold;
                color: #2c3e50;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 6px;
                background: #ecf0f1;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #2980b9;
            }
        """)
    
    def loadLineProperties(self):
        """从线条对象加载属性"""
        # 颜色
        color = self.line.get_color()
        self.color_button.setColor(color)
        self.updateColorPreview()
        
        # 透明度
        alpha = self.line.get_alpha()
        if alpha is None:
            alpha = 1.0
        self.alpha_slider.setValue(int(alpha * 100))
        self.alpha_label.setText(f"{alpha:.2f}")
        
        # 线宽
        linewidth = self.line.get_linewidth()
        self.linewidth_spin.setValue(linewidth)
        
        # 线型
        linestyle = self.line.get_linestyle()
        # 修复findText的参数类型问题
        linestyle_str = str(linestyle) if linestyle is not None else '-'
        index = self.linestyle_combo.findText(linestyle_str)
        if index >= 0:
            self.linestyle_combo.setCurrentIndex(index)
        
        # 标记
        marker = self.line.get_marker()
        if marker == 'None' or marker is None:
            marker = 'None'
        # 修复findText的参数类型问题
        marker_str = str(marker) if marker is not None else 'None'
        index = self.marker_combo.findText(marker_str)
        if index >= 0:
            self.marker_combo.setCurrentIndex(index)
        
        # 标记大小
        markersize = self.line.get_markersize()
        self.markersize_spin.setValue(markersize)
        
        # 可见性
        visible = self.line.get_visible()
        self.visible_check.setChecked(visible)
    
    def updateColorPreview(self):
        """更新颜色预览"""
        try:
            color = self.line.get_color()
            rgb = mcolors.to_rgb(color)
            r, g, b = [int(c * 255) for c in rgb]
            self.color_preview.setStyleSheet(
                f"QLabel {{ background-color: rgb({r}, {g}, {b}); border: 1px solid black; }}")
        except:
            self.color_preview.setStyleSheet("QLabel { background-color: gray; border: 1px solid black; }")
    
    def onColorChanged(self, color):
        """颜色改变"""
        self.line.set_color(color)
        self.updateColorPreview()
        self.propertiesChanged.emit()
        # 立即更新图表显示
        if hasattr(self.line, 'axes') and self.line.axes and hasattr(self.line.axes, 'figure'):
            self.line.axes.figure.canvas.draw_idle()
    
    def onAlphaChanged(self, value):
        """透明度改变"""
        alpha = value / 100.0
        self.line.set_alpha(alpha)
        self.alpha_label.setText(f"{alpha:.2f}")
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onLinewidthChanged(self, value):
        """线宽改变"""
        self.line.set_linewidth(value)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onLinestyleChanged(self, style):
        """线型改变"""
        self.line.set_linestyle(style)
        self.propertiesChanged.emit()
        # 立即更新图表显示
    
        self._updateCanvas()
    
    def onMarkerChanged(self, marker):
        """标记改变"""
        if marker == 'None':
            marker = 'None'
        self.line.set_marker(marker)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onMarkersizeChanged(self, size):
        """标记大小改变"""
        self.line.set_markersize(size)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def onVisibilityChanged(self, visible):
        """可见性改变"""
        self.line.set_visible(visible)
        self.propertiesChanged.emit()
        # 立即更新图表显示
        self._updateCanvas()
    
    def _updateCanvas(self):
        """更新画布显示"""
        if hasattr(self.line, 'axes') and self.line.axes and hasattr(self.line.axes, 'figure'):
            self.line.axes.figure.canvas.draw_idle()
    
    def getCurrentStyle(self):
        """获取当前样式"""
        return {
            'color': self.line.get_color(),
            'linewidth': self.line.get_linewidth(),
            'linestyle': self.line.get_linestyle(),
            'marker': self.line.get_marker(),
            'markersize': self.line.get_markersize(),
            'alpha': self.line.get_alpha() if self.line.get_alpha() is not None else 1.0
        }
    
    def saveCurrentStyle(self):
        """保存当前样式"""
        algorithm_name = self.line.get_label()
        
        # 获取有效的算法名称
        effective_name = self._getEffectiveAlgorithmName(algorithm_name)
        
        if effective_name:
            style = self.getCurrentStyle()
            if self.config_manager:
                self.config_manager.save_algorithm_style(effective_name, style)
            self.styleChanged.emit(effective_name, style)
            QMessageBox.information(self, "成功", f"已保存算法 '{effective_name}' 的样式")
        else:
            # 如果没有有效名称，提供输入对话框让用户自定义
            from PyQt5.QtWidgets import QInputDialog
            custom_name, ok = QInputDialog.getText(
                self, 
                "输入算法名称", 
                "请为此算法输入一个名称:",
                text=f"Algorithm_{self.line_index + 1}"
            )
            
            if ok and custom_name.strip():
                effective_name = custom_name.strip()
                style = self.getCurrentStyle()
                if self.config_manager:
                    self.config_manager.save_algorithm_style(effective_name, style)
                self.styleChanged.emit(effective_name, style)
                QMessageBox.information(self, "成功", f"已保存算法 '{effective_name}' 的样式")
            else:
                QMessageBox.warning(self, "警告", "保存取消：未提供有效的算法名称")
    
    def _getEffectiveAlgorithmName(self, label):
        """获取有效的算法名称"""
        # 首先尝试从图例获取名称
        try:
            if hasattr(self.line, 'axes') and self.line.axes:
                ax = self.line.axes
                legend = ax.get_legend()
                if legend and len(legend.get_texts()) > self.line_index:
                    legend_text = legend.get_texts()[self.line_index].get_text()
                    if legend_text and not legend_text.startswith('_') and legend_text.strip():
                        return legend_text.strip()
        except:
            pass
        
        # 如果图例名称无效，检查线条标签
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 都无效则返回None
        return None
    
    def showAlgorithmInfo(self):
        """显示算法信息"""
        algorithm_name = self._getEffectiveAlgorithmName(self.line.get_label())
        if not algorithm_name:
            algorithm_name = f"Line {self.line_index + 1}"
        
        # 获取线条的详细信息
        info_text = f"""
算法名称: {algorithm_name}

当前属性:
• 颜色: {self.line.get_color()}
• 线宽: {self.line.get_linewidth()}
• 线型: {self.line.get_linestyle()}
• 标记: {self.line.get_marker()}
• 标记大小: {self.line.get_markersize()}
• 透明度: {self.line.get_alpha() if self.line.get_alpha() is not None else 1.0}
• 可见性: {'是' if self.line.get_visible() else '否'}

数据点数量: {len(self.line.get_xdata())}
X范围: {min(self.line.get_xdata()):.3f} - {max(self.line.get_xdata()):.3f}
Y范围: {min(self.line.get_ydata()):.3f} - {max(self.line.get_ydata()):.3f}
        """
        
        QMessageBox.information(self, f"算法信息 - {algorithm_name}", info_text.strip())


class PlotEditorGUI(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.figure = None
        self.canvas = None
        self.current_file = None
        self.line_widgets = []
        self.config_manager = ConfigManager()
        self.setupUI()
        self.setWindowTitle("matplotlib图表编辑器")
        self.resize(1400, 800)
        
        # 设置主窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #e7e7e7;
                border: 1px solid #adadad;
                border-radius: 3px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d4edda;
            }
            QPushButton:pressed {
                background-color: #c3e6cb;
            }
            QPushButton:disabled {
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)
    
    def setupUI(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        central_widget.setLayout(main_layout)
        
        # 创建主分割器 - 水平分割（左侧算法区域 | 右侧区域）
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setHandleWidth(8)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                border: 1px solid #999999;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bbbbbb;
            }
            QSplitter::handle:pressed {
                background-color: #aaaaaa;
            }
        """)
        main_layout.addWidget(main_splitter)
        
        # 左侧：算法选择和属性区域
        algorithm_panel = self.createAlgorithmPanel()
        main_splitter.addWidget(algorithm_panel)
        
        # 右侧：创建垂直分割器（图表区域 | 控制区域）
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setHandleWidth(8)
        right_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                border: 1px solid #999999;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bbbbbb;
            }
            QSplitter::handle:pressed {
                background-color: #aaaaaa;
            }
        """)
        main_splitter.addWidget(right_splitter)
        
        # 右上：图表显示区域
        plot_area = self.createPlotArea()
        right_splitter.addWidget(plot_area)
        
        # 右下：控制面板区域
        control_panel = self.createControlPanel()
        right_splitter.addWidget(control_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([350, 1150])  # 左侧算法区域 | 右侧区域
        right_splitter.setSizes([800, 300])  # 图表区域 | 控制区域
        
        # 创建菜单栏
        self.createMenuBar()
    
    def createMenuBar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        open_action = file_menu.addAction('打开 .pkl 文件')
        open_action.triggered.connect(self.openFile)
        
        save_action = file_menu.addAction('保存')
        save_action.triggered.connect(self.saveFile)
        
        save_as_action = file_menu.addAction('另存为')
        save_as_action.triggered.connect(self.saveAsFile)
        
        file_menu.addSeparator()
        
        export_png_action = file_menu.addAction('导出为PNG')
        export_png_action.triggered.connect(self.exportPNG)
        
        export_svg_action = file_menu.addAction('导出为SVG')
        export_svg_action.triggered.connect(self.exportSVG)
        
        file_menu.addSeparator()
        
        batch_action = file_menu.addAction('批处理')
        batch_action.triggered.connect(self.batchProcess)
    
    def createAlgorithmPanel(self):
        """创建算法选择和属性面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        panel.setLayout(layout)
        
        # 算法选择区域
        algorithm_group = QGroupBox("算法选择")
        algorithm_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        algorithm_layout = QVBoxLayout()
        
        # 算法选择按钮
        self.algorithm_menu_btn = QPushButton("选择算法")
        self.algorithm_menu_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # 创建算法选择菜单
        self.algorithm_menu = QMenu(self)
        self.algorithm_menu_btn.setMenu(self.algorithm_menu)
        
        # 当前选择的算法显示
        self.current_algorithm = "all"
        self.selected_algorithm_label = QLabel("当前: 所有算法")
        self.selected_algorithm_label.setStyleSheet("""
            QLabel { 
                color: #2c3e50; 
                font-weight: bold; 
                font-size: 12px;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
                border: 1px solid #bdc3c7;
            }
        """)
        
        algorithm_layout.addWidget(self.algorithm_menu_btn)
        algorithm_layout.addWidget(self.selected_algorithm_label)
        algorithm_group.setLayout(algorithm_layout)
        layout.addWidget(algorithm_group)
        
        # 算法属性区域
        properties_group = QGroupBox("算法属性")
        properties_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        properties_layout = QVBoxLayout()
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(200)  # 设置最小高度，确保有足够空间显示算法属性
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
        
        scroll_widget = QWidget()
        scroll_widget.setStyleSheet("QWidget { background-color: transparent; }")
        self.properties_layout = QVBoxLayout()
        self.properties_layout.setSpacing(8)
        scroll_widget.setLayout(self.properties_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        properties_layout.addWidget(scroll_area)
        properties_group.setLayout(properties_layout)
        layout.addWidget(properties_group)
        
        return panel
    
    def createControlPanel(self):
        """创建控制面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:horizontal, QScrollBar:vertical {
                background-color: #ecf0f1;
                border-radius: 6px;
            }
            QScrollBar:horizontal {
                height: 12px;
            }
            QScrollBar:vertical {
                width: 12px;
            }
            QScrollBar::handle:horizontal, QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover, QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
        
        scroll_widget = QWidget()
        layout = QHBoxLayout()  # 使用水平布局来容纳多个区域
        layout.setContentsMargins(10, 10, 10, 10)
        scroll_widget.setLayout(layout)
        
        # 文件操作区域
        file_group = QGroupBox("文件操作")
        file_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        file_layout = QVBoxLayout()
        
        self.open_button = QPushButton("打开 .pkl 文件")
        self.open_button.clicked.connect(self.openFile)
        file_layout.addWidget(self.open_button)
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.saveFile)
        self.save_button.setEnabled(False)
        file_layout.addWidget(self.save_button)
        
        self.export_button = QPushButton("导出PNG")
        self.export_button.clicked.connect(self.exportPNG)
        self.export_button.setEnabled(False)
        file_layout.addWidget(self.export_button)
        
        self.batch_button = QPushButton("批处理")
        self.batch_button.clicked.connect(self.batchProcess)
        file_layout.addWidget(self.batch_button)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 图表属性区域
        chart_group = QGroupBox("图表属性")
        chart_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        chart_layout = QGridLayout()
        
        # 标题编辑
        chart_layout.addWidget(QLabel("标题:"), 0, 0)
        self.title_edit = QLineEdit()
        self.title_edit.textChanged.connect(self.onTitleChanged)
        chart_layout.addWidget(self.title_edit, 0, 1)
        
        # X轴标签
        chart_layout.addWidget(QLabel("X轴标签:"), 1, 0)
        self.xlabel_edit = QLineEdit()
        self.xlabel_edit.textChanged.connect(self.onXLabelChanged)
        chart_layout.addWidget(self.xlabel_edit, 1, 1)
        
        # Y轴标签
        chart_layout.addWidget(QLabel("Y轴标签:"), 2, 0)
        self.ylabel_edit = QLineEdit()
        self.ylabel_edit.textChanged.connect(self.onYLabelChanged)
        chart_layout.addWidget(self.ylabel_edit, 2, 1)
        
        # 网格显示
        self.grid_check = QCheckBox("显示网格")
        self.grid_check.toggled.connect(self.onGridToggled)
        chart_layout.addWidget(self.grid_check, 3, 0, 1, 2)
        
        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)
        
        # 坐标轴设置区域
        axis_group = QGroupBox("坐标轴设置")
        axis_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """)
        axis_layout = QGridLayout()
        
        # X轴刻度类型
        axis_layout.addWidget(QLabel("X轴刻度:"), 0, 0)
        self.xscale_combo = QComboBox()
        self.xscale_combo.addItems(["linear", "log"])
        self.xscale_combo.currentTextChanged.connect(self.onXScaleChanged)
        axis_layout.addWidget(self.xscale_combo, 0, 1)
        
        # Y轴刻度类型
        axis_layout.addWidget(QLabel("Y轴刻度:"), 1, 0)
        self.yscale_combo = QComboBox()
        self.yscale_combo.addItems(["linear", "log"])
        self.yscale_combo.currentTextChanged.connect(self.onYScaleChanged)
        axis_layout.addWidget(self.yscale_combo, 1, 1)
        
        # X轴范围
        axis_layout.addWidget(QLabel("X轴范围:"), 2, 0)
        x_range_container = QWidget()
        x_range_layout = QHBoxLayout()
        x_range_layout.setContentsMargins(0, 0, 0, 0)
        self.xmin_edit = QLineEdit()
        self.xmin_edit.setPlaceholderText("最小值")
        self.xmin_edit.textChanged.connect(self.onXRangeChanged)
        self.xmax_edit = QLineEdit()
        self.xmax_edit.setPlaceholderText("最大值")
        self.xmax_edit.textChanged.connect(self.onXRangeChanged)
        x_range_layout.addWidget(self.xmin_edit)
        x_range_layout.addWidget(QLabel("-"))
        x_range_layout.addWidget(self.xmax_edit)
        x_range_container.setLayout(x_range_layout)
        axis_layout.addWidget(x_range_container, 2, 1)
        
        # Y轴范围
        axis_layout.addWidget(QLabel("Y轴范围:"), 3, 0)
        y_range_container = QWidget()
        y_range_layout = QHBoxLayout()
        y_range_layout.setContentsMargins(0, 0, 0, 0)
        self.ymin_edit = QLineEdit()
        self.ymin_edit.setPlaceholderText("最小值")
        self.ymin_edit.textChanged.connect(self.onYRangeChanged)
        self.ymax_edit = QLineEdit()
        self.ymax_edit.setPlaceholderText("最大值")
        self.ymax_edit.textChanged.connect(self.onYRangeChanged)
        y_range_layout.addWidget(self.ymin_edit)
        y_range_layout.addWidget(QLabel("-"))
        y_range_layout.addWidget(self.ymax_edit)
        y_range_container.setLayout(y_range_layout)
        axis_layout.addWidget(y_range_container, 3, 1)
        
        # 自动范围按钮
        self.auto_range_btn = QPushButton("自动范围")
        self.auto_range_btn.clicked.connect(self.onAutoRange)
        axis_layout.addWidget(self.auto_range_btn, 4, 0, 1, 2)
        
        axis_group.setLayout(axis_layout)
        layout.addWidget(axis_group)
        
        # 图例控制区域
        legend_group = QGroupBox("图例设置")
        legend_layout = QGridLayout()
        
        # 图例显示开关
        self.legend_check = QCheckBox("显示图例")
        self.legend_check.toggled.connect(self.onLegendToggled)
        legend_layout.addWidget(self.legend_check, 0, 0, 1, 2)
        
        # 图例位置
        legend_layout.addWidget(QLabel("图例位置:"), 1, 0)
        self.legend_loc_combo = QComboBox()
        self.legend_loc_combo.addItems([
            "best", "upper right", "upper left", "lower left", "lower right",
            "right", "center left", "center right", "lower center", "upper center", "center",
            "outside right", "outside left", "outside top", "outside bottom"
        ])
        self.legend_loc_combo.currentTextChanged.connect(self.onLegendLocationChanged)
        legend_layout.addWidget(self.legend_loc_combo, 1, 1)
        
        # 图例字体大小
        legend_layout.addWidget(QLabel("字体大小:"), 2, 0)
        self.legend_fontsize_spin = QDoubleSpinBox()
        self.legend_fontsize_spin.setRange(6, 20)
        self.legend_fontsize_spin.setValue(10)
        self.legend_fontsize_spin.setSuffix(" pt")
        self.legend_fontsize_spin.valueChanged.connect(self.onLegendFontsizeChanged)
        legend_layout.addWidget(self.legend_fontsize_spin, 2, 1)
        
        # 图例透明度
        legend_layout.addWidget(QLabel("透明度:"), 3, 0)
        self.legend_alpha_slider = QSlider(Qt.Horizontal)
        self.legend_alpha_slider.setRange(0, 100)
        self.legend_alpha_slider.setValue(80)
        self.legend_alpha_slider.valueChanged.connect(self.onLegendAlphaChanged)
        legend_layout.addWidget(self.legend_alpha_slider, 3, 1)
        
        legend_group.setLayout(legend_layout)
        layout.addWidget(legend_group)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        panel_layout = QVBoxLayout()
        panel_layout.setContentsMargins(0, 0, 0, 0)
        panel.setLayout(panel_layout)
        panel_layout.addWidget(scroll_area)
        
        return panel
    
    def createPlotArea(self):
        """创建图表显示区域"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
        """)
        layout = QVBoxLayout()
        layout.setContentsMargins(25, 15, 15, 15)  # 左侧增加更多边距确保Y轴标签可见
        widget.setLayout(layout)
        
        # 图表画布将在加载文件时创建
        self.plot_layout = layout
        
        return widget
    
    def openFile(self):
        """打开.pkl文件"""
        last_dir = self.config_manager.get_last_directory()
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开matplotlib图表文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
        
        if file_path:
            try:
                with open(file_path, 'rb') as f:
                    self.figure = pickle.load(f)
                
                self.current_file = file_path
                # 保存目录位置
                self.config_manager.set_last_directory(str(Path(file_path).parent))
                
                self.setupPlotCanvas()
                self.loadChartProperties()
                self.loadLineProperties()
                self.updateUI()
                
                QMessageBox.information(self, "成功", f"成功加载文件: {Path(file_path).name}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法加载文件: {str(e)}")
    
    def setupPlotCanvas(self):
        """设置图表画布"""
        # 清除旧的画布
        for i in reversed(range(self.plot_layout.count())):
            self.plot_layout.itemAt(i).widget().setParent(None)
        
        # 调整图表边距确保标签完全可见
        if self.figure and len(self.figure.axes) > 0:
            self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.9)
        
        # 自动应用保存的样式
        self.applyStoredStyles()
        
        # 创建新的画布
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        self.plot_layout.addWidget(self.toolbar)
        self.plot_layout.addWidget(self.canvas)
        
        self.canvas.draw()
    
    def applyStoredStyles(self):
        """自动应用保存的样式"""
        if not self.figure or len(self.figure.axes) == 0:
            return
        
        ax = self.figure.axes[0]
        lines = ax.lines
        
        # 获取所有保存的样式
        stored_styles = self.config_manager.get_all_algorithm_styles()
        
        if not stored_styles:
            return
        
        applied_count = 0
        
        for line in lines:
            # 获取线条的有效算法名称
            algorithm_name = self._getEffectiveAlgorithmNameForLine(line)
            
            if algorithm_name and algorithm_name in stored_styles:
                style = stored_styles[algorithm_name]
                
                # 应用样式
                try:
                    if 'color' in style:
                        line.set_color(style['color'])
                    if 'linewidth' in style:
                        line.set_linewidth(style['linewidth'])
                    if 'linestyle' in style:
                        line.set_linestyle(style['linestyle'])
                    if 'marker' in style:
                        line.set_marker(style['marker'])
                    if 'markersize' in style:
                        line.set_markersize(style['markersize'])
                    if 'alpha' in style:
                        line.set_alpha(style['alpha'])
                    
                    applied_count += 1
                except Exception as e:
                    print(f"应用样式失败 {algorithm_name}: {str(e)}")
        
        if applied_count > 0:
            print(f"自动应用了 {applied_count} 个算法的保存样式")
    
    def _getEffectiveAlgorithmNameForLine(self, line):
        """为线条获取有效的算法名称"""
        # 首先尝试从线条标签获取
        label = line.get_label()
        if label and not label.startswith('_') and label.strip():
            return label.strip()
        
        # 如果标签无效，尝试从图例获取
        try:
            if hasattr(line, 'axes') and line.axes:
                ax = line.axes
                legend = ax.get_legend()
                if legend:
                    # 找到这条线在lines列表中的索引
                    lines = ax.lines
                    line_index = -1
                    for i, l in enumerate(lines):
                        if l is line:
                            line_index = i
                            break
                    
                    # 获取对应的图例文本
                    if line_index >= 0 and len(legend.get_texts()) > line_index:
                        legend_text = legend.get_texts()[line_index].get_text()
                        if legend_text and not legend_text.startswith('_') and legend_text.strip():
                            return legend_text.strip()
        except:
            pass
        
        return None
    
    def loadChartProperties(self):
        """加载图表属性"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            
            # 标题
            title = ax.get_title()
            self.title_edit.setText(title)
            
            # 轴标签
            xlabel = ax.get_xlabel()
            ylabel = ax.get_ylabel()
            self.xlabel_edit.setText(xlabel)
            self.ylabel_edit.setText(ylabel)
            
            # 网格
            grid_visible = ax.grid
            self.grid_check.setChecked(bool(grid_visible))
            
            # 坐标轴刻度类型
            xscale = ax.get_xscale()
            yscale = ax.get_yscale()
            
            xscale_index = self.xscale_combo.findText(xscale)
            if xscale_index >= 0:
                self.xscale_combo.setCurrentIndex(xscale_index)
            
            yscale_index = self.yscale_combo.findText(yscale)
            if yscale_index >= 0:
                self.yscale_combo.setCurrentIndex(yscale_index)
            
            # 坐标轴范围
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            self.xmin_edit.setText(f"{xlim[0]:.3f}")
            self.xmax_edit.setText(f"{xlim[1]:.3f}")
            self.ymin_edit.setText(f"{ylim[0]:.3f}")
            self.ymax_edit.setText(f"{ylim[1]:.3f}")
            
            # 图例设置
            legend = ax.get_legend()
            if legend:
                self.legend_check.setChecked(True)
                
                # 获取图例位置 - 检查是否为外部位置
                bbox = legend.get_bbox_to_anchor()
                loc = legend._loc
                
                # 判断是否为外部位置
                if bbox is not None:
                    x, y = bbox.x0, bbox.y0
                    if x > 1.0:  # 右侧外部
                        self.legend_loc_combo.setCurrentText("outside right")
                    elif x < 0.0:  # 左侧外部
                        self.legend_loc_combo.setCurrentText("outside left")
                    elif y > 1.0:  # 顶部外部
                        self.legend_loc_combo.setCurrentText("outside top")
                    elif y < 0.0:  # 底部外部
                        self.legend_loc_combo.setCurrentText("outside bottom")
                    else:
                        # 标准位置
                        if isinstance(loc, str):
                            loc_index = self.legend_loc_combo.findText(loc)
                            if loc_index >= 0:
                                self.legend_loc_combo.setCurrentIndex(loc_index)
                else:
                    # 标准位置
                    if isinstance(loc, str):
                        loc_index = self.legend_loc_combo.findText(loc)
                        if loc_index >= 0:
                            self.legend_loc_combo.setCurrentIndex(loc_index)
                
                # 获取图例字体大小
                if legend.get_texts():
                    fontsize = legend.get_texts()[0].get_fontsize()
                    self.legend_fontsize_spin.setValue(fontsize)
                
                # 获取图例透明度
                alpha = legend.get_frame().get_alpha()
                if alpha is not None:
                    self.legend_alpha_slider.setValue(int(alpha * 100))
            else:
                self.legend_check.setChecked(False)
            
            # 加载算法列表
            self.loadAlgorithmList()
            
            # 加载线条属性控件
            self.loadLineProperties()
    
    def loadAlgorithmList(self):
        """加载算法列表到弹出菜单"""
        self.algorithm_menu.clear()
        
        # 添加"所有算法"选项
        all_action = self.algorithm_menu.addAction("所有算法")
        all_action.triggered.connect(lambda: self.selectAlgorithm("all", "所有算法"))
        
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            
            # 从图例中获取算法名称
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    algorithm_name = text.get_text()
                    if algorithm_name and not algorithm_name.startswith('_'):
                        action = self.algorithm_menu.addAction(algorithm_name)
                        action.triggered.connect(lambda checked, name=algorithm_name: self.selectAlgorithm(name, name))
            else:
                # 如果没有图例，从线条标签获取
                for line in lines:
                    label = line.get_label()
                    if label and not label.startswith('_'):
                        action = self.algorithm_menu.addAction(label)
                        action.triggered.connect(lambda checked, name=label: self.selectAlgorithm(name, name))
    
    def selectAlgorithm(self, algorithm_id, algorithm_name):
        """选择算法"""
        self.current_algorithm = algorithm_id
        self.selected_algorithm_label.setText(f"当前: {algorithm_name}")
        
        # 如果已经有控件，只需要更新可见性，不需要重新创建
        if hasattr(self, 'all_line_widgets') and self.all_line_widgets:
            self.updateWidgetVisibility()
            self.line_widgets = [w for w in self.all_line_widgets if w.isVisible()]
        else:
            # 如果没有控件，则创建新的
            self.loadLineProperties()
    
    def loadLineProperties(self):
        """加载线条属性"""
        # 清除所有旧的线条控件
        self.clearLineWidgets()
        
        # 初始化控件列表
        if not hasattr(self, 'all_line_widgets'):
            self.all_line_widgets = []
        
        # 创建新的控件
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            
            for i, line in enumerate(lines):
                line_widget = LinePropertiesWidget(line, i, self.config_manager)
                line_widget.propertiesChanged.connect(self.onLinePropertiesChanged)
                line_widget.styleChanged.connect(self.onStyleChanged)
                self.properties_layout.addWidget(line_widget)
                self.all_line_widgets.append(line_widget)
                
                # 加载线条属性到控件中，确保显示当前的样式
                line_widget.loadLineProperties()
        
        # 根据当前选择的算法显示/隐藏控件
        self.updateWidgetVisibility()
        
        # 更新line_widgets列表以保持兼容性
        self.line_widgets = [w for w in self.all_line_widgets if w.isVisible()]
    
    def clearLineWidgets(self):
        """清除所有线条控件"""
        if hasattr(self, 'all_line_widgets'):
            for widget in self.all_line_widgets:
                # 断开信号连接
                try:
                    widget.propertiesChanged.disconnect()
                    widget.styleChanged.disconnect()
                except:
                    pass
                # 从布局中移除并删除控件
                self.properties_layout.removeWidget(widget)
                widget.setParent(None)
                widget.deleteLater()
            self.all_line_widgets.clear()
        
        if hasattr(self, 'line_widgets'):
            self.line_widgets.clear()
    
    def updateWidgetVisibility(self):
        """更新控件可见性"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            lines = ax.lines
            selected_algorithm = self.current_algorithm
            
            for i, widget in enumerate(self.all_line_widgets):
                if i < len(lines):
                    line = lines[i]
                    line_label = line.get_label()
                    
                    # 决定是否显示该控件
                    if selected_algorithm == "all":
                        widget.show()
                    else:
                        if line_label == selected_algorithm:
                            widget.show()
                        else:
                            widget.hide()
    
    def updateUI(self):
        """更新UI状态"""
        has_figure = self.figure is not None
        self.save_button.setEnabled(has_figure)
        self.export_button.setEnabled(has_figure)
    
    def onTitleChanged(self, text):
        """标题改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_title(text)
            self.canvas.draw_idle()
    
    def onXLabelChanged(self, text):
        """X轴标签改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_xlabel(text)
            self.canvas.draw_idle()
    
    def onYLabelChanged(self, text):
        """Y轴标签改变"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].set_ylabel(text)
            self.canvas.draw_idle()
    
    def onGridToggled(self, checked):
        """网格切换"""
        if self.figure and len(self.figure.axes) > 0:
            self.figure.axes[0].grid(checked)
            self.canvas.draw_idle()
    
    def onLegendToggled(self, checked):
        """图例显示切换"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            if checked:
                ax.legend()
            else:
                legend = ax.get_legend()
                if legend:
                    legend.remove()
            self.canvas.draw_idle()
    
    def onLegendLocationChanged(self, location):
        """图例位置改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                # 处理外部位置
                if location.startswith("outside"):
                    if location == "outside right":
                        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
                    elif location == "outside left":
                        ax.legend(bbox_to_anchor=(-0.05, 1), loc='upper right')
                    elif location == "outside top":
                        ax.legend(bbox_to_anchor=(0.5, 1.05), loc='lower center')
                    elif location == "outside bottom":
                        ax.legend(bbox_to_anchor=(0.5, -0.05), loc='upper center')
                    
                    # 调整图表布局以适应外部图例
                    if location == "outside right":
                        self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.8, top=0.9)
                    elif location == "outside left":
                        self.figure.subplots_adjust(left=0.25, bottom=0.1, right=0.95, top=0.9)
                    elif location == "outside top":
                        self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.85)
                    elif location == "outside bottom":
                        self.figure.subplots_adjust(left=0.15, bottom=0.2, right=0.95, top=0.9)
                else:
                    # 标准位置
                    ax.legend(loc=location)
                    # 恢复默认布局
                    self.figure.subplots_adjust(left=0.15, bottom=0.1, right=0.95, top=0.9)
                
                self.canvas.draw_idle()
    
    def onLegendFontsizeChanged(self, fontsize):
        """图例字体大小改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    text.set_fontsize(fontsize)
                self.canvas.draw_idle()
    
    def onLegendAlphaChanged(self, alpha_percent):
        """图例透明度改变"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            if legend:
                alpha = alpha_percent / 100.0
                legend.get_frame().set_alpha(alpha)
                self.canvas.draw_idle()
    
    def onXScaleChanged(self, scale):
        """X轴刻度类型改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                self.figure.axes[0].set_xscale(scale)
                self.canvas.draw_idle()
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法设置X轴刻度为{scale}: {str(e)}")
    
    def onYScaleChanged(self, scale):
        """Y轴刻度类型改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                self.figure.axes[0].set_yscale(scale)
                self.canvas.draw_idle()
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法设置Y轴刻度为{scale}: {str(e)}")
    
    def onXRangeChanged(self):
        """X轴范围改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                xmin_text = self.xmin_edit.text().strip()
                xmax_text = self.xmax_edit.text().strip()
                
                if xmin_text and xmax_text:
                    xmin = float(xmin_text)
                    xmax = float(xmax_text)
                    if xmin < xmax:
                        self.figure.axes[0].set_xlim(xmin, xmax)
                        self.canvas.draw_idle()
            except ValueError:
                pass  # 忽略无效输入
    
    def onYRangeChanged(self):
        """Y轴范围改变"""
        if self.figure and len(self.figure.axes) > 0:
            try:
                ymin_text = self.ymin_edit.text().strip()
                ymax_text = self.ymax_edit.text().strip()
                
                if ymin_text and ymax_text:
                    ymin = float(ymin_text)
                    ymax = float(ymax_text)
                    if ymin < ymax:
                        self.figure.axes[0].set_ylim(ymin, ymax)
                        self.canvas.draw_idle()
            except ValueError:
                pass  # 忽略无效输入
    
    def onAutoRange(self):
        """自动设置坐标轴范围"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            ax.relim()
            ax.autoscale()
            
            # 更新范围输入框
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            self.xmin_edit.setText(f"{xlim[0]:.3f}")
            self.xmax_edit.setText(f"{xlim[1]:.3f}")
            self.ymin_edit.setText(f"{ylim[0]:.3f}")
            self.ymax_edit.setText(f"{ylim[1]:.3f}")
            
            self.canvas.draw_idle()
    
    def onLinePropertiesChanged(self):
        """线条属性改变"""
        if self.canvas:
            # 更新图例样式
            self.updateLegendStyles()
            self.canvas.draw_idle()
    
    def updateLegendStyles(self):
        """更新图例样式以匹配线条样式"""
        if self.figure and len(self.figure.axes) > 0:
            ax = self.figure.axes[0]
            legend = ax.get_legend()
            
            if legend:
                # 获取图例中的线条对象
                legend_lines = legend.get_lines()
                plot_lines = ax.lines
                
                # 确保图例线条数量与绘图线条数量匹配
                for i, (legend_line, plot_line) in enumerate(zip(legend_lines, plot_lines)):
                    # 同步样式
                    legend_line.set_color(plot_line.get_color())
                    legend_line.set_linewidth(plot_line.get_linewidth())
                    legend_line.set_linestyle(plot_line.get_linestyle())
                    legend_line.set_marker(plot_line.get_marker())
                    legend_line.set_markersize(plot_line.get_markersize())
                    legend_line.set_alpha(plot_line.get_alpha())
    
    def saveFile(self):
        """保存文件"""
        if self.current_file and self.figure:
            try:
                with open(self.current_file, 'wb') as f:
                    pickle.dump(self.figure, f)
                QMessageBox.information(self, "成功", "文件保存成功!")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def saveAsFile(self):
        """另存为"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存图表文件", last_dir, "Pickle Files (*.pkl);;All Files (*)")
            
            if file_path:
                try:
                    with open(file_path, 'wb') as f:
                        pickle.dump(self.figure, f)
                    self.current_file = file_path
                    # 保存目录位置
                    self.config_manager.set_last_directory(str(Path(file_path).parent))
                    QMessageBox.information(self, "成功", "文件保存成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    

    
    def exportSVG(self):
        """导出为SVG"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出SVG图片", last_dir, "SVG Files (*.svg);;All Files (*)")
            
            if file_path:
                try:
                    self.figure.savefig(file_path, format='svg', bbox_inches='tight')
                    QMessageBox.information(self, "成功", "SVG导出成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def onStyleChanged(self, algorithm_name, style_dict):
        """样式改变回调"""
        print(f"算法 {algorithm_name} 的样式已保存: {style_dict}")
    
    def batchProcess(self):
        """批处理"""
        algorithm_styles = self.config_manager.get_all_algorithm_styles()
        
        if not algorithm_styles:
            QMessageBox.warning(self, "警告", "没有保存的算法样式。请先编辑图表并保存算法样式。")
            return
        
        # 打开批处理对话框
        dialog = BatchProcessDialog(algorithm_styles, self)
        if dialog.exec_() == QDialog.Accepted:
            files = dialog.getSelectedFiles()
            export_png = dialog.shouldExportPNG()
            
            if not files:
                QMessageBox.warning(self, "警告", "没有选择要处理的文件。")
                return
            
            # 创建进度对话框
            progress_dialog = QProgressDialog("正在处理文件...", "取消", 0, 100, self)
            progress_dialog.setWindowTitle("批处理进度")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.show()
            
            # 创建批处理线程
            self.batch_thread = BatchProcessThread(files, algorithm_styles, export_png)
            self.batch_thread.progress.connect(progress_dialog.setValue)
            self.batch_thread.status.connect(lambda msg: progress_dialog.setLabelText(msg))
            self.batch_thread.finished_signal.connect(
                lambda success, total: self.onBatchProcessFinished(success, total, progress_dialog))
            
            # 连接取消按钮
            progress_dialog.canceled.connect(self.batch_thread.terminate)
            
            # 开始处理
            self.batch_thread.start()
    
    def onBatchProcessFinished(self, success_count, total_count, progress_dialog):
        """批处理完成"""
        progress_dialog.close()
        
        if success_count == total_count:
            QMessageBox.information(self, "成功", 
                f"批处理完成！成功处理了 {success_count} 个文件。")
        else:
            QMessageBox.warning(self, "部分成功", 
                f"批处理完成！成功处理了 {success_count}/{total_count} 个文件。")
    
    def exportPNG(self):
        """导出为PNG"""
        if self.figure:
            last_dir = self.config_manager.get_last_directory()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PNG图片", last_dir, "PNG Files (*.png);;All Files (*)")
            
            if file_path:
                try:
                    self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                    QMessageBox.information(self, "成功", "PNG导出成功!")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("matplotlib图表编辑器")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = PlotEditorGUI()
    window.show()
    
    # 如果命令行提供了文件路径，自动打开
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if Path(file_path).exists() and file_path.endswith('.pkl'):
            try:
                with open(file_path, 'rb') as f:
                    window.figure = pickle.load(f)
                window.current_file = file_path
                window.setupPlotCanvas()
                window.loadChartProperties()
                window.loadLineProperties()
                window.updateUI()
                print(f"自动加载文件: {file_path}")
            except Exception as e:
                print(f"无法加载文件 {file_path}: {e}")
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 