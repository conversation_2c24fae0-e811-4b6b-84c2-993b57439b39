---
type: "always_apply"
---



@index.html 你需要阅读并参考 "历史日志文档"，然后用相同的格式来更新整理最新的日志文档

# 日志文件更新：
1、需要更新的文件：
- 今日正在更新日志文档： @PoSDK 项目更新日志 (2025-7-21).md
- 日志文档管理： @index.rst   

2、参考文件：
- 历史日志文档：
  - @PoSDK 项目更新日志 (2025-7-9).md
  - @PoSDK 项目更新日志 (2025-7-8).md
  - @PoSDK 项目更新日志 (2025-7-4).md
  - @PoSDK 项目更新日志 (2025-7-3).md
  - @PoSDK 项目更新日志 (2025-7-2).md
  - @PoSDK 项目更新日志 (2025-6-27).md
  - @PoSDK 项目更新日志 (2025-6-26).md
  - @PoSDK 项目更新日志 (2025-6-25).md
  - @PoSDK 项目更新日志 (2025-6-22).md 
  - @PoSDK 项目更新日志 (2025-6-19).md 
  - @PoSDK 项目更新日志 (2025-6-15).md
  - @PoSDK 项目更新日志 (2025-6-14).md
  - @PoSDK 项目更新日志 (2025-6-12).md
  - @PoSDK 项目更新日志 (2025-6-9).md
  - @PoSDK 项目更新日志 (2025-6-7).md
  - @PoSDK 项目更新日志 (2025-6-6).md
  - @PoSDK 项目更新日志 (2025-6-5).md
  - @PoSDK 项目更新日志 (2025-5-30).md
  - @PoSDK 项目更新日志 (2025-5-22).md
  - @PoSDK 项目更新日志 (2025-5-17).md



