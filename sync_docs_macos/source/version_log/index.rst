项目更新日志
=============

PoSDK 项目更新日志记录了项目开发过程中的重要功能更新、bug修复和文档改进。
每个日志文件按日期组织，详细记录了相应时期的开发成果和技术改进。

最新更新
--------

* :doc:`PoSDK 项目更新日志 (2025-7-21)` - PoMVG两视图估计器统一精细优化控制系统实现、成功率统计功能添加、算法预热机制与时间统计优化

历史更新记录
--------------

按时间倒序排列：

.. toctree::
   :maxdepth: 2
   :caption: 更新日志目录

   PoSDK 项目更新日志 (2025-7-21)
   PoSDK 项目更新日志 (2025-7-10)
   PoSDK 项目更新日志 (2025-7-9)
   PoSDK 项目更新日志 (2025-7-8)
   PoSDK 项目更新日志 (2025-7-6)
   PoSDK 项目更新日志 (2025-7-3)
   PoSDK 项目更新日志 (2025-7-2)
   PoSDK 项目更新日志 (2025-6-27)
   PoSDK 项目更新日志 (2025-6-26)
   PoSDK 项目更新日志 (2025-6-25)
   PoSDK 项目更新日志 (2025-6-24)
   PoSDK 项目更新日志 (2025-6-23)
   PoSDK 项目更新日志 (2025-6-22)
   PoSDK 项目更新日志 (2025-6-19)
   PoSDK 项目更新日志 (2025-6-15)
   PoSDK 项目更新日志 (2025-6-14)
   PoSDK 项目更新日志 (2025-6-12)
   PoSDK 项目更新日志 (2025-6-9)
   PoSDK 项目更新日志 (2025-6-7)
   PoSDK 项目更新日志 (2025-6-6)
   PoSDK 项目更新日志 (2025-6-5)
   PoSDK 项目更新日志 (2025-5-30)
   PoSDK 项目更新日志 (2025-5-22)
   PoSDK 项目更新日志 (2025-5-19)
   PoSDK 项目更新日志 (2025-5-17)
   

重要里程碑
----------

* **2025-7-21**: 统一精细优化控制系统实现，成功率统计功能添加，评估系统能力增强
* **2025-7-10**: 算法调试和分析能力全面增强，特征值分析和精度统计功能完成
* **2025-7-4**: TwoViewOptimizer模块重大重构完成，实现统一残差管理和数值稳定优化
* **2025-?-?**: 正式发布v1.0.0.0版本

版本对应关系
--------------

各更新日志对应的版本信息：

.. list-table:: 
   :header-rows: 1
   :widths: 30 20 50

   * - 更新日期
     - 版本号
     - 主要内容
   * - 2025-7-21
     - v0.15.3.0
     - PoMVG两视图估计器统一精细优化控制系统实现、成功率统计功能添加、评估系统能力增强
   * - 2025-7-10
     - v0.15.2.0
     - PoVG SixPoint算法inliers处理优化、method_LiRPFast特征值分析和精度统计功能实现
   * - 2025-7-9
     - v0.15.1.1
     - nullspace计算方法性能测试框架、method_LiRPFast编译错误修复、ATA构建优化
   * - 2025-7-8
     - v0.15.1.0
     - RT_Check算法性能测试框架增强、use_opt_mode性能对比测试、TwoViewEstimator PoSDK精细优化功能
   * - 2025-7-7
     - v0.15.0.1
     - 双视图估计器噪声鲁棒性测试、测试框架优化、文档修复
   * - 2025-7-4
     - v0.15.0.0
     - TwoViewOptimizer重大重构、残差函数管理系统、迭代显示控制、球坐标系参数化、Bundle Adjustment完善
   * - 2025-7-3
     - v0.14.7.0
     - TwoViewOptimizer架构重构、优化器精简、编译错误修复
   * - 2025-7-2
     - v0.14.6.1
     - 日志文档索引更新、版本管理优化
   * - 2025-6-27
     - v0.14.6.0
     - TwoViewOptimizer Ceres优化器实现、残差函数扩展统一、算法性能提升
   * - 2025-6-26
     - v0.14.5.0
     - OpenGVSimulator数据接口修复、TwoViewOptimizer显示优化、残差函数修复
   * - 2025-6-25
     - v0.14.4.0
     - PassingMethodOptions优先级优化、TwoViewEstimator质量验证统一、Bundle Adjustment配置增强
   * - 2025-6-24
     - v0.14.3.0
     - DisplayConfigInfo界面优化、DataSample智能采样机制、MethodRobustLiRP两阶段优化
   * - 2025-6-23
     - v0.14.2.0
     - MethodPreset参数传递机制优化、架构增强、文档修复
   * - 2025-6-22
     - v0.14.1.0
     - MAGSAC算法集成完善、测试系统扩展
   * - 2025-6-19
     - v0.14.0.0
     - 第三方鲁棒估计库集成、双视图估计器扩展
   * - 2025-6-15
     - v1.8.0
     - OpenGV双视图仿真器系统、多接口数据生成
   * - 2025-6-14
     - v0.13.4.0
     - 坐标转换器、图表编辑器、文档修复
   * - 2025-6-12
     - v0.13.3.0
     - OpenCV双视图估计器插件、算法扩展、数据转换器完善
   * - 2025-6-9
     - v0.13.2.0
     - 依赖管理系统标准化、文档修复、一键安装脚本
   * - 2025-6-7
     - v0.13.1.0
     - Strecha测试代码检测、双视图RobustLiRP功能第三次检测与修复
   * - 2025-6-6
     - v0.13.0.0
     - 双视图匹配可视化插件、文档修复
   * - 2025-6-5
     - v0.12.0.0
     - 评估器系统、全局异常点检测拓展、文档修复
   * - 2025-5-30
     - v0.11.0.0
     - 评估器系统、异常检测拓展、文档修复
   * - 2025-5-22
     - v0.10.2.0
     - 位姿评估增强、轨迹归一化、测试完善
   * - 2025-5-19
     - v0.10.1.0
     - 算法优化、测试框架扩展
   * - 2025-5-17
     - v0.10.0
     - 基础功能实现、架构建立

查看方式
--------

您可以通过以下方式查看项目更新：

1. **按日期浏览**: 点击上方的日期链接查看具体更新内容
2. **重要功能查找**: 通过搜索功能查找特定功能的更新记录
3. **版本对比**: 对比不同版本间的功能差异和改进

